/**********************************************************************************************************************
 * D<PERSON><PERSON><PERSON>IMER
 * This software is supplied by Renesas Electronics Corporation and is only intended for use with Renesas products. No
 * other uses are authorized. This software is owned by Renesas Electronics Corporation and is protected under all
 * applicable laws, including copyright laws.
 * THIS SOFTWARE IS PROVIDED "AS IS" AND RENESAS MAKES NO WARRANTIES REGARDING
 * THIS SOFTWARE, WHETHER EXPRESS, IMPLIED OR STATUTORY, INCLUDING BUT NOT LIMITED TO WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. ALL SUCH WARRANTIES ARE EXPRESSLY DISCLAIMED. TO THE MAXIMUM
 * EXTENT PERMITTED NOT PROHIBITED BY LAW, NEITHER RENESAS ELECTRONICS CORPORATION NOR ANY OF ITS AFFILIATED COMPANIES
 * SHALL BE LIABLE FOR ANY DIRECT, INDIRECT, SPECIAL, INCIDENTAL OR CONSEQUENTIAL DAMAGES FOR ANY REASON RELATED TO
 * THIS SOFTWARE, EVEN IF RENESAS OR ITS AFFILIATES HAVE BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGES.
 * Renesas reserves the right, without notice, to make changes to this software and to discontinue the availability of
 * this software. By using this software, you agree to the additional terms and conditions found by accessing the
 * following link:
 * http://www.renesas.com/disclaimer
 *
 * Copyright (C) 2020 Renesas Electronics Corporation. All rights reserved.
 *********************************************************************************************************************/
 /***********************************************************************/
 /*                                                                     */
 /*  FILE        :Main.c                                                */
 /*  DATE        :Mon, Oct 24, 2016                                     */
 /*  DESCRIPTION :Main Program                                          */
 /*  CPU TYPE    :                                                      */
 /*                                                                     */
 /*  NOTE:THIS IS A TYPICAL EXAMPLE.                                    */
 /*                                                                     */
 /***********************************************************************/
#include <dtdef.h>
#include "bsw_if.h"
#include "SchM.h"
void main(void);

static void ut_app_main(void)
{
	while(1){
        uint32_t tick = dt_tick_get();
		dt_kprintf("I am in app main task!!\n");
		dt_thread_delay_until(&tick, 100);
	}
}

static DT_STACK_DEFINE(test_Stk, 4096);
static void APP_Test_Init(void)
{
    dt_tid_t thread_t = 0U;
    dt_thread_attr_t attr = {
        .name = "app_task",
        .prio = 19,
        .options = K_TASK_CORE(1),
        .stackaddr = test_Stk,
        .stacksize = sizeof(test_Stk),
    };
    (void)dt_thread_create(&thread_t, &attr, (void*)ut_app_main, 0);
    (void)dt_thread_startup(thread_t);
}

void main(void)
{
    SchM_Init();
    SchM_Start();
    APP_Test_Init();
    while (1) {
        dt_thread_mdelay(1000);
    }
}
