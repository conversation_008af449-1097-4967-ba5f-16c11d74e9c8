#include <dtdef.h>
#include "kernel.h"
#include "info.h"
#include "syscalls/thread.h"
#include "syscalls/msg_q.h"
#include "syscalls/mutex.h"
#include "syscalls/sem.h"
#include "syscalls/event.h"

/* section information */
typedef struct {
    unsigned long src;     /* Destination Address    */
    unsigned long end;      /* Source Address        */
    unsigned long dst;     /* Section Size            */
} DATA_SEC_INFO;

typedef struct{
  unsigned long start;
  unsigned long end;
}BSS_SEC_INFO;

#define FOREACH_DYNAMIC_DATA(key_, iter_, s_, e_)\
  struct static_##key_##_data **sec_start = (struct static_##key_##_data **)s_;\
  struct static_##key_##_data **sec_end = (struct static_##key_##_data **)e_;\
  for( iter_ = s_; iter_ < e_; iter_ ++)



#define CONFIG_APP_MSG_SIZE 8

extern dt_ubase_t _S_INIT_DSEC_const[];
extern dt_ubase_t _E_INIT_DSEC_const[];
extern dt_ubase_t _S_INIT_BSEC_const[];
extern dt_ubase_t _E_INIT_BSEC_const[];
extern const uintptr_t DT_TYPE_COMMON_SECTION_START(text)[];
extern const uintptr_t DT_TYPE_COMMON_SECTION_END(text)[];
extern const uintptr_t DT_TYPE_COMMON_SECTION_START(data_R)[];
extern const uintptr_t DT_TYPE_COMMON_SECTION_END(bss)[];
extern const uintptr_t DT_TYPE_SECTION_START(knl_static_thread_data, const)[];
extern const uintptr_t DT_TYPE_SECTION_END(knl_static_thread_data, const)[];
extern const uintptr_t DT_TYPE_SECTION_START(knl_static_sem_data, const)[];
extern const uintptr_t DT_TYPE_SECTION_END(knl_static_sem_data, const)[];
extern const uintptr_t DT_TYPE_SECTION_START(knl_static_mutex_data, const)[];
extern const uintptr_t DT_TYPE_SECTION_END(knl_static_mutex_data, const)[];
extern const uintptr_t DT_TYPE_SECTION_START(knl_static_event_data, const)[];
extern const uintptr_t DT_TYPE_SECTION_END(knl_static_event_data, const)[];
extern const uintptr_t DT_TYPE_SECTION_START(knl_static_mailbox_data, const)[];
extern const uintptr_t DT_TYPE_SECTION_END(knl_static_mailbox_data, const)[];
extern const uintptr_t DT_TYPE_SECTION_START(knl_static_mq_data, const)[];
extern const uintptr_t DT_TYPE_SECTION_END(knl_static_mq_data, const)[];
extern const uintptr_t DT_TYPE_SECTION_START(knl_static_tasklet_data, const)[];
extern const uintptr_t DT_TYPE_SECTION_END(knl_static_tasklet_data, const)[];


dt_tid_t app_bg_id = 0;
DT_STACK_DEFINE(app_stack, 2048);
void app_bg_thread(void *param);

DT_MESSAGEQUEUE_DEFINE(app_msgq, sizeof(app_msg_t), 32);


void _INITSCT_RH(void *p_sinfo_copy, void *p_einfo_copy, void *p_sinfo_clear, void *p_einfo_clear)
{
  DATA_SEC_INFO        *pdata_info, *pdata_einfo;
  BSS_SEC_INFO         *pbss_info, *pbss_einfo;
  // unsigned long   n;
  // unsigned char   *p;
  // unsigned char   *q;

  /* COPY (ROM->RAM) */
  pdata_info = (DATA_SEC_INFO *)p_sinfo_copy;
  pdata_einfo = (DATA_SEC_INFO *)p_einfo_copy;
  while (pdata_info < pdata_einfo){
    for(uint32_t *psrc = (uint8_t *)pdata_info->src,
      *pdst = (uint32_t *)pdata_info->dst; 
      psrc < (uint32_t *)pdata_info->end; psrc ++, pdst ++){
      *pdst = *psrc;
    }
    pdata_info++;
  }
  
  /* CLEAR */
  pbss_info = (BSS_SEC_INFO *)p_sinfo_clear;
  pbss_einfo = (BSS_SEC_INFO *)p_einfo_clear;
  while (pbss_info < pbss_einfo){
    for(uint8_t *pos = (uint8_t *)pbss_info->start; 
      pos < (uint8_t *)pbss_info->end; pos ++){
      *pos = 0x00;
    }
    pbss_info ++;
  }
  
  return;
}

dt_err_t dt_knl_init_dynamic_threads(uintptr_t s, uintptr_t e)
{
  struct static_thread_data **iter;
  FOREACH_DYNAMIC_DATA(thread, iter, s, e){
    struct static_thread_data *data = *iter;
    dt_tid_t *ptid = (dt_tid_t *)data->init_object;
    dt_thread_attr_t attr = {
      .name = data->init_name,
      .prio = data->init_prio,
      .options = data->init_options,
      .period = data->init_tick,
      .timelimit = data->init_dead_tick,
      .delayed = data->init_delayed,
      .stackaddr = data->init_stack,
      .stacksize = data->init_stack_size,
    };
    dt_thread_create(ptid, &attr, data->init_entry, data->init_param);
  }
}

dt_err_t dt_knl_init_dynamic_mutexs(uintptr_t s, uintptr_t e)
{
  struct static_mutex_data **iter;
  FOREACH_DYNAMIC_DATA(mutex, iter, s, e){
    struct static_mutex_data *data = *iter;
    dt_mutex_id_t *pmtxid = (dt_mutex_id_t *)data->init_object;
    dt_mutex_create(pmtxid, data->init_name, 0);
  }
}

dt_err_t dt_knl_init_dynamic_sems(uintptr_t s, uintptr_t e)
{
  struct static_sem_data **iter;
  FOREACH_DYNAMIC_DATA(sem, iter, s, e){
    struct static_sem_data *data = *iter;
    dt_sem_id_t *psemid = (dt_sem_id_t *)data->init_object;
    dt_sem_create(psemid, data->init_name, data->init_value, data->init_limit);
  }
  return DT_EOK;
}

dt_err_t dt_knl_init_dynamic_events(uintptr_t s, uintptr_t e)
{
  struct static_event_data **iter;
  FOREACH_DYNAMIC_DATA(event, iter, s, e){
    struct static_event_data *data = *iter;
    dt_event_id_t *peid = (dt_event_id_t *)data->init_object;
    dt_event_create(peid, data->init_name, 0);
  }
}

dt_err_t dt_knl_init_dynamic_msgqs(uintptr_t s, uintptr_t e)
{
  struct static_mq_data **iter;\
  FOREACH_DYNAMIC_DATA(mq, iter, s, e){
    struct static_mq_data *data = *iter;
    dt_mq_id_t *pmqid = (dt_mq_id_t *)data->init_object;
    dt_mq_create(pmqid, data->init_name, data->init_msg_pool,
              data->init_msg_size, data->init_max_msgs, 0);
  }
  return DT_EOK;
}

// dt_err_t dt_knl_init_dynamic_tasklets(uintptr_t s, uintptr_t e)
// {
//   struct static_tasklet_data **iter;
//   FOREACH_DYNAMIC_DATA(tasklet, iter, s, e){
//     struct static_tasklet_data *data = *iter;
//     struct dt_tasklet *tasklet = (struct dt_tasklet *)data->init_object;
//     (void)dt_knl_tasklet_init(tasklet, data->init_name, data->init_taskid, 
//         data->init_prio,data->init_func, data->init_data);
//   }
//   return DT_EOK;
// }
void app_error(uint32_t error)
{

}
void app_shutdown(uint32_t reason)
{

}
void app_startup(void)
{
  extern void main(void);
  main();
}

extern const firmware_info_t app_info;
static void ccrt_app_startup(void)
{
  dt_knl_init_dynamic_threads(
      (uintptr_t)DT_TYPE_SECTION_START(knl_static_thread_data, const), 
      (uintptr_t)DT_TYPE_SECTION_END(knl_static_thread_data, const));
  dt_knl_init_dynamic_mutexs(
      (uintptr_t)DT_TYPE_SECTION_START(knl_static_mutex_data, const), 
      (uintptr_t)DT_TYPE_SECTION_END(knl_static_mutex_data, const));
  dt_knl_init_dynamic_sems(
      (uintptr_t)DT_TYPE_SECTION_START(knl_static_sem_data, const), 
      (uintptr_t)DT_TYPE_SECTION_END(knl_static_sem_data, const));
  dt_knl_init_dynamic_events(
      (uintptr_t)DT_TYPE_SECTION_START(knl_static_event_data, const), 
      (uintptr_t)DT_TYPE_SECTION_END(knl_static_event_data, const));
  // dt_knl_init_dynamic_msgqs(
  //     (uintptr_t)DT_TYPE_SECTION_START(knl_static_mq_data, const), 
  //     (uintptr_t)DT_TYPE_SECTION_END(knl_static_mq_data, const));
  // dt_knl_init_dynamic_tasklets(
  //     (uintptr_t)DT_TYPE_SECTION_START(knl_static_tasklet_data, const), 
  //     (uintptr_t)DT_TYPE_SECTION_END(knl_static_tasklet_data, const));
  app_startup();
}



static void ccrt_app_shutdown(uint32_t reason)
{
  app_shutdown(reason);
}

void ccrt_start(void)
{
  _INITSCT_RH((void *)_S_INIT_DSEC_const,
    (void *)_E_INIT_DSEC_const,
    (void *)_S_INIT_BSEC_const,
    (void *)_E_INIT_BSEC_const);
}


typedef enum{
  APP_EVENT_ERR,
  APP_EVENT_USER,
  APP_EVENT_STARTUP,
  APP_EVENT_SHUTDOWN,
}app_event_t;


void app_bg_thread(void *param)
{
  ccrt_start();
  ccrt_app_startup();
  while(true){
    app_msg_t msg;
    dt_mq_recv(app_msgq, &msg, sizeof(msg), DT_WAITING_FOREVER);
    switch (msg.msgid){
    case APP_EVENT_ERR:
      app_error(msg.param2);
      break;
    case APP_EVENT_SHUTDOWN:
      ccrt_app_shutdown(msg.param2);
      break;
    default:
      break;
    }
  }
}

const struct static_thread_data info_head = DT_THREAD_INITIALIZER(&app_bg_id,
    app_stack, 2048, app_bg_thread, NULL, 4, 0, 0, "app_bg", 0, 0, 0);

// DT_SECTION(.fw_info)


// const firmware_info_t app_info = {
//   .head.magic = CCASW_MAGIC,
//   .head.id = {0x4B, 0x4D, 0x00, 0x11, 0xDE, 0x01, 0x14, 0x06, 0x00},  //HT-D3
//   //.head.id = {0x4A, 0x56, 0x00, 0x11, 0xE0, 0x01, 0x14, 0x05, 0x00},    //HT-W3
//   .head.ecu_ver = {0x75u,0x49u,0x18u,0x03u,0x0Du,0x02u,0,0,0},
//   .priv.app.parts_code = "HTE-2947110DA-D3",
//   .head.hw_ver = {0x8C, 23, 11, 6, 0, 0, 0, 0, 0},//Ӳ���汾
//   .priv.app.head = &info_head,
//   .priv.app.rom_ss =  (uintptr_t)DT_TYPE_COMMON_SECTION_START(text),
//   .priv.app.rom_se =  (uintptr_t)DT_TYPE_COMMON_SECTION_END(text),
//   .priv.app.ram_ss =  (uintptr_t)DT_TYPE_COMMON_SECTION_START(data_R),
//   .priv.app.ram_se =  (uintptr_t)DT_TYPE_COMMON_SECTION_END(bss),
// };

// PRAGMA(section default)