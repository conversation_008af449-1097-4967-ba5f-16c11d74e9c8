﻿/*
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2024-04-26 10:51:58
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2024-04-26 12:53:58
 * @FilePath: \Proj_D3_Integral -0424\RTE\DiagCfg\APP_DiagConfig_inc.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/*****************************************************************************
�ļ�����: APP_DiagConfig_inc.h
ժ Ҫ:    �ɸ�����Ҫ�޸� 

����:
�����б�:

* Version:
* - 2023-02-05  �汾1  ����
*
* Revision history
* Date        Version      Author            Description
*----------  -------    -----------     -----------------------------
* 2023-02-05  1.0.00    shu.taixiao     ����
*
* Par: ������Ҫ��Ϣ��
*      ������Ҫ��Ϣ˵������ѡ��
* Warning: ������Ϣ
* Par:��Ȩ��Ϣ
* Copyright (c) 2008-2023 by BYD COMPANY LIMITED. All rights reserved.
*****************************************************************************/
#ifndef APP_DIAGCONFIG_INC_H_
#define APP_DIAGCONFIG_INC_H_

#include "Features_Cfg.h"
#include "bsw_uds_defines.h"

#if (FEATURE_APP_DISUS_A == FEATURE_ENABLE)
#include "DiSus_A_IOControlByIdentifierCallback.h"
#include "DiSus_A_ReadDataByIdentifierCallback.h"
#include "DiSus_A_RoutineControlCallback.h"
#include "DiSus_A_WriteDataByIdentifierCallback.h"
#include "DiSus_A_DtcStatusScanInterface.h"
#include "DiSus_DiagFunctionIf_IOControl.h"
#include "DiSus_DiagFunctionIf_Routine.h"
#include "DiSus_DiagFunction_ReadDtc.h"
#include "DiSus_A_DTCCfg.h"
#include "DiSus_NVM.h"
#include "VehicleCfg.h"
#endif

#if (FEATURE_APP_EPB == FEATURE_ENABLE)
#include "EPB_IOControlByIdentifierCallback.h"
#include "EPB_ReadDataByIdentifierCallback.h"
#include "EPB_RoutineControlCallback.h"
#include "EPB_WriteDataByIdentifierCallback.h"
#include "EPB_DtcStatusScanInterface.h"
#include "EPB_DTCCfg.h"
#endif

#if (FEATURE_APP_EPSA == FEATURE_ENABLE)
#include "EPSA_ReadDataByIdentifierCallback.h"
#include "EPSA_DtcStatusScanInterface.h"
#include "EPSA_DTCCfg.h"
#include "EPSA_WriteDataByIdentifierCallback.h"
#endif

#if (FEATURE_APP_ETS == FEATURE_ENABLE)
#include "ETS_ReadDataByIdentifierCallback.h"
#include "ETS_DtcStatusScanInterface.h"
#include "ETS_IOControlByIdentifierCallback.h"
#include "ETS_DTCCfg.h"
#endif

#if (FEATURE_APP_IMS == FEATURE_ENABLE)
#include "IMS_ReadDataByIdCallback.h"
#include "IMS_IOControlByIdentCallback.h"
#include "IMS_WriteDataByIdentCallback.h"
#endif

#if (FEATURE_APP_AFS == FEATURE_ENABLE)
#include "AFS_ReadDataByIdentifierCallback.h"
#include "AFS_DtcStatusScanInterface.h"
#include "AFS_DTCCfg.h"
#endif
#if (FEATURE_APP_VMC == FEATURE_ENABLE)
#include "VMC_ReadDataByIdentifierCallback.h"
#include "VMC_DTCCfg.h"
#include "VMC_DtcStatusScanInterface.h"
#endif

#if (FEATURE_APP_VSE == FEATURE_ENABLE)
#include "VSE_ReadDataByldentifierCallback.h"
#endif

#endif /* APP_DIAGCONFIG_INC_H_ */
