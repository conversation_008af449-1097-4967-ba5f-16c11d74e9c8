/*****************************************************************************
* File Name: App_DTCCfg.c
* Brief: 本文件定义了DTC List和冻结帧的DTC List.


* Version:
* - 2023-02-05  版本1  创建
*
* Revision history
* Date        Version      Author            Description
*----------  -------    -----------     -----------------------------
* 2023-02-05  1.0.00    shu.taixiao     创建
*
* Par: 其他重要信息：
*      其他重要信息说明（可选）
* Warning: 警告信息
* Par:版权信息
* Copyright (c) 2008-2023 by BYD COMPANY LIMITED. All rights reserved.
*****************************************************************************/
#include "APP_DiagConfig_inc.h"
#include "info.h"
#include "string.h"
/* place your code section start */


const stDTCMsgStructType g_stDTCList[] =
{

{0x6DB081u, NULL, 0u   , 0u }, /* 示例，集成时需删除 */

#if (FEATURE_APP_DISUS_A == FEATURE_ENABLE)
#include "DiSus_A_DTCCfgTbl.h"
#endif

#if (FEATURE_APP_EPB == FEATURE_ENABLE)
#include "EPB_DTCCfgTbl.h"
#endif

#if (FEATURE_APP_ETS == FEATURE_ENABLE)
#include "ETS_DTCCfgTbl.h"
#endif

#if (FEATURE_APP_IMS == FEATURE_ENABLE)
// #include "IMS_DTCCfgTbl.h"
#endif

#if (FEATURE_APP_EPSA == FEATURE_ENABLE)
#include "EPSA_DTCCfgTbl.h"
#endif

#if (FEATURE_APP_VMC == FEATURE_ENABLE)
#include "VMC_DTCCfgTbl.h"
#endif
#if (FEATURE_APP_AFS == FEATURE_ENABLE)
#include "AFS_DTCCfgTbl.h"
#endif
};

uint16_t get_num_S19_DID(void)
{
    return ((sizeof(g_stDTCList)) / sizeof(stDTCMsgStructType));
}

/*****************************************************************************
  函数名称:       ClearDtcCallback
  说明:          $14服务  清除故障码回调函数
  输入参数:       reqData
  输出参数:       resData

  返回值:         E_OK     任务完成

  被访问变量:
  被修改变量:
  其他:
*****************************************************************************/
void ClearDtcCallback(void)
{
#if (FEATURE_APP_EPB == FEATURE_ENABLE)
    EPB_ClearDtcCallback();
#endif

#if (FEATURE_APP_DISUS_A == FEATURE_ENABLE)
    DiSusA_ClearDtcCallback();
#endif

#if (FEATURE_APP_ETS == FEATURE_ENABLE)
    ETS_ClearDtcCallback();
#endif
#if (FEATURE_APP_AFS == FEATURE_ENABLE)
    AFS_ClearDtcCallback();
#endif
#if (FEATURE_APP_VMC == FEATURE_ENABLE)
    VMC_ClearDtcCallback();
#endif
}

/*****************************************************************************
  函数名称:       Clear_CommunicationDtc
  说明:          上电清除丢失通讯类相关故障
  输入参数:       reqData
  输出参数:       resData

  返回值:         E_OK     任务完成

  被访问变量:
  被修改变量:
  其他:
*****************************************************************************/
void Clear_CommunicationDtc(uint32_t dtc)
{
#if (FEATURE_APP_DISUS_A == FEATURE_ENABLE)
    DiSusA_ClearCommunicationDtc();
#endif
}

/*****************************************************************************
  函数名称:       SetDtcCallback
  说明:           应用层模型故障注入
  输入参数:       emDTC_INDEX index,uint8 status
  输出参数:       无
  返回值:         无
  被访问变量:
  被修改变量:
  其他:          实时调用
*****************************************************************************/
void SetDtcCallback(uint32_t dtc, boolean status)
{
#if (FEATURE_APP_EPB == FEATURE_ENABLE)
    EPB_Set_Fault_Status(index, status);
#endif

#if (FEATURE_APP_DISUS_A == FEATURE_ENABLE)
    DiSusA_Set_Fault_Status(dtc, status);
#endif

#if (FEATURE_APP_ETS == FEATURE_ENABLE)
    ETS_Set_Fault_Status(index, status);
#endif

#if (FEATURE_APP_AFS == FEATURE_ENABLE)
    AFS_Set_Fault_Status(dtc, status);
#endif
}


static uint8 u8ReadConfigWordFlag = 0u; //配置字标识符
#define NUM_OF_HT_VEHICLE  37u

void SetReadConfigWordFlag(uint8 uFlag)
{
	u8ReadConfigWordFlag = uFlag;
}

typedef struct
{
    uint32 u32VehicleCode; // 车型配置
} VehicleCfg_Word;

typedef enum
{
    EREA_TYPE = 0,
    HTEA_TYPE,
    MAX_VehicleCode_TYPE,
} stVehicleCode;


uint8 GetCarType(uint8 *VWordConfig)
{
    if (((VWordConfig[0] == 0x11) && ((VWordConfig[1] == 0xDF) || (VWordConfig[1] == 0xE0) || (VWordConfig[1] == 0xDE))) || ((VWordConfig[0] == 0u) && (VWordConfig[1] == 0u)))
    {
        return HTEA_TYPE;
    }
    else
    {
        return 0;
    }
}

/*********************************************************************
*函数功能: 获取 配置字 状况
*输入参数: void
*输出参数: 无
*返回参数: E_STATE
TURE = IDLE, 设备正常
FAILE, 设备故障
*其他说明:
**********************************************************************/
uint8_t au8WordConfig[8u] = {0u};
E_STATE GetConfigWordState(void)
{
    static E_STATE eStatus = TURE;
    uint8 i;
    extern const stECUModuleInfoType gstModuleCfgEx[];
    if (u8ReadConfigWordFlag == 0u)
    {
        if (ShrExp_Get_VehicleCfgData(au8WordConfig, 8u) != 0)
        {
            eStatus = FAILE;
        }
        else
        {
            if (GetCarType(&au8WordConfig) == HTEA_TYPE)
            {
                for (i = 0u; i < NUM_OF_HT_VEHICLE; i++)
                {
                    if ( 0 == strcmp(au8WordConfig,gstModuleCfgEx[i].VehicleCfgWord))
                    {
                        eStatus = TURE;
                        break;
                    }
                    eStatus = FAILE;
                }
            }
            else
            {
                eStatus = FAILE;
            }
        }
        u8ReadConfigWordFlag = 1u;
    }

    return eStatus;
}

void UDSDTCCfgInit(void)
{
    uint16_t i = 0;
    uint16_t DtcNum = get_num_S19_DID();
    if (AppRegSetDtcCallback(SetDtcCallback))
    {
        // dt_kprintf("set dtc reg fail");
    }
    for (i = 0; i < DtcNum; i++)
    {
        if (AppRegDtcTbl(0, (stDTCMsgStructType*)g_stDTCList + i))
        {
            break;
        }
    }
    if (AppRegClearDtcCallback(ClearDtcCallback))
    {
        // dt_kprintf("clear dtc reg fail");
    }
    if(AppRegCheckConfigWordCallback(GetConfigWordState))
    {
        // dt_kprintf("clear dtc reg fail");
    }
}

/* place your code section end */
