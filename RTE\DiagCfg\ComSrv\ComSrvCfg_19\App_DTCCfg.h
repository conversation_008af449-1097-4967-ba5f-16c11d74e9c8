/********************************************************************
* File Name: App_DTCCfg.h
* Brief: 本文件定义了DTC的枚举类型；DTC在EEPROM中保存的地址；带有冻结帧
*   的DTC个数；单个故障码对应冻结帧的分组个数；每组冻结帧数据的长度。并
*   声明DTC List和冻结帧的DTC List.
*
* 文件详细说明
*
* Version:
* - 2023-02-05  版本1  创建
*
* Revision history
* Date        Version      Author            Description
*----------  -------    -----------     -----------------------------
* 2023-02-05  1.0.00    shu.taixiao     创建 
*
* Par: 其他重要信息：
*      其他重要信息说明（可选）
* Warning: 警告信息
* Par:版权信息
* Copyright (c) 2008-2021 by BYD COMPANY LIMITED. All rights reserved.
********************************************************************/
#ifndef APP_DTCINFORMATIONCONFIG_H_
#define APP_DTCINFORMATIONCONFIG_H_
#include "Features_Cfg.h"
#include "bsw_if.h"

typedef enum
{
    DTC_INDEX_C2DB081 = 0x6DB081, //配置字未写入
    
#if (FEATURE_APP_DISUS_A == FEATURE_ENABLE)
    DTC_INDEX_C2DA217 = 0x6DA217u, //诊断过压
    DTC_INDEX_C2DA316 =0x6DA316u	,//	诊断欠压                , //诊断欠压
    DTC_INDEX_U000104 =0xC00104u	,//	CAN             , //CAN Bus-off/CAN总线关闭
    DTC_INDEX_C2DA400 =0x6DA400u	,//	PCBA温度过高                , //PCBA温度过高
    DTC_INDEX_U019780 =0xC19780u	,//	与IPB               , //与IPB/ESP丢失通信
    DTC_INDEX_U20A387 =0xE0A387u	,//	与left_BCM丢失通信              , //与left_BCM丢失通信
    DTC_INDEX_U024587= 0xC24587u	,//	与多媒体丢失通信                 //与多媒体丢失通信
    DTC_INDEX_U018F87= 0xC18F87u	,//	与VCU丢失通信                //与VCU丢失通信
    DTC_INDEX_U110983= 0xD10983u	,//	与EPS丢失通信                //与EPS丢失通信
    DTC_INDEX_C2DB100= 0x6DB100u	,//	ECU自检失败              //ECU自检失败
    DTC_INDEX_C1E2049= 0x5E2049u	,//	IMU传感器通用故障                //IMU传感器通用故障
    DTC_INDEX_C2DB200= 0x6DB200u	,//	IMU未标定                //IMU未标定
    DTC_INDEX_C1E2146= 0x5E2146u	,//	左前高度传感器未标定                 //左前高度传感器未标定
    DTC_INDEX_C1E2112= 0x5E2112u	,//	左前高度传感器供电对电源短路或开路(过温、过压)               //左前高度传感器供电对电源短路或开路
    DTC_INDEX_C1E2111= 0x5E2111u	,//	左前高度传感器供电对地短路（电流泄漏）               //左前高度传感器供电对地短路
    DTC_INDEX_C1E2129= 0x5E2129u	,//	左前高度传感器输出信号无效（通用故障）               //左前高度传感器输出信号无效
    DTC_INDEX_C1E2246= 0x5E2246u	,//	右前高度传感器未标定                 //右前高度传感器未标定
    DTC_INDEX_C1E2212= 0x5E2212u	,//	右前高度传感器供电对电源短路或开路               //右前高度传感器供电对电源短路或开路
    DTC_INDEX_C1E2211= 0x5E2211u	,//	右前高度传感器供电对地短路               //右前高度传感器供电对地短路
    DTC_INDEX_C1E2229= 0x5E2229u	,//	右前高度传感器输出信号无效               //右前高度传感器输出信号无效
    DTC_INDEX_C1E2346= 0x5E2346u	,//	左后高度传感器未标定                 //左后高度传感器未标定
    DTC_INDEX_C2DA912= 0x6DA912u	,//	左后高度传感器供电对电源短路或开路               //左后高度传感器供电对电源短路或开路
    DTC_INDEX_C2DA911= 0x6DA911u	,//	左后高度传感器供电对地短路               //左后高度传感器供电对地短路
    DTC_INDEX_C2DA900= 0x6DA900u	,//	左后高度传感器输出信号无效               //左后高度传感器输出信号无效
    DTC_INDEX_C1E2446= 0x5E2446u	,//	右后高度传感器未标定                 //右后高度传感器未标定
    DTC_INDEX_C2DAA12= 0x6DAA12u	,//	右后高度传感器供电对电源短路或开路               //右后高度传感器供电对电源短路或开路
    DTC_INDEX_C2DAA11= 0x6DAA11u	,//	右后高度传感器供电对地短路               //右后高度传感器供电对地短路
    DTC_INDEX_C2DAA00= 0x6DAA00u	,//	右后高度传感器输出信号无效               //右后高度传感器输出信号无效
    DTC_INDEX_C1E2B12= 0x5E2B12u	,//	高度控制泵电机继电器对电源短路（仅为控制端检测               //高度控制泵电机继电器对电源短路（仅为控制端检测,初级线圈）
    DTC_INDEX_C1E2B11= 0x5E2B11u	,//	高度控制泵电机继电器对地短路                 //高度控制泵电机继电器对地短路
    DTC_INDEX_C1E2B13= 0x5E2B13u	,//	高度控制泵电机继电器开路或由于高度控制泵电机继电器短路等原因造成的过流               //高度控制泵电机继电器开路或由于高度控制泵电机继电器短路等原因造成的过流
    DTC_INDEX_C1E3610= 0x5E3610u	,//	电源管理通用故障                 //电源管理通用故障
    DTC_INDEX_C2DAE00= 0x6DAE00u	,//	MCU故障              //MCU故障
    DTC_INDEX_U10B787= 0xD0B787u	,//	与ADAS丢失通信               //与ADAS丢失通信
    DTC_INDEX_C2DB012= 0x6DB012u	,//	储气罐压力传感器对电源短路               //储气罐压力传感器对电源短路或开路
    DTC_INDEX_C2DB011= 0x6DB011u	,//	储气罐压力传感器对地短路                 //储气罐压力传感器对地短路
    DTC_INDEX_C2DB029= 0x6DB029u	,//	储气罐压力传感器信号无效                 //储气罐压力传感器信号无效
    DTC_INDEX_C2DB112= 0x6DB112u	,//	电机温度传感器对电源短路                 //电机温度传感器对电源短路或开路
    DTC_INDEX_C2DB111= 0x6DB111u	,//	电机温度传感器对地短路               //电机温度传感器对地短路
    DTC_INDEX_C2DB129= 0x6DB129u	,//	电机温度传感器信号无效               //电机温度传感器信号无效
    DTC_INDEX_C2DB212= 0x6DB212u	,//	左前高度控制阀对电源短路                 //左前高度控制阀对电源短路
    DTC_INDEX_C2DB211= 0x6DB211u	,//	左前高度控制阀对地短路               //左前高度控制阀对地短路
    DTC_INDEX_C2DB213= 0x6DB213u	,//	左前高度控制阀开路               //左前高度控制阀开路
    DTC_INDEX_C2DB312= 0x6DB312u	,//	右前高度控制阀对电源短路                 //右前高度控制阀对电源短路
    DTC_INDEX_C2DB311= 0x6DB311u	,//	右前高度控制阀对地短路               //右前高度控制阀对地短路
    DTC_INDEX_C2DB313= 0x6DB313u	,//	右前高度控制阀开路               //右前高度控制阀开路
    DTC_INDEX_C2DB412= 0x6DB412u	,//	左后高度控制阀对电源短路                 //左后高度控制阀对电源短路
    DTC_INDEX_C2DB411= 0x6DB411u	,//	左后高度控制阀对地短路               //左后高度控制阀对地短路
    DTC_INDEX_C2DB413= 0x6DB413u	,//	左后高度控制阀开路               //左后高度控制阀开路
    DTC_INDEX_C2DB512= 0x6DB512u	,//	右后高度控制阀对电源短路                 //右后高度控制阀对电源短路
    DTC_INDEX_C2DB511= 0x6DB511u	,//	右后高度控制阀对地短路               //右后高度控制阀对地短路
    DTC_INDEX_C2DB513= 0x6DB513u	,//	右后高度控制阀开路               //右后高度控制阀开路
    DTC_INDEX_C2DB612= 0x6DB612u	,//	储气罐阀对电源短路               //储气罐阀对电源短路
    DTC_INDEX_C2DB611= 0x6DB611u	,//	储气罐阀对地短路                 //储气罐阀对地短路
    DTC_INDEX_C2DB613= 0x6DB613u	,//	储气罐阀开路                 //储气罐阀开路
    DTC_INDEX_C2DB712= 0x6DB712u	,//	泄压阀对电源短路                 //泄压阀对电源短路
    DTC_INDEX_C2DB711= 0x6DB711u	,//	泄压阀对地短路               //泄压阀对地短路
    DTC_INDEX_C2DB713= 0x6DB713u	,//	泄压阀开路               //泄压阀开路
    DTC_INDEX_C2DB812= 0x6DB812u	,//	快速增压阀对电源短路                 //快速增压阀对电源短路
    DTC_INDEX_C2DB811= 0x6DB811u	,//	快速增压阀对地短路               //快速增压阀对地短路
    DTC_INDEX_C2DB813= 0x6DB813u	,//	快速增压阀开路               //快速增压阀开路
    DTC_INDEX_C2DB912= 0x6DB912u	,//	左前阻尼连续可调线性阀1对电源短路或过流              //左前阻尼连续可调线性阀对电源短路或过流
    DTC_INDEX_C2DB911= 0x6DB911u	,//	左前阻尼连续可调线性阀1对地短路              //左前阻尼连续可调线性阀对地短路
    DTC_INDEX_C2DB913= 0x6DB913u	,//	左前阻尼连续可调线性阀1开路              //左前阻尼连续可调线性阀开路
    DTC_INDEX_C2DBB12= 0x6DBB12u	,//	右前阻尼连续可调线性阀1对电源短路或过流              //右前阻尼连续可调线性阀对电源短路或过流
    DTC_INDEX_C2DBB11= 0x6DBB11u	,//	右前阻尼连续可调线性阀1对地短路              //右前阻尼连续可调线性阀对地短路
    DTC_INDEX_C2DBB13= 0x6DBB13u	,//	右前阻尼连续可调线性阀1开路              //右前阻尼连续可调线性阀开路
    DTC_INDEX_C2DBD12= 0x6DBD12u	,//	左后阻尼连续可调线性阀1对电源短路或过流              //左后阻尼连续可调线性阀对电源短路或过流
    DTC_INDEX_C2DBD11= 0x6DBD11u	,//	左后阻尼连续可调线性阀1对地短路              //左后阻尼连续可调线性阀对地短路
    DTC_INDEX_C2DBD13= 0x6DBD13u	,//	左后阻尼连续可调线性阀1开路              //左后阻尼连续可调线性阀开路
    DTC_INDEX_C2DBE12= 0x6DBE12u	,//	右后阻尼连续可调线性阀1对电源短路或过流              //右后阻尼连续可调线性阀对电源短路或过流
    DTC_INDEX_C2DBE11= 0x6DBE11u	,//	右后阻尼连续可调线性阀1对地短路              //右后阻尼连续可调线性阀对地短路
    DTC_INDEX_C2DBE13= 0x6DBE13u	,//	右后阻尼连续可调线性阀1开路              //右后阻尼连续可调线性阀开路
    DTC_INDEX_C2DC012= 0x6DC012u	,//	左前空簧刚度控制开关阀电磁阀对电源短路               //左前空簧刚度控制开关阀电磁阀对电源短路
    DTC_INDEX_C2DC011= 0x6DC011u	,//	左前空簧刚度控制开关阀电磁阀对地短路                 //左前空簧刚度控制开关阀电磁阀对地短路
    DTC_INDEX_C2DC013= 0x6DC013u	,//	左前空簧刚度控制开关阀电磁阀开路                 //左前空簧刚度控制开关阀电磁阀开路
    DTC_INDEX_C2DC112= 0x6DC112u	,//	右前空簧刚度控制开关阀电磁阀对电源短路               //右前空簧刚度控制开关阀电磁阀对电源短路
    DTC_INDEX_C2DC111= 0x6DC111u	,//	右前空簧刚度控制开关阀电磁阀对地短路                 //右前空簧刚度控制开关阀电磁阀对地短路
    DTC_INDEX_C2DC113= 0x6DC113u	,//	右前空簧刚度控制开关阀电磁阀开路                 //右前空簧刚度控制开关阀电磁阀开路
    DTC_INDEX_C2DC212= 0x6DC212u	,//	左后空簧刚度控制开关阀电磁阀对电源短路               //左后空簧刚度控制开关阀电磁阀对电源短路
    DTC_INDEX_C2DC211= 0x6DC211u	,//	左后空簧刚度控制开关阀电磁阀对地短路                 //左后空簧刚度控制开关阀电磁阀对地短路
    DTC_INDEX_C2DC213= 0x6DC213u	,//	左后空簧刚度控制开关阀电磁阀开路                 //左后空簧刚度控制开关阀电磁阀开路
    DTC_INDEX_C2DC312= 0x6DC312u	,//	右后空簧刚度控制开关阀电磁阀对电源短路               //右后空簧刚度控制开关阀电磁阀对电源短路
    DTC_INDEX_C2DC311= 0x6DC311u	,//	右后空簧刚度控制开关阀电磁阀对地短路                 //右后空簧刚度控制开关阀电磁阀对地短路
    DTC_INDEX_C2DC313= 0x6DC313u	,//	右后空簧刚度控制开关阀电磁阀开路                 //右后空簧刚度控制开关阀电磁阀开路
    DTC_INDEX_C2DBC00= 0x6DBC00u	,//	悬架系统过温                 //悬架系统过温
    DTC_INDEX_C2DB300= 0x6DB300u	,//	悬架系统欠温                 //悬架系统欠温
    DTC_INDEX_C2DB400= 0x6DB400u	,//	悬架系统过压                 //悬架系统过压
    DTC_INDEX_C2DB500= 0x6DB500u	,//	悬架行程过高                 //悬架行程过高
    DTC_INDEX_C2DB600= 0x6DB600u	,//	悬架行程过低                 //悬架行程过低
    DTC_INDEX_C2DB700= 0x6DB700u	,//	悬架高度调节速率异常                 //悬架高度调节速率异常
    DTC_INDEX_C2DB800= 0x6DB800u	,//	悬架阀系占空比超限               //悬架阀系占空比超限
    DTC_INDEX_C2DB900= 0x6DB900u	,//	悬架储气罐充气速率异常               //悬架储气罐充气速率异常
    DTC_INDEX_C2DBA00= 0x6DBA00u	,//	悬架控制泵过热               //悬架控制泵过热
	DTC_INDEX_C2DC500= 0x6DC500u	,//	下电SBC复位异常              // 下电SBC复位异常
    DTC_INDEX_C2DC700= 0x6DC700u	,//	悬架系统排气异常                 //悬架系统排气异常
	DTC_INDEX_C2DBB00= 0x6DBB00u	,//	悬架控制泵建压能力不足               // 悬架控制泵建压能力不足
	DTC_INDEX_C2DC702= 0x6DC702u	,//	扭矩信号异常                 //扭矩信号异常
	DTC_INDEX_C2DC703= 0x6DC703u	,//	轮速信号异常                 //轮速信号异常
	DTC_INDEX_C2DC704= 0x6DC704u	,//	制动信号异常                 //制动信号异常
	DTC_INDEX_C2DC705= 0x6DC705u	,//	转向信号异常                 //转向信号异常
	DTC_INDEX_C2DC707= 0x6DC707u	,//	姿态阀和泵底层保护故障               //姿态阀和泵底层保护故障
    DTC_INDEX_C2DC900= 0x6DC900u	,//	车辆碰撞故障                 //车辆碰撞故障
    DTC_INDEX_C2DCA31= 0x6DCA31u	,//	悬架温度信号失效                 //悬架温度信号失效
    DTC_INDEX_C2DCA00= 0x6DCA00u	,//	侧翼四通阀对电源短路-20230515                //侧翼四通阀对电源短路-20230515
    DTC_INDEX_C2DCB00= 0x6DCB00u	,//	侧翼四通阀对地短路               //侧翼四通阀对地短路
    DTC_INDEX_C2DCC00= 0x6DCC00u	,//	侧翼四通阀开路               //侧翼四通阀开路
    DTC_INDEX_C2DCD00= 0x6DCD00u	,//	侧翼压力传感器对电源短路                 //侧翼压力传感器对电源短路或开路
    DTC_INDEX_C2DCE00= 0x6DCE00u	,//	侧翼压力传感器对地短路或开路                 //侧翼压力传感器对地短路
    DTC_INDEX_C2DCF00= 0x6DCF00u	,//	侧翼压力传感器输出信号无效               //侧翼压力传感器输出信号无效
    DTC_INDEX_C2DD000= 0x6DD000u	,//	排气阀占空比故障                 //排气阀占空比故障
    DTC_INDEX_C2DC708= 0x6DC708u	,//	IG继电器状态不匹配               //IG继电器状态不匹配
    DTC_INDEX_C2DA500= 0x6DA500u	,//	悬架系统漏气                 //悬架系统漏气
    DTC_INDEX_U102A88= 0xD02A88u	,//	ADAS                 //ADAS CANBUSOFF
    DTC_INDEX_U007388= 0xC07388u	,//	IPB              //IPB CANBUSOFF
//    DTC_INDEX_C11710A, //AFS侧翼管道失效
    DTC_INDEX_U2CB111= 0xECB111u	,//	与路面预瞄丢失通讯               //与路面预瞄丢失通讯
    DTC_INDEX_C2DD42B= 0x6DD42Bu	,//	执行器信号反向故障                //执行器信号反向故障
#endif

#if (FEATURE_APP_EPB == FEATURE_ENABLE)
    DTC_INDEX_C110016, //Ecu battery under voltage  
    DTC_INDEX_C110017, //Ecu battery over voltage
    DTC_INDEX_C110018, //Ecu Battery soft under voltage
    DTC_INDEX_C110019, //Ecu Battery soft over voltage
    DTC_INDEX_C116009, //Ecu hardware error
    DTC_INDEX_C112054, //End of line test error 
    DTC_INDEX_C116006, //Caliper mode error 
    DTC_INDEX_C111013, //Ign on wire error
    DTC_INDEX_C117009, //EPB button Error
    DTC_INDEX_C117007, //EPB button stuck
    DTC_INDEX_C117006, //EPB button apply forbid
    DTC_INDEX_U012287, //Lose communication with ESP
    DTC_INDEX_U014087, //Lose communication with BCM
    DTC_INDEX_U010087, //Lose communication with ECM
    DTC_INDEX_U010187, //Lose communication with TCU
    DTC_INDEX_U011087, //Lose communication with MCU
    DTC_INDEX_U011088, //Lose communication with CCU
    DTC_INDEX_U041681, //CAN invalid signal from ESP
    DTC_INDEX_U042281, //CAN invalid signal from BCM
    DTC_INDEX_U102281, //CAN invalid signal from ECM
    DTC_INDEX_U040281, //CAN invalid signal from TCU
    DTC_INDEX_U041181, //CAN invalid signal from MCU
    DTC_INDEX_U041182, //CAN invalid signal from CCU
    DTC_INDEX_U007388, //CAN bus off
    DTC_INDEX_C11600A, //Predrive inside fault  
    DTC_INDEX_C11600B, //Predrive voltage fault  
    DTC_INDEX_C11600C, //Predrive SPI fault  
    DTC_INDEX_C117498, //Predrive over temperature fault  
    DTC_INDEX_C11600D, //Predrive GIO0_3 fault 
    DTC_INDEX_C11600E, //Predrive GIO4_8 short to power  
    DTC_INDEX_C11600F, //Predrive GIO4_8 short to ground  
    DTC_INDEX_C116010, //Predrive GPIO fault  
    DTC_INDEX_C115009, //Left caliper line error  
    DTC_INDEX_C11B010, //Left caliper overload  
    DTC_INDEX_C11B617, //Left caliper long time work  
    DTC_INDEX_C11BC00, //Left caliper in diagnostic mode  
    DTC_INDEX_C11B013, //Left caliper line open  
    DTC_INDEX_C11B41D, //Left caliper over currut 
    DTC_INDEX_C113016, //Left caliper power supply voltage below threshold  
    DTC_INDEX_C113017, //Left caliper power supply voltage above threshold  
    DTC_INDEX_C11B011, //Left caliper line short to ground  
    DTC_INDEX_C11B012, //Left caliper line shour to battery  
    DTC_INDEX_C11B272, //Predrive left side mos voltage fault  
    DTC_INDEX_C11BA29, //Predrive left side sensor fault 
    DTC_INDEX_C115109, //Right caliper line error  
    DTC_INDEX_C11B110, //Right caliper overload 
    DTC_INDEX_C11B717, //Right caliper long time work  
    DTC_INDEX_C11BD00, //Right caliper in diagnostic mode  
    DTC_INDEX_C11B113, //Right caliper line open  
    DTC_INDEX_C11B51D, //Right caliper over currut  
    DTC_INDEX_C113116, //Right caliper power supply voltage below threshold 
    DTC_INDEX_C113117, //Right caliper power supply voltage above threshold  
    DTC_INDEX_C11B111, //Right caliper line short to ground  
    DTC_INDEX_C11B112, //Right caliper line shour to battery 
    DTC_INDEX_C11B372, //Predrive right side mos voltage fault 
    DTC_INDEX_C11BB29, //Predrive right side sensor fault 
#endif

#if (FEATURE_APP_ETS == FEATURE_ENABLE)
    DTC_INDEX_C11610A,
    DTC_INDEX_C11610B,
    DTC_INDEX_C11610C,
    DTC_INDEX_C117598,
    //DTC_INDEX_C115209,
    DTC_INDEX_C11B211,
    //DTC_INDEX_C11B817,
    DTC_INDEX_C11B61D,
    DTC_INDEX_C113216,
    DTC_INDEX_C113217,
    DTC_INDEX_C11B472,
    DTC_INDEX_C11BC29,   
    DTC_INDEX_C11B473,
    DTC_INDEX_C11B474,
    DTC_INDEX_C11B475,
    DTC_INDEX_C11BC2A,
    DTC_INDEX_C11B476,
    DTC_INDEX_C11B477,
    DTC_INDEX_C11B478,
#endif

#if (FEATURE_APP_IMS == FEATURE_ENABLE)

#endif

#if (FEATURE_APP_EPSA == FEATURE_ENABLE)
    DTC_INDEX_C1DE100 = 0x5DE100,
    DTC_INDEX_C1DE101 = 0x5DE101,
    DTC_INDEX_C1DE102 = 0x5DE102,
    DTC_INDEX_C1DE103 = 0x5DE103,
    DTC_INDEX_C1DE104 = 0x5DE104,
    DTC_INDEX_C1DE105 = 0x5DE105,
    DTC_INDEX_C1DE106 = 0x5DE106,
    DTC_INDEX_C1DE107 = 0x5DE107,
    DTC_INDEX_C1DE108 = 0x5DE108,
    DTC_INDEX_C1DE109 = 0x5DE109,
    DTC_INDEX_C1DE10A = 0x5DE10A,
    DTC_INDEX_C1DE10B = 0x5DE10B,
    DTC_INDEX_C11D018 = 0x51D018,
    DTC_INDEX_C12D021 = 0x52D021,
    DTC_INDEX_U021402 = 0xC21402,
    DTC_INDEX_U021403 = 0xC21403,
    DTC_INDEX_U021404 = 0xC21404,
    DTC_INDEX_U021405 = 0xC21405,
    DTC_INDEX_U021407 = 0xC21407,
    DTC_INDEX_U021408 = 0xC21408,
    DTC_INDEX_U021411 = 0xC21411,
    DTC_INDEX_U021412 = 0xC21412,
    DTC_INDEX_U021409 = 0xC21409,
    DTC_INDEX_U02140A = 0xC2140A,
    DTC_INDEX_C12D012 = 0x52D012,
    DTC_INDEX_C12D013 = 0x52D013,
    DTC_INDEX_C12D014 = 0x52D014,
    DTC_INDEX_C12D015 = 0x52D015,
    DTC_INDEX_C12D016 = 0x52D016,
    DTC_INDEX_C12D017 = 0x52D017,
    DTC_INDEX_C12D018 = 0x52D018,
    DTC_INDEX_C12D019 = 0x52D019,
    DTC_INDEX_C12D01A = 0x52D01A,
    DTC_INDEX_C12D01B = 0x52D01B,
    DTC_INDEX_C12D01C = 0x52D01C,
    DTC_INDEX_C12D01D = 0x52D01D,
    DTC_INDEX_C11D019 = 0x51D019,
    DTC_INDEX_C11D020 = 0x51D020,
    DTC_INDEX_C12D01E = 0x52D01E,
    DTC_INDEX_C12D01F = 0x52D01F,
    DTC_INDEX_C12D020 = 0x52D020,
    DTC_INDEX_C11D022 = 0x51D022,
    DTC_INDEX_C11D021 = 0x51D021,
    
#endif
#if (FEATURE_APP_VMC == FEATURE_ENABLE)
    DTC_INDEX_C21A001 = 0x61A001u, //IPB LDI失效
    DTC_INDEX_C21A021 = 0x61A021u, //VDC功能故障
    DTC_INDEX_C21A031 = 0x61A031u, //ABS功能故障
    DTC_INDEX_C21A041 = 0x61A041u, //TCS功能故障

#endif
#if (FEATURE_APP_AFS == FEATURE_ENABLE)
    DTC_INDEX_C11710A = 0x51710A, //AFS爆管故障
#endif
    NUM_OF_DTC
}emDTC_INDEX;



#define HIGH_FREQ_SCAN_TIME      5u         //高频故障码调度时间
#define MIDDLE_FREQ_SCAN_TIME    15u        //中频故障码调度时间
#define LOW_FREQ_SCAN_TIME       50u        //低频故障码调度时间
#define CHECK_AUTO_RECOVERY_TIME 10000u     //上电刷新点火开关钥匙循环计数的时间间隔


extern void UDSDTCCfgInit(void);
extern void ClearDtcCallback(void);
extern void AppModule_Set_Fault_Status(emDTC_INDEX index, boolean status);
extern void InitDtc_CommDtc(emDTC_INDEX *parr_emDtcIndex,uint16 Len);

#endif /* APP_DTCINFORMATIONCONFIG_H_ */

