/*
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2024-04-26 10:51:58
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2024-04-26 15:11:28
 * @FilePath: \Proj_D3_Integral -0424\RTE\DiagCfg\ComSrv\ComSrvCfg_22\App_ReadDataByIdentifierCfg.c
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/*****************************************************************************
文件名称: App_ReadDataByIdentifierCfg.c
摘 要:    该文件主要实现了22服务的DID的信息表配置 


* Version:
* - 2023-02-05  版本1  创建
*
* Revision history
* Date        Version      Author            Description
*----------  -------    -----------     -----------------------------
* 2023-02-05  1.0.00    shu.taixiao     创建
*
* Par: 其他重要信息：
*      其他重要信息说明（可选）
* Warning: 警告信息
* Par:版权信息
* Copyright (c) 2008-2023 by BYD COMPANY LIMITED. All rights reserved.
*****************************************************************************/
#include "App_ReadDataByIdentifierCfg.h"
#include "APP_DiagConfig_inc.h"
/* place your code section start */

const stS22DIDTypeStruct g_stS22DIDMsg[] =
{   //{DID,Len,fun}
    {0xF1A1u, 8u, (FunReadDataByID *)0}, /* 示例，集成时需删除 */

#if (FEATURE_APP_DISUS_A == FEATURE_ENABLE)
#include "DiSus_A_ReadDataByIdentifierCfgTbl.h"
#endif

#if (FEATURE_APP_EPB == FEATURE_ENABLE)
#include "EPB_ReadDataByIdentifierCfgTbl.h"
#endif  

#if (FEATURE_APP_ETS == FEATURE_ENABLE)
#include "ETS_ReadDataByIdentifierCfgTbl.h"
#endif  

#if (FEATURE_APP_IMS == FEATURE_ENABLE)
#include "IMS_ReadDataByIdentifierCfgTbl.h"
#endif 

#if (FEATURE_APP_EPSA == FEATURE_ENABLE)
#include "EPSA_ReadDataByIdentifierCfgTbl.h"
#endif 

#if ( FEATURE_APP_AFS == FEATURE_ENABLE )
#include "AFS_ReadDataByIdentifierCfgTbl.h"
#endif 
#if ( FEATURE_APP_VMC == FEATURE_ENABLE )
#include "VMC_ReadDataByIdentifierCfgTbl.h"
#endif 

#if ( FEATURE_APP_VSE == FEATURE_ENABLE )
#include "VSE_ReadDataByIdentifierCfgTbl.h"
#endif 
};

uint16_t get_num_S22_DID( void )
{
    return ((sizeof(g_stS22DIDMsg))/sizeof(stS22DIDTypeStruct));
}

void UDSReadDataInit(void)
{
    uint16_t i = 0;
    uint16_t DidNum = get_num_S22_DID();
    for (i = 0; i < DidNum; i++)
    {
        if(AppRegReadDataTbl(0, (stS22DIDTypeStruct*)g_stS22DIDMsg + i))
        {
            //dt_kprintf("uds read data reg err!\n");
            break;
        }
    }
}

/* place your code section end */
