/*
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2024-04-26 10:51:58
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2024-04-26 12:35:49
 * @FilePath: \Proj_D3_Integral -0424\RTE\DiagCfg\ComSrv\ComSrvCfg_22\App_ReadDataByIdentifierCfg.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/********************************************************************
* File Name: App_ReadDataByIdentifierCfg.h
* Brief: 该文件主要实现了22服务的DID的index及信息的数据结构的定义
*
* 文件详细说明
*
* Version:
*    - 2023-02-05  版本1  创建
*
*Revision history
*Date        Version      Author            Description
*----------  -------    -----------     -----------------------------
* 2023-02-05  1.0.00    shu.taixiao     创建
*
* Par: 其他重要信息：
*      其他重要信息说明（可选）
* Warning: 警告信息
* Par:版权信息
* Copyright (c) 2008-2021 by BYD COMPANY LIMITED. All rights reserved.
********************************************************************/

#ifndef APP_READDATABYIDENTIFIERCFG_H_
#define APP_READDATABYIDENTIFIERCFG_H_

#include "bsw_if.h"

extern void UDSReadDataInit(void);

#endif /* APP_READDATABYIDENTIFIERCFG_H_ */
