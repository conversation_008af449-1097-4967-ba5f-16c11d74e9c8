/*****************************************************************************
文件名称: App_WriteDataByIdentifierCfg.c
摘 要:    可根据需要修改 

其他:
函数列表:

* Version:
* - 2023-02-05  版本1  创建
*
* Revision history
* Date        Version      Author            Description
*----------  -------    -----------     -----------------------------
* 2023-02-05  1.0.00    shu.taixiao     创建
*
* Par: 其他重要信息：
*      其他重要信息说明（可选）
* Warning: 警告信息
* Par:版权信息
* Copyright (c) 2008-2023 by BYD COMPANY LIMITED. All rights reserved.
*****************************************************************************/
#include "App_WriteDataByIdentifierCfg.h"
#include "APP_DiagConfig_inc.h"
/* place your code section start */

const stS2EDIDTypeStruct g_stS2EDIDMsg[] =
{
       
//{DID,Len,fun}
{0xF1A1u, 8u, NULL}, /* 示例，集成时需删除 */

#if (FEATURE_APP_DISUS_A == FEATURE_ENABLE)
#include "DiSus_A_WriteDataByIdentifierCfgTbl.h"
#endif

#if (FEATURE_APP_EPB == FEATURE_ENABLE)
#include "EPB_WriteDataByIdentifierCfgTbl.h"
#endif  

#if (FEATURE_APP_ETS == FEATURE_ENABLE)
//#include "ETS_WriteDataByIdentifierCfgTbl.h"
#endif  

#if (FEATURE_APP_EPSA == FEATURE_ENABLE)
#include "EPSA_WriteDataByIdentifierCfgTbl.h"
#endif 

#if (FEATURE_APP_IMS == FEATURE_ENABLE)
 #include "IMS_WriteDataByIdentifierCfgTbl.h"
#endif 
};

uint16_t get_num_S2E_DID( void )
{
    return ((sizeof(g_stS2EDIDMsg))/sizeof(stS2EDIDTypeStruct));
}

void UDSWriteDataInit(void)
{
    uint16_t i = 0;
    uint16_t DidNum = get_num_S2E_DID();
    for (i = 0; i < DidNum; i++)
    {
        if(AppRegWriteDataTbl(0, (stS2EDIDTypeStruct*)g_stS2EDIDMsg + i))
        {
            break;
        }
    }
}
/* place your code section end */
