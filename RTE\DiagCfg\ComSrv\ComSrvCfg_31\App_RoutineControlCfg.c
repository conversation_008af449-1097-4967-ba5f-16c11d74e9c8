/*
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2024-04-26 10:51:58
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2024-04-26 15:11:13
 * @FilePath: \Proj_D3_Integral -0424\RTE\DiagCfg\ComSrv\ComSrvCfg_31\App_RoutineControlCfg.c
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/*****************************************************************************
文件名称: App_RoutineControlCfg.c
摘 要:    可根据需要修改 

其他:
函数列表:

* Version:
* - 2023-02-05  版本1  创建
*
* Revision history
* Date        Version      Author            Description
*----------  -------    -----------     -----------------------------
* 2023-02-05  1.0.00    shu.taixiao     创建
*
* Par: 其他重要信息：
*      其他重要信息说明（可选）
* Warning: 警告信息
* Par:版权信息
* Copyright (c) 2008-2023 by BYD COMPANY LIMITED. All rights reserved.
*****************************************************************************/
#include "APP_DiagConfig_inc.h"
#include "APP_RoutineControlCfg.h"

/* place your code section start */

// 关联DID和对应的回调函数
const t_RoutineCtrlById g_RoutineCtrlById_Table[]=
{
    /* DID            Function */
    {0x0203u, (FunRoutineCtrlByID *)0},    /* 示例，集成时需删除 */

#if (FEATURE_APP_DISUS_A == FEATURE_ENABLE)
#include "DiSus_A_RoutineControlCfgTbl.h"
#endif

#if (FEATURE_APP_EPB == FEATURE_ENABLE)
#include "EPB_RoutineControlCfgTbl.h"
#endif  

#if (FEATURE_APP_ETS == FEATURE_ENABLE)
//#include "ETS_RoutineControlCfgTbl.h"
#endif  

#if (FEATURE_APP_IMS == FEATURE_ENABLE)
// #include "IMS_RoutineControlCfgTbl.h"
#endif 

};

uint16_t get_num_S31_DID( void )
{
    return ((sizeof(g_RoutineCtrlById_Table))/sizeof(t_RoutineCtrlById));
}

void UDSRoutCtrlInit(void)
{
    uint16_t i = 0;
    uint16_t DidNum = get_num_S31_DID();
    for (i = 0; i < DidNum; i++)
    {
        if(AppRegRoutCtrlTbl(0, (t_RoutineCtrlById*)g_RoutineCtrlById_Table + i))
        {
            break;
        }
    }
}

/* place your code section end */
