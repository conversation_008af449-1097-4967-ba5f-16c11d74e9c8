﻿/*******************************************************************************
 *
 * Filename:    Features_Cfg.h
 *
 * Description:
 *  This header enable/disables the features for the overall product.
 *  All features are to be controlled by pre-configuration, ie. by pre-processor.
 *  Note that some features have dependencies on other features.
 *  All features shall be controllerd by the following way:
 *  #if (FEATURE_A == FEATURE_ENABLE )
 *  #endif
 *  OR
 *  #if (FEATURE_A == FEATURE_DISABLE )
 *  #endif
 *
 *  Please DON'T use #ifdef or #ifndef style.
 *
 * Other unit shall only include this header file
 *
 * Copyright (c) 2008-2023 by BYD COMPANY LIMITED. All rights reserved.
*******************************************************************************/

#ifndef __FEATURES_CFG_H__
#define __FEATURES_CFG_H__

/*! Feature generic definition */
#define FEATURE_ENABLE          (1u)
#define FEATURE_DISABLE         (0u)



/*! Application features  */
#define FEATURE_APP_DISUS_A        FEATURE_DISABLE
#define FEATURE_APP_EPB            FEATURE_DISABLE
#define FEATURE_APP_ETS            FEATURE_DISABLE
#define FEATURE_APP_IMS            FEATURE_DISABLE
#define FEATURE_APP_EPSA           FEATURE_DISABLE
#define FEATURE_APP_AFS            FEATURE_DISABLE
#define FEATURE_APP_VMC            FEATURE_DISABLE
#define FEATURE_APP_VSE            FEATURE_DISABLE






#endif /* __FEATURES_CFG_H__ */


