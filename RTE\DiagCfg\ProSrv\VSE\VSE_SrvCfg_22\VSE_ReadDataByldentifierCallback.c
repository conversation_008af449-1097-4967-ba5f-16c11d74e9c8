/**
 * @file VSE_ReadDataByIdCallback.c
 * <AUTHOR>
 * @brief 
 * @version 0.1
 * @date 2024-03-4
 * 
 * @copyright Copyright (c) 2024
 * 
 */


#include "Features_Cfg.h"
#if ( FEATURE_APP_VSE == FEATURE_ENABLE )

//#include "v_def.h"

#include "tp_cfg.h"
#include "comtype.h"
#include "global_info.h"
//#include "error_def.h"
//#include "App_ReadDataByIdentifierCallback.h"
//#include "ApplDesc.h"
#include "App_WriteDataByIdentifierCfg.h"
//#include "API_Drive.h"
#include "VSE_rte_diag_if.h"
#include "Platform_Types.h"

uint8 ReadDID_2712(uint8 data[], uint16 len)
{
    uint8 err = 0u;
    uint16 i;

    if (len <= 9)
    {
        for (i = 0u; i < len; i++)
        {
            data[i] = D3_VSE_SW_NUM[i];
        }
    }
    else
    {
        err = kDescNrcGeneralReject;
    }
    return err;
}

uint8 ReadDID_2713(uint8 data[], uint16 len)
{
    uint8 err = 0u;
    uint16 i;

    if (len <= 6)
    {
        for (i = 0u; i < len; i++)
        {
            data[i] = D3_VSE_PARA_CALIBRATION_VER[i];
        }
    }
    else
    {
        err = kDescNrcGeneralReject;
    }
    return err;
}
 #endif