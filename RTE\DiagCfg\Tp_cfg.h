﻿/*****************************************************************************
文件名称: Tp_cfg.h
摘 要:    可根据需要修改 

其他:
函数列表:

* Version:
*    - 2023-02-05  版本1  创建
*
*Revision history
*Date        Version      Author            Description
*----------  -------    -----------     -----------------------------
* 2023-02-05  1.0.00    shu.taixiao     创建
*
* Par: 其他重要信息：
*      其他重要信息说明（可选）
* Warning: 警告信息
* Par:版权信息
* Copyright (c) 2008-2023 by BYD COMPANY LIMITED. All rights reserved.
*****************************************************************************/
#ifndef TP_CFG_H
#define TP_CFG_H


/*******************************************************************************
                            可改变的配置   
*******************************************************************************/

/******************************************************************************
   若接收数据的最大长度为len ,须满足 DATABUFLEN>len 且 DATABUFLEN 用如下公式计算

   先令 len <= N*7 - 1   求出刚好满足条件的N,其中N为大于0的正整数
   
   然后用公式:DATABUFLEN = N*8 计算出DATABUFLEN 值
   
   例：欲使MCU能够接收32个字节， DATABUFLEN计算过程如下
   32 <= N*7-1  刚好满足条件的N=5
   所以
   DATABUFLEN = 5*8 = 40
   
   注意: MAX_DATA_LEN 设定后，DATABUFLEN是用 MAX_DATA_LEN 计算出来的。两者是关联的
   不能随意设定，否则将出错。
*******************************************************************************/

/* 应用程序中推荐设置 */
#define    MAX_DATA_LEN     300           /* 能接收的最大数据长度 */
#define    DATABUFLEN       2048          /* 接收缓冲区的大小 */
#define    DATABUFLEN_TX    2048          /* 发送缓冲区的大小(根据实际情况自定义)*/

#define    STmin            20           /*帧时间间隔 单位是ms*/
#define    FS               0            /*状态：继续发送*/
#define    BS               0            /*指定接收多少个连续帧后发送流控制帧*//*BDNT BS最大值是8*/
#define    N_As             70           /*发送方，报文发送时间*/
#define    N_Ar             70           /*接收方，报文发送时间*/
#define    N_Bs             140          /*到下一次接收流控制帧的时间*/     //企标的时间150ms，存在任务延时，设置比标准偏小
#define    N_Br             0            /*发送下一流控制帧的时间*/
#define    N_Cs             20           /*到下次发送连续帧的时间*/
#define    N_Cr             140          /*到接收下一连续帧最大等待时间*/   //企标的时间150ms，存在任务延时，设置比标准偏小
#define    N_Fs             20           /*无流控帧传输方式，发送下一帧时间*/

#define    P2client         256
#define    P2server         100
#define    P3client         256
#define    P3server         5000

#define    S3client         2000

#define    TP_PHY_ID         0x597            /* 诊断物理请求ID */
#define    TP_RSP_ID         0x59F            /* 诊断应答ID */
#define    TP_FUN_ID         0x7DF            /* 诊断功能请求ID */
#define    TP_DEBUG_ID       0x799            /* 调试用ID */

#define    TP_WITHOUT_FC       1u             /* 是否使用流控制帧 非0:使用，0:不使用 */

#define    SEC_ECU_KEY_VALUE	0x00000034

#define    UDS_CAN_CHAN       1u
#define    COM_CAN_CHAN       1u

#endif
