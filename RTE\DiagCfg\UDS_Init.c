/*
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2024-04-26 15:24:36
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2024-04-26 15:25:23
 * @FilePath: \Proj_D3_Integral -0424\RTE\DiagCfg\UDS_Init.c
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#include "App_DTCCfg.h"
#include "App_IOControlByIdentifierCfg.h"
#include "App_ReadDataByIdentifierCfg.h"
#include "App_RoutineControlCfg.h"
#include "App_WriteDataByIdentifierCfg.h"



void UDS_Init(void)
{
    UDSWriteDataInit();
    UDSIOCtrlInit();
    UDSDTCCfgInit();
    UDSReadDataInit();
    UDSRoutCtrlInit();
}

