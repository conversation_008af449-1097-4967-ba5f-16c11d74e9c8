/*****************************************************************************
文件名称: global_info.h
摘 要:    可根据需要修改 

其他:
函数列表:

* Version:
* - 2023-02-05  版本1  创建
*
* Revision history
* Date        Version      Author            Description
*----------  -------    -----------     -----------------------------
* 2023-02-05  1.0.00    shu.taixiao     创建
*
* Par: 其他重要信息：
*      其他重要信息说明（可选）
* Warning: 警告信息
* Par:版权信息
* Copyright (c) 2008-2023 by BYD COMPANY LIMITED. All rights reserved.
*****************************************************************************/
#ifndef GLOBAL_INFO_H_
#define GLOBAL_INFO_H_

#define  DIEE_PCBSN_ADDR    (0220)


#define EEP_FF94_BASE_ADRESS  	(0x17A0)     /*EEPROM地址，由用户自己定义 */
#define EEP_FF95_BASE_ADRESS  	(0x17B0)     /*EEPROM地址，由用户自己定义 */

#define EEP_FF94_SIZE     (9U)
#define EEP_FF95_SIZE     (6U)



extern const uint8 ECU_VER[6];
extern const sint8 SparePartNumber[20];      /*TBD  零件号 ASCII len 20 Byte */

 extern const uint8 APP_SW_NUM[9]; 	//app软件编码
 extern const uint8 DiSus_A_APP_SW_VER[6];  	//app软件版本
 

/* 底层软件编码 */
extern volatile uint8 BESYS_CODE[9];   

#endif /* GLOBAL_INFO_H_ */
