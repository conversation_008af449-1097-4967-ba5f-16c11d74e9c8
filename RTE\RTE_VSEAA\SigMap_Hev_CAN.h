#ifndef _SIGMAP_HEV_H_
#define _SIGMAP_HEV_H_

// CAN信号内容
#define DiDyna_LongitudinalTorque_S                                       0x0800400000000000    // 0x049, 1.0-2.7, 目标纵向制动力矩
#define Didyna_LFI_fault_status_S                                         0x0800800000000008    // 0x049, 3.0, 纵向制动力矩接口故障状态
#define DiDyna_LongitudinalTorqueRqst_S                                   0x0801000000000010    // 0x049, 3.1, 纵向制动力矩请求状态
#define DiDyna_Func_Active_S                                              0x0801800000000018    // 0x049, 3.2-3.5, DiDyna功能激活状态
#define Didyna_Sys_fault_status_S                                         0x0802000000000020    // 0x049, 4.0, DiDyna系统故障状态
#define DiDyna_Prefill_Req_S                                              0x0802800000000028    // 0x049, 4.1, 制动预充压请求标志
#define Rolling_counter_049_S                                             0x0803000000000030    // 0x049, 7.4-7.7, 0x356生命帧
#define Checksum_049_S                                                    0x0803800000000038    // 0x049, 8.0-8.7, 0x356校验码
#define LFI_Longitudinal_Torque_Interface_Function_Execution_Estimate_S   0x0000401000000040    // 0x04B, 1.0-2.7, LFI_纵向力矩接口功能执行估算值
#define LFI_Longitudinal_Torque_Interface_Function_Fault_Flag_S           0x0000800000000048    // 0x04B, 3.0, LFI_纵向力矩接口功能故障标志位
#define LFI_Longitudinal_Torque_Interface_Function_Activation_Flag_S      0x0001000000000050    // 0x04B, 3.1, LFI_纵向力矩接口功能激活标志位
#define Rolling_counter_04B_S                                             0x0001800000000058    // 0x04B, 7.4-7.7, 0x04B生命帧
#define Checksum_04B_S                                                    0x0002000000000060    // 0x04B, 8.0-8.7, 0x04B校验码
#define Tv_actv_flg_S                                                     0x0002800010000068    // 0x04F, 1.0, TV激活标志
#define Tv_err_flg_S                                                      0x0003000010000070    // 0x04F, 1.1, TV故障标志
#define Dtcs_actv_flg_S                                                   0x0003800010000078    // 0x04F, 1.2, DTCS激活标志
#define Dtcs_err_flg_S                                                    0x0004000010000080    // 0x04F, 1.3, DTCS故障标志
#define Drift_Func_OnOff_State_S                                          0x0004800010000088    // 0x04F, 1.4-1.5, 漂移功能开关状态
#define MasterCylTq_Req_04F_S                                             0x0005000010000090    // 0x04F, 1.6-1.7, 主缸控制请求状态
#define MasterCylTq_Target_S                                              0x0005c02010000098    // 0x04F, 2.0-3.7, 主缸目标制动扭矩
#define CCU_Prefill_Req_S                                                 0x00060000100000a0    // 0x04F, 4.0, 制动预充压请求标志
#define CCU_ADAS_ABA_Level_S                                              0x00068000100000a8    // 0x04F, 4.1-4.2, ADAS自适应制动辅助等级
#define CCU_ADAS_ABA_Req_S                                                0x00070000100000b0    // 0x04F, 4.3, ADAS自适应制动辅助有效位
#define CCU_System_Status_VMC                                             0x00078000100000b8    // 0x04F, 25.1-25.4, CCU制动系统状
#define Checknum_S                                                        0x00080000200000c0    // 0x051, 1.0-2.7, 校验码
#define Alive_Counter_S                                                   0x00088000200000c8    // 0x051, 3.0-4.7, Alive_counter
#define ACU_Wx_RollRate_S                                                 0x0009403020008000    // 0x051, 5.0-6.7, wx侧倾角速度
#define ACU_LongitudeAccelerationSensor_St_S                              0x00098000200000d0    // 0x051, 7.0-7.1, X方向加速度状态
#define ACU_Wx_RollRateSensor_St_S                                        0x000a0000200000d8    // 0x051, 7.2-7.3, Wx侧倾角速度状态
#define YRS_YawRateCalSts_051_S                                           0x000a8000200000e0    // 0x051, 7.4, 偏航率传感器标定状态
#define ACU_Reserved01_S                                                  0x000b0000200000e8    // 0x051, 7.5-7.7, 信号扩展预留位
#define ACU_LongitudeAcceleration_S                                       0x000bc04020008008    // 0x051, 8.0-9.7, X方向加速度
#define ACU_Reserved02_S                                                  0x000c0000200000f0    // 0x051, 10.0-10.7, 信号扩展预留位
#define ACU_LateralAcceleration_S                                         0x000cc05020008010    // 0x051, 11.0-12.7, Y方向加速
#define ACU_Wy_PitchRate_S                                                0x000d406020008018    // 0x051, 13.0-14.7, Wy俯仰角速度
#define ACU_LateralAccelerationSensor_St_S                                0x000d8000200000f8    // 0x051, 15.0-15.1, LateralAccelarationSensorState Y方向加速度状态
#define ACU_Wy_PitchRateSensor_St_S                                       0x000e000020000100    // 0x051, 15.2-15.3, Wy_俯仰角速度状
#define ACU_2_Reserved4_S                                                 0x000e800020000108    // 0x051, 15.4-17.7, 信号扩展预留
#define ACU_VerticalAcceleration_S                                        0x000f407020008020    // 0x051, 18.0-19.7, Z方向加速
#define ACU_YawRate_S                                                     0x000fc08020008028    // 0x051, 20.0-21.7, Wz横摆角速
#define ACU_VerticalAccelerationSensor_St_S                               0x0010000020000110    // 0x051, 22.0-22.1, Z方向加速度状态
#define ACU_YawRateSensor_St_S                                            0x0010800020000118    // 0x051, 22.2-22.3, Wz横摆角速度状态
#define Reserved_S                                                        0x0011000020000120    // 0x051, 22.4-32.7, Reserved
#define Suspension_Motor_Switching_s_S                                    0x0804000010000128    // 0x05D, 1.0-1.1, 悬架泵电机开关信号
#define Suspension_Motor_Switching_Status_s_S                             0x0804800010000130    // 0x05D, 1.2, 悬架泵电机开关信号有效位
#define Rolling_Counter_05D_S                                             0x0805000010000138    // 0x05D, 7.4-7.7, 计数器
#define SRS_Collosion_Signal_S                                            0x1011800030000140    // 0x08C, 1.0-1.7, SRS_碰撞信号
#define Collosion_Signal_Checksum_S                                       0x1012000030000148    // 0x08C, 2.0-2.7, 碰撞信号校验码
#define Loss_Control_Loop_Counter_08C_S                                   0x1012800030000150    // 0x08C, 8.4-8.7, 丢包控制循环计数器_08C
#define DiSus_Cloud_UpDown_S                                              0x2013000048000158    // 0x0A2, 2.0-2.2, 手机云悬架高度调节
#define CRC_Checknum_0AB_S                                                0x0013800050000160    // 0x0AB, 1.0-2.7, 校验码0AB
#define Alive_Counter_0AB_S                                               0x0014000050000168    // 0x0AB, 3.0-4.7, 滚动循环计数器0AB
#define RCS_EPSAD_ScrewDisplacement_Req_S                                 0x0014800050000170    // 0x0AB, 5.0-5.2, RCS工作状态
#define RCS_EPSAD_Request_Type_S                                          0x0015000050000178    // 0x0AB, 5.3-5.4, 遥控后转控制类型
#define RCS_EPSAD_SteeringCtrlReqForEPSAD_S                               0x0015800050000180    // 0x0AB, 5.5, 遥控后转请求状态位
#define RCS_EPSAD_SteeringCtrlReqForEPSAD_VD_S                            0x0016000050000188    // 0x0AB, 5.6, 遥控驾驶对后轮角度请求有效位
#define RCS_Tar_StrAgl_S                                                  0x0016c09050000190    // 0x0AB, 6.0-7.7, 遥控驾驶对后轮角度请求
#define L_CRC_Checknum_0B6_S                                              0x0017000060000198    // 0x0B6, 1.0-2.7, 左后轮校验码L_0B6
#define L_Alive_Counter_0B6_S                                             0x00178000600001a0    // 0x0B6, 3.0-4.7, 左后轮滚动循环计数器L_0B6
#define L_RWS_Status_S                                                    0x00180000600001a8    // 0x0B6, 5.0-5.2, 左后轮转向系统状态
#define L_RWS_Ctrl_Status_S                                               0x00188000600001b0    // 0x0B6, 5.3-5.4, 左后轮转向系统控制状态
#define L_RWS_Ctrl_Status_VD_S                                            0x00190000600001b8    // 0x0B6, 5.5, 左后轮转向系统控制状态有效性
#define L_RWS_Screw_Displacement_VD_S                                     0x00198000600001c0    // 0x0B6, 5.6, 左后轮实际丝杆位移量有效性
#define L_RWS_Screw_Displacement_Speed_VD_S                               0x001a0000600001c8    // 0x0B6, 5.7, 左后轮丝杆位移速度有效性
#define L_RWS_Screw_Displacement_S                                        0x001ac0a0600001d0    // 0x0B6, 6.0-7.3, 左后轮实际丝杆位移量
#define L_RWS_Screw_Displacement_Speed_S                                  0x001b40b0600001d8    // 0x0B6, 7.4-8.7, 左后轮丝杆位移速度
#define L_End_Protection_Value_S                                          0x001b8000600001e0    // 0x0B6, 9.0-9.7, 左后轮末端保护值
#define L_RWS_Motor_Rotation_Num_S                                        0x001c40c0600001e8    // 0x0B6, 10.0-11.7, 左后轮转向电机转动圈数
#define L_RWS_Motor_Rotation_S                                            0x001c8000600001f0    // 0x0B6, 12.0-13.7, 左后轮转向电机转速
#define L_RWS_ECU_Temperature_S                                           0x001d0000600001f8    // 0x0B6, 14.0-15.3, 左后轮转向ECU温度
#define L_Supply_Voltage_S                                                0x001dc0d060000200    // 0x0B6, 15.4-16.7, 左后轮供电电压
#define L_Line_Sensor_Value_S                                             0x001e000060000208    // 0x0B6, 18.0-19.3, 左后轮直线位移传感器值
#define L_RWS_Fail_Grade_S                                                0x001e800060000210    // 0x0B6, 19.4-19.5, 左后轮转向系统故障等级
#define L_RWS_Alarm_Light_S                                               0x001f000060000218    // 0x0B6, 19.6, 左后轮转向报警灯信号
#define L_ECU_Software_Version_S                                          0x001f800060000220    // 0x0B6, 20.0-21.7, 左后轮系统集成软件版本号
#define L_ECU_Software_Year_S                                             0x0020000060000228    // 0x0B6, 22.0-22.7, 左后轮软件版本年份
#define L_ECU_Software_Month_S                                            0x0020800060000230    // 0x0B6, 23.0-23.7, 左后轮软件版本月份
#define L_ECU_Software_Day_S                                              0x0021000060000238    // 0x0B6, 24.0-24.7, 左后轮软件版本日
#define L_ECU_Software_Number_S                                           0x0021800060000240    // 0x0B6, 25.0-25.7, 左后轮软件版本修改次数
#define L_RWS_Fault_Mask_S                                                0x0022000060000248    // 0x0B6, 26.0-29.7, 左后轮故障掩码
#define L_Motor_Q_Current_S                                               0x0022800060000250    // 0x0B6, 30.0-31.3, 左后轮电机Q轴电流值
#define L_Motor_D_Current_S                                               0x0023000060000258    // 0x0B6, 31.4-32.7, 左后轮电机D轴电流值
#define R_CRC_Checknum_0C0_S                                              0x0023800070000260    // 0x0C0, 1.0-2.7, 右后轮校验码R_0C0
#define R_Alive_Counter_0C0_S                                             0x0024000070000268    // 0x0C0, 3.0-4.7, 右后轮滚动循环计数器R_0C0
#define R_RWS_Status_S                                                    0x0024800070000270    // 0x0C0, 5.0-5.2, 右后轮转向系统状态
#define R_RWS_Ctrl_Status_S                                               0x0025000070000278    // 0x0C0, 5.3-5.4, 右后轮转向系统控制状态
#define R_RWS_Ctrl_Status_VD_S                                            0x0025800070000280    // 0x0C0, 5.5, 右后轮转向系统控制状态有效性
#define R_RWS_Screw_Displacement_VD_S                                     0x0026000070000288    // 0x0C0, 5.6, 右后轮实际丝杆位移量有效性
#define R_RWS_Screw_Displacement_Speed_VD_S                               0x0026800070000290    // 0x0C0, 5.7, 右后轮丝杆位移速度有效性
#define R_RWS_Screw_Displacement_S                                        0x002740e070000298    // 0x0C0, 6.0-7.3, 右后轮实际丝杆位移量
#define R_RWS_Screw_Displacement_Speed_S                                  0x0027c0f0700002a0    // 0x0C0, 7.4-8.7, 右后轮丝杆位移速度
#define R_End_Protection_Value_S                                          0x00280000700002a8    // 0x0C0, 9.0-9.7, 右后轮末端保护值
#define R_RWS_Motor_Rotation_Num_S                                        0x0028c100700002b0    // 0x0C0, 10.0-11.7, 右后轮转向电机转动圈数
#define R_RWS_Motor_Rotation_S                                            0x00290000700002b8    // 0x0C0, 12.0-13.7, 右后轮转向电机转速
#define R_RWS_ECU_Temperature_S                                           0x00298000700002c0    // 0x0C0, 14.0-15.3, 右后轮转向ECU温度
#define R_Supply_Voltage_S                                                0x002a4110700002c8    // 0x0C0, 15.4-16.7, 右后轮供电电压
#define R_Line_Sensor_Value_S                                             0x002a8000700002d0    // 0x0C0, 18.0-19.3, 右后轮直线位移传感器值
#define R_RWS_Fail_Grade_S                                                0x002b0000700002d8    // 0x0C0, 19.4-19.5, 右后轮转向系统故障等级
#define R_RWS_Alarm_Light_S                                               0x002b8000700002e0    // 0x0C0, 19.6, 右后轮转向报警灯信号
#define R_ECU_Software_Version_S                                          0x002c0000700002e8    // 0x0C0, 20.0-21.7, 右后轮系统集成软件版本号
#define R_ECU_Software_Year_S                                             0x002c8000700002f0    // 0x0C0, 22.0-22.7, 右后轮软件版本年份
#define R_ECU_Software_Month_S                                            0x002d0000700002f8    // 0x0C0, 23.0-23.7, 右后轮软件版本月份
#define R_ECU_Software_Day_S                                              0x002d800070000300    // 0x0C0, 24.0-24.7, 右后轮软件版本日
#define R_ECU_Software_Number_S                                           0x002e000070000308    // 0x0C0, 25.0-25.7, 右后轮软件版本修改次数
#define R_RWS_Fault_Mask_S                                                0x002e800070000310    // 0x0C0, 26.0-29.7, 右后轮故掩码
#define R_Motor_Q_Current_S                                               0x002f000070000318    // 0x0C0, 30.0-31.3, 右后轮电机Q轴电流
#define R_Motor_D_Current_S                                               0x002f800070000320    // 0x0C0, 31.4-32.7, 右后轮电机D轴电流
#define Suspension_Pump_Motor_Drive_Fault_Signal_S                        0x0030000080000328    // 0x0C4, 3.0-3.2, 悬架泵电机驱动故障信号
#define Suspension_Pump_Motor_Working_Status_Signal_S                     0x0030800080000330    // 0x0C4, 3.3-3.4, 悬架泵电机工作状态信号
#define Disus_Signal_Input_Status_S                                       0x0031000080000338    // 0x0C4, 3.5-3.6, Disus信号输入状态
#define DisusASU_working_current_S                                        0x0031c12080000340    // 0x0C4, 4.0-5.7, DisusASU工作电流
#define Rolling_Counter_0C4_S                                             0x0032000080000348    // 0x0C4, 7.4-7.7, 计数器
#define u16RodStroke_V                                                    0x0032c13090000350    // 0x0D5, 1.0-2.1, 制动踏板行程
#define bBrkRodStroke_S                                                   0x0033000090000358    // 0x0D5, 2.2, 制动踏板行程有效位
#define IPB_u8Status                                                      0x0033800090000360    // 0x0D5, 3.2-3.4, IPB状态信号MBB
#define IPB_Supplier                                                      0x0034000090000368    // 0x0D5, 4.7, IPB_Supplier
#define Decelerate_Demand                                                 0x0034800090000370    // 0x0D5, 5.0-5.7, 减速度需求
#define Brake_Req_Status                                                  0x0035000090000378    // 0x0D5, 7.0, 制动请求指令
#define Rolling_Counter_0x0D5                                             0x0035800090000380    // 0x0D5, 7.4-7.7, 滚动计数校验
#define CRC_Checknum_0F4_S                                                0x00360000a0000388    // 0x0F4, 1.0-2.7, 0x0F4校验码
#define Alive_Counter_0F4_S                                               0x00368000a0000390    // 0x0F4, 3.0-4.7, 0x0F4生命帧
#define OK_Lamp                                                           0x00370000a0000398    // 0x0F4, 16.4-16.5, OK指示灯
#define VCU_Goal_Gear_S                                                   0x00378000a00003a0    // 0x0F4, 17.0-17.3, 目标挡位
#define TCS_Active_S                                                      0x00380000a00003a8    // 0x0F4, 19.2, CCU_DTCS激活标志
#define TCS_Fault_0F4_S                                                   0x00388000a00003b0    // 0x0F4, 19.3, CCU_DTCS故障标志
#define CCU_EPBBrReq_S                                                    0x00390000a00003b8    // 0x0F4, 20.6-20.7, CCU请求EPB状态
#define Veh_drv_mod_S                                                     0x00398000a00003c0    // 0x0F4, 21.0-21.7, 整车驾驶模式
#define CCU_Vehicle_DrivingMode_Valid_S                                   0x003a0000a00003c8    // 0x0F4, 22.0, 整车驾驶模式有效标志
#define CCU_RddtbrakeActive_S                                             0x003a8000a00003d0    // 0x0F4, 22.3, 备份制动激活标志
#define CCU_EPBBrReq_VD_S                                                 0x003b0000a00003d8    // 0x0F4, 24.1, 请求EPB状态有效标志
#define VOT_Actv_Sat                                                      0x003b8000a00003e0    // 0x0F4, 24.7, VOT功能激活标志
#define CCU_System_Status_S                                               0x003c0000a00003e8    // 0x0F4, 25.1-25.4, CCU制动系统状态
#define VehicleHold_Active                                                0x003c8000a00003f0    // 0x0F4, 26.7, 静止保持功能激活
#define VOT_PreReq_S                                                      0x003d0000a00003f8    // 0x0F4, 35.6, VOT保压功能激活标志
#define Drift_Func_OnOff_State                                            0x003d8000a0000400    // 0x0F4, 46.3-46.4, 漂移功能开/关状态
#define CRC_Checknum_0FC_S                                                0x003e0000b0000408    // 0x0FC, 1.0-2.7, CRC_Checknum_0FC_S
#define Alive_Counter_0FC_S                                               0x003e8000b0000410    // 0x0FC, 3.0-4.7, Alive_counter_0FC
#define RL_mot_whl_tar_tq_S                                               0x003f0000b0000418    // 0x0FC, 9.0-10.7, 左后电机轮端目标扭矩
#define RR_mot_whl_tar_tq_S                                               0x003f8000b0000420    // 0x0FC, 11.0-12.7, 右后电机轮端目标扭矩
#define Vehicle_Torque_FL_0FC_S                                           0x00400000b0000428    // 0x0FC, 13.0-14.7, 左前电机实际扭矩
#define Vehicle_Torque_FR_0FC_S                                           0x00408000b0000430    // 0x0FC, 15.0-16.7, 右前电机实际扭矩
#define Vehicle_Torque_RL_0FC_S                                           0x00410000b0000438    // 0x0FC, 17.0-18.7, 左后电机实际扭矩
#define Vehicle_Torque_RR_0FC_S                                           0x00418000b0000440    // 0x0FC, 19.0-20.7, 右后电机实际扭矩
#define RL_mot_whl_tar_tq_efc_flg_S                                       0x00420000b0000448    // 0x0FC, 21.2, 左后电机轮端目标扭矩有效标志
#define RR_mot_whl_tar_tq_efc_flg_S                                       0x00428000b0000450    // 0x0FC, 21.3, 右后电机轮端目标扭矩有效标志
#define Torque_State_FL_0FC_S                                             0x00430000b0000458    // 0x0FC, 21.4, 左前电机实际扭矩有效标志
#define Torque_State_FR_0FC_S                                             0x00438000b0000460    // 0x0FC, 21.5, 右前电机实际扭矩有效标志
#define Torque_State_RL_0FC_S                                             0x00440000b0000468    // 0x0FC, 21.6, 左后电机实际扭矩有效标志
#define Torque_State_RR_0FC_S                                             0x00448000b0000470    // 0x0FC, 21.7, 右后电机实际扭矩有效标志
#define RL_mot_spd_S                                                      0x00450000b0000478    // 0x0FC, 26.0-27.7, 左后电机转速
#define RR_mot_spd_S                                                      0x00458000b0000480    // 0x0FC, 28.0-29.7, 右后电机转速
#define RL_mot_spd_efc_flg_S                                              0x00460000b0000488    // 0x0FC, 30.2, 左后电机转速有效标志
#define RR_mot_spd_efc_flg_S                                              0x00468000b0000490    // 0x0FC, 30.3, 右后电机转速有效标志
#define whl_spd_FL_S                                                      0x00470000b0000498    // 0x0FC, 30.4-31.7, IPB左前轮速
#define whl_spd_FR_S                                                      0x00478000b00004a0    // 0x0FC, 32.0-33.3, IPB右前轮速
#define whl_spd_RL_S                                                      0x00480000b00004a8    // 0x0FC, 33.4-34.7, IPB左后轮速
#define whl_spd_RR_S                                                      0x00488000b00004b0    // 0x0FC, 35.0-36.3, IPB右后轮速
#define FL_whl_spd_efc_flg_S                                              0x00490000b00004b8    // 0x0FC, 36.4, IPB左前轮速有效标志
#define FR_whl_spd_efc_flg_S                                              0x00498000b00004c0    // 0x0FC, 36.5, IPB右前轮速有效标志
#define RL_whl_spd_efc_flg_S                                              0x004a0000b00004c8    // 0x0FC, 36.6, IPB左后轮速有效标志
#define RR_whl_spd_efc_flg_S                                              0x004a8000b00004d0    // 0x0FC, 36.7, IPB右后轮速有效标志
#define CRC_Checknum_0FE_S                                                0x0805800020008030    // 0x0FE, 1.0-2.7, 校验码0FE
#define Alive_Counter_0FE_S                                               0x0806000020008038    // 0x0FE, 3.0-4.7, 滚动循环计数器0FE
#define EPSA_Con_Status_RWS_S                                             0x08068000200004d8    // 0x0FE, 5.0-5.1, EPSA对RWS执行器的控制状态
#define EPSA_Con_Status_VD_RWS_S                                          0x08070000200004e0    // 0x0FE, 5.2, EPSA对RWS执行器控制状态有效性
#define EPSA_Screw_Disp_Request_RWS_S                                     0x08078000200004e8    // 0x0FE, 5.3, EPSA对RWS执行器丝杆位移控制请求
#define EPSA_Screw_Disp_Request_VD_RWS_S                                  0x08080000200004f0    // 0x0FE, 5.4, EPSA对RWS执行器丝杆位移控制请求有效性
#define EPSA_Screw_Disp_Demand_RWS_S                                      0x0808c140200004f8    // 0x0FE, 6.0-7.7, EPSA对RWS执行器丝杆位移请求
#define EPSA_Screw_Disp_Demand_VD_RWS_S                                   0x0809000020000500    // 0x0FE, 8.0, EPSA对RWS执行器丝杆位移请求有效性
#define EPSA_Work_Configuration_S                                         0x0809800020000508    // 0x0FE, 8.1, 后轮转向控制开关配置
#define EPSA_Work_Signal_S                                                0x080a000020000510    // 0x0FE, 8.2-8.3, 后轮转向控制开关信号
#define EPSA_CrabWalk_Configuration_S                                     0x080a800020000518    // 0x0FE, 8.4, 蟹行模式控制开关配置
#define EPSA_CrabWalk_Signal_S                                            0x080b000020000520    // 0x0FE, 8.5-8.6, 蟹行模式控制开关信号
#define EPSA_Error_Signal_S                                               0x080b800020000528    // 0x0FE, 9.0-9.2, 后轮转向相关功能异常信号
#define EPSA_Gray_Signal_S                                                0x080c000020000530    // 0x0FE, 9.3, 后轮转向相关按钮置灰信号
#define EPSA_Out_Mon_bHandSAbnormalFault                                  0x080c800020000538    // 0x0FE, 9.4, 握手状态异常
#define EPSA_Out_Mon_bHandSTimeoutFault                                   0x080d000020000540    // 0x0FE, 9.5, 握手超时故障
#define EPSA_Out_Mon_bFunEnaFault1                                        0x080d800020000548    // 0x0FE, 9.6, 握手失败功能异常使能故障
#define EPSA_Out_Mon_bFunEnaFault2                                        0x080e000020000550    // 0x0FE, 9.7, 多功能异常使能故障
#define EPSA_Out_Mon_bFunEnaCntFault                                      0x080e800020000558    // 0x0FE, 10.0, 功能使能计数异常故障
#define EPSA_Out_Mon_bADASFunEnaFault                                     0x080f000020000560    // 0x0FE, 10.1, ADAS异常使能状态位
#define EPSA_Out_Mon_bArbiAngDisableFault                                 0x080f800020000568    // 0x0FE, 10.2, 转角归零仲裁故障状态位
#define EPSA_Out_Mon_bArbiAngLimitFault                                   0x0810000020000570    // 0x0FE, 10.3, 转角变化速率超限仲裁故障状态位
#define EPSA_Out_Mon_bArbiAngAmpFault                                     0x0810800020000578    // 0x0FE, 10.4, 转角阈值仲裁故障状态位
#define EPSA_Out_Mon_bAngDirFail                                          0x0811000020000580    // 0x0FE, 10.5, 高速转角方向异常故障状态位
#define EPSA_Out_Mon_bAngExecuteFault                                     0x0811800020000588    // 0x0FE, 10.6, 后轮转角执行故障状态位
#define EPSA_Out_Mon_bAngRateRespFault                                    0x0812000020000590    // 0x0FE, 10.7, 后轮转角控制反向故障状态位
#define EPSA_Out_Mon_bRWSComLossFault                                     0x0812800020000598    // 0x0FE, 11.0, EPSA与RWS通信丢失
#define EPSA_Out_Mon_bBCMComLossFault                                     0x08130000200005a0    // 0x0FE, 11.1, EPSA与BCM通信丢失
#define EPSA_Out_Mon_bVCUComLossFault                                     0x08138000200005a8    // 0x0FE, 11.2, EPSA与VCU通信丢失
#define EPSA_Out_Mon_bEPSComLossFault                                     0x08140000200005b0    // 0x0FE, 11.3, EPSA与EPS通信丢失
#define EPSA_Out_Mon_bIPBComLossFault                                     0x08148000200005b8    // 0x0FE, 11.4, EPSA与IPB通信丢失
#define EPSA_Out_Mon_bSCUComLossFault                                     0x08150000200005c0    // 0x0FE, 11.5, EPSA与SCU通信丢失
#define EPSA_Out_Mon_bADASComLossFault                                    0x08158000200005c8    // 0x0FE, 11.6, EPSA与ADAS通信丢失
#define EPSA_Out_Mon_bSRSComLossFault                                     0x08160000200005d0    // 0x0FE, 11.7, EPSA与SRS通信丢失
#define EPSA_Out_Mon_bRWSComLossFault_L                                   0x08168000200005d8    // 0x0FE, 12.0, EPSA与左侧后轮转向器丢失通信
#define EPSA_Out_Mon_bRWSComLossFault_R                                   0x08170000200005e0    // 0x0FE, 12.1, EPSA与右侧后轮转向器丢失通信
#define EPSA_Out_Mon_bRCSComLossFault                                     0x08178000200005e8    // 0x0FE, 12.2, EPSA与RCS通信丢失
#define EPSA_Out_Mon_bCCUComLossFault                                     0x08180000200005f0    // 0x0FE, 12.3, EPSA与CCU通信丢失
#define EPSA_Out_Mon_bConfigBitLossFault                                  0x08188000200005f8    // 0x0FE, 13.0, EPSA配置字丢失
#define EPSA_Out_Mon_bEPSA_Self_Fault                                     0x0819000020000600    // 0x0FE, 13.1, EPSA安全机制自检故障
#define EPSA_Con_Status_RWS_L_S                                           0x0819800020000608    // 0x0FE, 15.0-15.1, EPSA对RWS左执行器的控制状态
#define EPSA_Con_Status_VD_RWS_L_S                                        0x081a000020000610    // 0x0FE, 15.2, EPSA对RWS左执行器控制状态有效性
#define EPSA_Screw_Disp_Request_RWS_L_S                                   0x081a800020000618    // 0x0FE, 15.3, EPSA对RWS左执行器丝杆位移控制请求
#define EPSA_Screw_Disp_Request_VD_RWS_L_S                                0x081b000020000620    // 0x0FE, 15.4, EPSA对RWS左执行器丝杆位移控制请求有效性
#define EPSA_Screw_Disp_Demand_VD_RWS_L_S                                 0x081b800020000628    // 0x0FE, 15.5, EPSA对RWS左执行器丝杆位移请求有效性
#define EPSA_Screw_Disp_Demand_RWS_L_S                                    0x081c415020000630    // 0x0FE, 16.0-17.7, EPSA对RWS左执行器丝杆位移请求
#define EPSA_Con_Status_RWS_R_S                                           0x081c800020000638    // 0x0FE, 19.0-19.1, EPSA对RWS右执行器的控制状态
#define EPSA_Con_Status_VD_RWS_R_S                                        0x081d000020000640    // 0x0FE, 19.2, EPSA对RWS右执行器控制状态有效性
#define EPSA_Screw_Disp_Request_RWS_R_S                                   0x081d800020000648    // 0x0FE, 19.3, EPSA对RWS右执行器丝杆位移控制请求
#define EPSA_Screw_Disp_Request_VD_RWS_R_S                                0x081e000020000650    // 0x0FE, 19.4, EPSA对RWS右执行器丝杆位移控制请求有效性
#define EPSA_Screw_Disp_Demand_VD_RWS_R_S                                 0x081e800020000658    // 0x0FE, 19.5, EPSA对RWS右执行器丝杆位移请求有效性
#define EPSA_Screw_Disp_Demand_RWS_R_S                                    0x081f416020000660    // 0x0FE, 20.0-21.7, EPSA对RWS右执行器丝杆位移请求
#define EPSA_Security_Detect                                              0x081f800020000668    // 0x0FE, 22.0-22.2, EPSA安全机制检测
#define EPSA_ActFcnMod                                                    0x0820000020000670    // 0x0FE, 22.3-22.6, EPSA实际功能模式
#define CRC_Checknum_109_S                                                0x0820800030000678    // 0x109, 1.0-2.7, CRC_Checknum_109_S
#define Alive_Counter_109_S                                               0x0821000030000680    // 0x109, 3.0-4.7, Alive_Counter_109_S
#define DiSus_Actual_Height_Invalid_FL_S                                  0x0821800030000688    // 0x109, 9.0, 左前悬架实际高度无效
#define DiSus_Actual_Height_Invalid_FR_S                                  0x0822000030000690    // 0x109, 9.1, 右前悬架实际高度无效
#define DiSus_Actual_Height_Invalid_RL_S                                  0x0822800030000698    // 0x109, 9.2, 左后悬架实际高度无效
#define DiSus_Actual_Height_Invalid_RR_S                                  0x08230000300006a0    // 0x109, 9.3, 右后悬架实际高度无效
#define DiSus_Mode_Adjust_Status_S                                        0x08238000300006a8    // 0x109, 9.4, 悬架可调节状态
#define DiSus_Mode_Execute_Status_S                                       0x08240000300006b0    // 0x109, 9.5-9.7, 悬架调节状态
#define DiSus_Actual_Driving_Mode_S                                       0x08248000300006b8    // 0x109, 10.0-10.7, 悬架实际驾驶模式 （集成爆胎模式）
#define DiSus_Actual_Height_FL_S                                          0x08254170300006c0    // 0x109, 11.0-12.7, 左前悬架实际高度
#define DiSus_Actual_Height_FR_S                                          0x0825c180300006c8    // 0x109, 13.0-14.7, 右前悬架实际高度
#define DiSus_Actual_Height_RL_S                                          0x08264190300006d0    // 0x109, 15.0-16.7, 左后悬架实际高度
#define DiSus_Actual_Height_RR_S                                          0x0826c1a0300006d8    // 0x109, 17.0-18.7, 右后悬架实际高度
#define IMU_Ax_S                                                          0x082741b0300006e0    // 0x109, 19.0-20.7, IMU_Ax
#define IMU_Ay_S                                                          0x0827c1c0300006e8    // 0x109, 21.0-22.7, IMU_Ay
#define IMU_Az_S                                                          0x082841d0300006f0    // 0x109, 23.0-24.7, IMU_Az
#define IMU_RollRate_S                                                    0x0828c1e0300006f8    // 0x109, 25.0-26.7, IMU_RollRate
#define IMU_PitchRate_S                                                   0x082941f030000700    // 0x109, 27.0-28.7, IMU_PitchRate
#define IMU_YawRate_S                                                     0x0829c20030000708    // 0x109, 29.0-30.7, IMU_YawRate
#define DiSus_Type_S                                                      0x082a000030000710    // 0x109, 35.0-35.3, DiSus的类型区分
#define DiSus_Pitch_Angle                                                 0x082ac21030000718    // 0x109, 35.4-36.6, 车身俯仰角
#define DiSus_Roll_Angle                                                  0x082b422030000720    // 0x109, 36.7-38.1, 车身侧倾角
#define DiSus_Ground_Clearance                                            0x082b800030000728    // 0x109, 38.2-39.3, 离地间隙
#define DiSus_Vehicle_Height                                              0x082c000030000730    // 0x109, 39.4-40.7, 车身高度
#define Inspector_Damper_FL_S                                             0x082c800030000738    // 0x109, 41.0-42.7, 左前线性阀输出电流
#define Inspector_Damper_FR_S                                             0x082d000030000740    // 0x109, 43.0-44.7, 右前线性阀输出电流
#define Inspector_Damper_RL_S                                             0x082d800030000748    // 0x109, 45.0-46.7, 左后线性阀输出电流
#define Inspector_Damper_RR_S                                             0x082e000030000750    // 0x109, 47.0-48.7, 右后线性阀输出电流
#define Inspector_APP_DriveSig_S                                          0x082e800030000758    // 0x109, 49.0-50.7, ASW高度执行器控制信号
#define Inspector_ECUMode_109_S                                           0x082f000030000760    // 0x109, 51.0-51.3, ECU模式
#define Inspector_SJ_ID_S                                                 0x082f800030000768    // 0x109, 51.4-52.3, 悬架抑制类型2
#define Inspector_PressureSig_109_S                                       0x0830000030000770    // 0x109, 52.4-53.0, 悬架压力信号 单位bar
#define Inspector_TempSig_109_S                                           0x0830800030000778    // 0x109, 53.1-54.1, 悬架温度信号 PH=INT-200摄氏度
#define Inspector_SBAD_ID_109_S                                           0x0831000030000780    // 0x109, 54.2-55.1, 悬架高度抑制类型
#define Inspector_Error_ID_109_S                                          0x0831800030000788    // 0x109, 55.2-57.1, ASW故障ID
#define Inspector_ActorOutSig_S                                           0x0832000030000790    // 0x109, 58.0-59.7, 所有执行器输出信号
#define AFS_Pressure_109_S                                                0x0832800030000798    // 0x109, 60.0-61.7, 侧翼储气罐压力信号
#define AFS_Ctr_109_S                                                     0x08330000300007a0    // 0x109, 62.0-62.7, 侧翼补气请求
#define Alive_counter_10C_S                                               0x004b0000c00007a8    // 0x10C, 3.0-4.7, 滚动计数
#define IPB_Vehicle_Speed_10C_S                                           0x004bc230c00007b0    // 0x10C, 5.3-6.6, Vehicle_speed
#define Vehicle_Speed_Stats_10C_S                                         0x004c0000c00007b8    // 0x10C, 6.7, VehicleSpeed_Status
#define Wheel_Speed_FL_S                                                  0x004cc240c00007c0    // 0x10C, 7.0-8.3, WheelSpeed_FL
#define Wheel_Speed_FR_S                                                  0x004d4250c00007c8    // 0x10C, 8.4-9.7, WheelSpeed_FR
#define Wheel_Speed_RL_S                                                  0x004dc260c0008040    // 0x10C, 10.0-11.3, WheelSpeed_RL
#define Wheel_Speed_RR_S                                                  0x004e4270c0008048    // 0x10C, 11.4-12.7, WheelSpeed_RR
#define Wheel_Speed_FR_Stats_S                                            0x004e8000c00007d0    // 0x10C, 13.0, WheelSpeed_FR_Status
#define Wheel_Speed_FL_Stats_S                                            0x004f0000c00007d8    // 0x10C, 13.1, WheelSpeed_FL_Status
#define Wheel_Speed_RR_Stats_S                                            0x004f8000c00007e0    // 0x10C, 13.2, WheelSpeed_RR_Status
#define WheelSpeed_RL_Status_S                                            0x00500000c00007e8    // 0x10C, 13.3, WheelSpeed_RL_Status
#define EBD_Active_10C_S                                                  0x00508000c00007f0    // 0x10C, 20.4, EBD_Active
#define EBD_Fault_10C_S                                                   0x00510000c00007f8    // 0x10C, 20.5, EBD_Fault
#define ABS_Active_10C_S                                                  0x00518000c0000800    // 0x10C, 20.6, ABS_Active
#define ABS_Fault_10C_S                                                   0x00520000c0000808    // 0x10C, 20.7, ABS_Fault
#define VDC_Active_10C_S                                                  0x00528000c0000810    // 0x10C, 21.0, VDCActive
#define VDC_Fault_10C_S                                                   0x00530000c0000818    // 0x10C, 21.1, VDC_Fault
#define Brake_Pedal_Status_10C_S                                          0x00538000c0000820    // 0x10C, 44.5-44.6, IPB_BRAKE_PEDAL_STARUS
#define IPB_Plunger_Pres_status_10C_S                                     0x00540000c0000828    // 0x10C, 46.4, IPB_PlungerPressure_status
#define IPB_Plunger_Pressure_10C_S                                        0x0054c280c0000830    // 0x10C, 46.6-48.1, IPB_PlungerPressure Plunger压力
#define ECM_Engine_Rev10D_S                                               0x00554290d0000838    // 0x10D, 1.0-2.7, 发动机转速
#define Engine_Indicated_Trq_S                                            0x0055c2a0e0000840    // 0x10E, 1.0-1.7, 发动机指示扭矩
#define CRC_Checknum_112_S                                                0x0833800040008050    // 0x112, 1.0-2.7, 校验码112
#define Alive_Counter_112_S                                               0x0834000040008058    // 0x112, 3.0-4.7, 滚动循环计数器112
#define EPSA_Angle_S                                                      0x0834c2b040000848    // 0x112, 5.0-6.2, 后轮实际转动角度
#define EPSA_Angle_VD_S                                                   0x0835000040000850    // 0x112, 6.3, 后轮实际转动角度有效性
#define EPSA_AngularVelocity_S                                            0x0835c2c040000858    // 0x112, 6.4-7.3, 后轮转动角速度
#define EPSA_Controller_Status_ADAS_S                                     0x0836000040000860    // 0x112, 7.4-7.6, EPSA控制器状态
#define EPSA_Controller_Status_VD_ADAS_S                                  0x0836800040000868    // 0x112, 7.7, EPSA控制器状态有效性
#define EPSA_LockCondition_ADAS_S                                         0x0837000040000870    // 0x112, 8.0, 后轮锁止状态
#define EPSA_Controller_Status_RCS_S                                      0x0837800040000878    // 0x112, 10.0-10.2, EPSA控制器与RCS交互控制状态
#define EPSA_Controller_Status_VD_RCS_S                                   0x0838000040000880    // 0x112, 10.3, EPSA控制器与RCS交互控制状态有效性
#define EPSA_LockCondition_RCS_S                                          0x0838800040000888    // 0x112, 10.4, 后轮RCS锁止状态
#define Vehicle_speed                                                     0x005642d0f0000890    // 0x116, 7.6-9.2, Vehicle_speed
#define WheelSpeed_FL                                                     0x0056c2e0f0000898    // 0x116, 9.3-10.7, WheelSpeed_FL
#define WheelSpeed_FR                                                     0x005742f0f00008a0    // 0x116, 11.0-12.4, WheelSpeed_FR
#define WheelSpeed_RL                                                     0x0057c300f00008a8    // 0x116, 12.5-14.1, WheelSpeed_RL
#define WheelSpeed_RR                                                     0x00584310f00008b0    // 0x116, 14.2-15.6, WheelSpeed_RR
#define WheelSpeed_FL_Status                                              0x00588000f00008b8    // 0x116, 15.7 , WheelSpeed_FL_Status
#define WheelSpeed_FR_Status                                              0x00590000f00008c0    // 0x116, 16.0 , WheelSpeed_FR_Status
#define WheelSpeed_RL_Status                                              0x00598000f00008c8    // 0x116, 16.1 , WheelSpeed_RL_Status
#define WheelSpeed_RR_Status                                              0x005a0000f00008d0    // 0x116, 16.2 , WheelSpeed_RR_Status
#define VDC_Actual_Style_State                                            0x005a8000f00008d8    // 0x116, 16.3-16.4, VDC实际风格状态
#define Rolling_Counter_116_S                                             0x005b0000f00008e0    // 0x116, 63.4-63.7, 滚动计数器
#define Steering_Wheel_Angle_S                                            0x005bc321000008e8    // 0x11F, 1.0-2.7, 方向盘角度 Steeringwheelangle
#define Steering_Wheel_Rotation_Speed_S                                   0x005c4331000008f0    // 0x11F, 3.0-3.7, 方向盘旋转速度 Steeringwheelrotationspeed
#define Failure_Stats_OK_S                                                0x005c8001000008f8    // 0x11F, 4.0, Failure_StatusOK
#define Sensor_Calibration_Stats_S                                        0x005d000100000900    // 0x11F, 4.1, 传感器校准状态 CalibrationStatus
#define TRIM_Trimming_Stats_S                                             0x005d800100000908    // 0x11F, 4.2, 传感器平衡状态
#define Rolling_Counter_11F_S                                             0x005e000100000910    // 0x11F, 5.0-5.3, 滚动循环计数器11F
#define SAS_CheckNum11F_S                                                 0x005e800100000918    // 0x11F, 5.4-5.7, 校验码11F
#define IPB_Vehicle_Speed_121_S                                           0x005f434110000920    // 0x121, 1.0-2.3, 车速
#define Vehicle_Speed_Stats_121_S                                         0x005f800110000928    // 0x121, 2.7, 车速信号状态位
#define Rolling_Counter_121_S                                             0x0060000110000930    // 0x121, 7.0-7.3, 滚动循环计数器121
#define WheelSpeed_FL_122_S                                               0x0060c35120008060    // 0x122, 1.0-2.3, 左前轮速
#define WheelSpeed_FR_Status_122_S                                        0x0061000120000938    // 0x122, 2.4, 右前轮速有效位
#define WheelSpeed_FL_Status_122_S                                        0x0061800120000940    // 0x122, 2.5, 左前轮速有效位
#define WheelSpeed_RR_Status_122_S                                        0x0062000120000948    // 0x122, 2.6, 右后轮速有效位
#define WheelSpeed_RL_Status_122_S                                        0x0062800120000950    // 0x122, 2.7, 左后轮速有效位
#define WheelSpeed_FR_122_S                                               0x0063436120008068    // 0x122, 3.0-4.3, 右前轮速
#define EBD_Active_122_S                                                  0x0063800120000958    // 0x122, 4.4, EBD_Active_122
#define ABS_Active_122_S                                                  0x0064000120000960    // 0x122, 4.5, ABS功能触发信号
#define EBD_Fault_122_S                                                   0x0064800120000968    // 0x122, 4.6, EBD_Fault_122
#define ABS_Fault_122_S                                                   0x0065000120000970    // 0x122, 4.7, ABS功能触发信号有效性信号
#define WheelSpeed_RL_122_S                                               0x0065c37120008070    // 0x122, 5.0-6.3, 左后轮速
#define AWD_Trq_Req_Method_S                                              0x0066000120000978    // 0x122, 6.4-6.5, AWDTrqReqMethod
#define TCS_Active_122_S                                                  0x0066800120000980    // 0x122, 6.6, TCSActive_122
#define DTC_Active_122_S                                                  0x0067000120000988    // 0x122, 6.7, DTCActive_122
#define WheelSpeed_RR_122_S                                               0x0067c38120008078    // 0x122, 7.0-8.3, 右后轮速
#define VDC_Active_122_S                                                  0x0068000120000990    // 0x122, 8.4, VDCActive_122
#define AVH_Failure                                                       0x0068800120000998    // 0x122, 8.5, AVH故障判断状态位
#define AVH_CtrlStatus                                                    0x00690001200009a0    // 0x122, 8.6-8.7, AVH功能状态位
#define HHC_Fault                                                         0x00698001300009a8    // 0x123, 1.0, HHC（Hill-Hold-Control）功能状态
#define IPB_BRAKE_PEDAL_STATUS                                            0x006a0001300009b0    // 0x123, 6.3-6.4, 制动踏板状态信号
#define Rolling_Counter_0x123                                             0x006a8001300009b8    // 0x123, 7.0-7.3, 滚动计数校验
#define ESC_Active                                                        0x006b0001300009c0    // 0x123, 7.5, ESC（ElectronicStabilityController）工作状态
#define TCS_Fault_123_S                                                   0x006b8001300009c8    // 0x123, 7.6, TCS功能触发信号有效性信号
#define VDC_Fault_123_S                                                   0x006c0001300009d0    // 0x123, 7.7, VDC功能触发信号有效性信号
#define DiDyna_CVC_YawTorqueRqst_S                                        0x08390000500009d8    // 0x129, 5.3, 横摆力矩请求状态
#define DiDyna_CVC_TarYawTorque_S                                         0x0839c39050008080    // 0x129, 5.4-7.3, 目标横摆力矩
#define DiDyna_CVC_MotorTorRqst_S                                         0x083a0000500009e0    // 0x129, 5.0, 驱动扭矩分配请求状态
#define DiDyna_CVC_FrontMotorTq_S                                         0x083ac3a050008088    // 0x129, 3.0-4.6, 前轴驾驶员需求扭矩
#define DiDyna_CVC_RearMotorTq_S                                          0x083b43b050008090    // 0x129, 1.0-2.6, 后轴驾驶员需求扭矩
#define DiDyna_CVC_FrontMotorTq_state_S                                   0x083b8000500009e8    // 0x129, 5.1, 前轴力矩请求状态有效位
#define DiDyna_CVC_RearMotorTq_state_S                                    0x083c0000500009f0    // 0x129, 5.2, 后轴力矩请求状态有效位
#define DFC_CVC_YawTorqueRqst_S                                           0x083c8000500009f8    // 0x129, 5.3, 横摆力矩请求状态
#define DFC_CVC_TarYawTorque_S                                            0x083d43c050008098    // 0x129, 5.4-7.3, 目标横摆力矩
#define IG1_Relay_Status_S                                                0x006c800140000a00    // 0x12D, 1.6-1.7, IG1继电器状态
#define Gateway_Engine_Stop_Inform_12D_S                                  0x006d000140000a08    // 0x12D, 4.6, 退电通知
#define BCMPower_Gear_12D_S                                               0x006d800140000a10    // 0x12D, 5.2-5.4, 电源档位
#define Rolling_Counter_12D_S                                             0x006e000140000a18    // 0x12D, 7.4-7.7, 循环丢包计数器
#define Didyna_TBC_Wheelstate_S                                           0x083d800060000a20    // 0x132, 7.0-7.2, 爆胎轮识别信号
#define Didyna_TBC_Status_S                                               0x083e000060000a28    // 0x132, 7.3, 爆胎稳定控制激活状态信号
#define DiDyna_TBC_StrTrqRqst_S                                           0x083e800060000a30    // 0x132, 7.4-7.5, EPS附加助力力矩请求状态
#define DiDyna_TBC_StrTrqt_S                                              0x083f43d060000a38    // 0x132, 7.6-9.1, EPS附加转向助力矩请求值
#define DiDyna_TBC_EPS_Shkhds                                             0x083f800060000a40    // 0x132, 9.2, EPS握手请求信号
#define DiDyna_MBB_EPB_i16Ax_V                                            0x0840000060000a48    // 0x132, 9.3-9.4, EPB请求状态
#define DiDyna_MBB_bStatus                                                0x0840800060000a50    // 0x132, 11.4, MBB开启状态
#define Str_act_tq                                                        0x006ec3e150000a58    // 0x135, 4.0-5.3, 方向盘实际扭矩
#define CRC_Checknum_139_S                                                0x006f000160000a60    // 0x139, 1.0-2.7, 校验码139
#define Alive_Counter_139_S                                               0x006f800160000a68    // 0x139, 3.0-4.7, 滚动循环计数器139
#define ADS2RWS_RearSteeringCtrlReq_S                                     0x0070000160000a70    // 0x139, 18.7, 交互握手 控制请求
#define ADS2RWS_TargetRWheelAngle_S                                       0x0070c3f160000a78    // 0x139, 19.0-20.2, 控制信号 后轮转角请求目标值
#define ADS2RWS_RearSteeringCtrlReqVD_S                                   0x0071000160000a80    // 0x139, 20.4, 交互握手 控制请求有效位
#define Park2RWS_Function_S                                               0x0071800160000a88    // 0x139, 33.0-33.2, 泊车功能请求
#define ADS2RWS_Request_Type_S                                            0x0072000160000a90    // 0x139, 33.3-33.4, 交互握手请求类型
#define Driving2RWS_Function_S                                            0x0072800160000a98    // 0x139, 33.5-33.7, 行车功能请求
#define Drift_State_S                                                     0x0073000170000aa0    // 0x147, 3.1-3.2, 漂移功能状态信号
#define Drift_PAD_Switch_Request_S                                        0x0073800170000aa8    // 0x147, 3.7-4.0, 漂移开关模式返回值
#define EPB_to_DiDyna_Req_S                                               0x0074000180000ab0    // 0x149, 1.0, EPB响应Didyna请求
#define Pre_Clamp_Available_S                                             0x0074800180000ab8    // 0x149, 1.1, 预夹紧可用状态
#define Clamp_Available_S                                                 0x0075000180000ac0    // 0x149, 1.2, 夹紧可用状态
#define Rolling_counter_149_S                                             0x0075800180000ac8    // 0x149, 7.0-7.3, 滚动计数器
#define Key_Cls_To_The_Vechile_S                                          0x2076000190000ad0    // 0x180, 2.2-2.3, 钥匙靠近车辆
#define Vehicle_Torque_183_S                                              0x0076c401a0000ad8    // 0x183, 1.0-2.7, 前轴总扭矩
#define F_Indicated_Driver_Req_Torq_183_S                                 0x00774411a0000ae0    // 0x183, 3.0-4.7, 前轴驾驶员需求指示扭矩
#define Front_Torque_State_183_S                                          0x00778001a0000ae8    // 0x183, 5.0, 前轴扭矩状态
#define F_Indicat_Driver_Req_Torq_Stat_183_S                              0x00780001a0000af0    // 0x183, 5.1, 前轴驾驶员需求指示扭矩状态
#define Msg_Cnt_0x183                                                     0x00788001a0000af8    // 0x183, 7.4-7.7, 0x183生命帧
#define Chks_0x183                                                        0x00790001a0000b00    // 0x183, 8.0-8.7, 0x183校验码
#define LDI_imateYawTorque                                                0x0079c421b00080a0    // 0x1A2, 5.0-6.7, 附加横摆力矩值
#define LDI_Available                                                     0x007a0001b0000b08    // 0x1A2, 7.4, LDI可用信号
#define LDI_Active                                                        0x007a8001b0000b10    // 0x1A2, 7.5, LDI激活信号
#define LDI_State                                                         0x007b0001b0000b18    // 0x1A2, 7.6-7.7, 附加横摆力矩状态
#define YRS_LatAcce_S                                                     0x007bc431c0000b20    // 0x1CC, 1.0-2.7, 横向加速度AY
#define YRS_YawRate_S                                                     0x007c4441c0000b28    // 0x1CC, 3.0-4.7, 指示偏航率
#define YRS_LatSnsrSts_S                                                  0x007c8001c0000b30    // 0x1CC, 5.0-5.1, 横向通道的状态
#define YRS_YawRateSnsrSts_1CC_S                                          0x007d0001c0000b38    // 0x1CC, 5.2-5.3, 偏航率通道的状态
#define YRS_YawRateCalSts_1CC_S                                           0x007d8001c0000b40    // 0x1CC, 6.0, 偏航率传感器标定状态
#define Rolling_Counter_1CC_S                                             0x007e0001c0000b48    // 0x1CC, 7.4-7.7, 滚动循环计数器1CC
#define SuspInhibitReq1E2_S                                               0x007e8001d0000b50    // 0x1E2, 7.0-7.2, 抑制悬架请求
#define SuspInhibitReq1E2_Valid_S                                         0x007f0001d0000b58    // 0x1E2, 7.3, 抑制悬架请求有效位
#define Rolling_Counter_1E2_S                                             0x007f8001d0000b60    // 0x1E2, 7.4-7.7, MsgCounter1E2
#define YRS_LgtAcce_S                                                     0x00804451e0000b68    // 0x1FA, 1.0-2.7, 纵向加速度AX
#define YRS_LgtSnsrSts_S                                                  0x00808001e0000b70    // 0x1FA, 3.0-3.1, 纵向通道的状态
#define Rolling_Counter_1FA_S                                             0x00810001e0000b78    // 0x1FA, 7.4-7.7, 滚动循环计数器1FA
#define EPB_Switch_Status                                                 0x00818001f0000b80    // 0x218, 1.0-1.1, EPB开关状态
#define EPB_Switch_Status_Validity                                        0x00820001f0000b88    // 0x218, 1.2, EPB开关状态有效位
#define EPB_System_Status_Indication_Request                              0x00828001f0000b90    // 0x218, 1.3-1.4, EPB状态指示请求
#define EPB_System_Display_Message_Request                                0x00830001f0000b98    // 0x218, 1.5-1.7, EPB文字提示信息请求
#define EPB_TMMCtrlCmd                                                    0x00838001f0000ba0    // 0x218, 2.0, （APBMI预留） (TMMcontrolcommand)
#define EPB_Status                                                        0x00840001f0000ba8    // 0x218, 2.1-2.3, EPB状态
#define EPB_System_Warning_Indication_Request                             0x00848001f0000bb0    // 0x218, 2.4-2.5, EPB状态指示请求
#define EPB_System_Audible_Warning_Request                                0x00850001f0000bb8    // 0x218, 2.6-2.7, EPB系统声音提示请求
#define EPB_System_Brake_Lights_Requested                                 0x00858001f0000bc0    // 0x218, 3.0, 制动灯点亮请求
#define Brake_backup_availability                                         0x00860001f0000bc8    // 0x218, 3.1, 制动备份能力反馈
#define EPB_Cruise_Control_Cancel_Requested                               0x00868001f0000bd0    // 0x218, 3.2, EPB取消巡航控制请求
#define Intervention_EPB_Flag                                             0x00870001f0000bd8    // 0x218, 3.3, 驾驶员/其他功能请求干预EPB标记位
#define Step_On_Brake_To_Release_Park                                     0x00878001f0000be0    // 0x218, 3.4, 踩制动踏板以释放EPB
#define EPB_TrailerMode_State                                             0x00880001f0000be8    // 0x218, 3.5, EPB拖车模式状态
#define EPB_Automatic_Braking_Requested                                   0x00888001f0000bf0    // 0x218, 3.6, EPB动态制动请求有效位
#define EPB_Switch_Config                                                 0x00890001f0000bf8    // 0x218, 3.7, EPB开关配置
#define EPB_Acceleration_Requested                                        0x00898001f0000c00    // 0x218, 4.0-5.3, EPB动态制动减速度请求值
#define EPB_Manufacturer                                                  0x008a0001f0000c08    // 0x218, 5.4-5.5, EPB供应商
#define EPB_Alive_Rolling_Count                                           0x008a8001f0000c10    // 0x218, 5.6-5.7, EPB在位计数校验
#define EPB_DriveMode_Res                                                 0x008b0001f0000c18    // 0x218, 6.0-6.1, EPB当前驾驶模式反馈
#define Status_of_left_park_brake                                         0x008b8001f0000c20    // 0x218, 6.2-6.4, 左卡钳状态
#define Status_of_right_park_brake                                        0x008c0001f0000c28    // 0x218, 6.5-6.7, 右卡钳状态
#define Rolling_Counter_0x218                                             0x008c8001f0000c30    // 0x218, 7.0-7.3, 滚动计数器
#define EPB_Available_State                                               0x008d0001f0000c38    // 0x218, 7.4-7.5, 电子手刹可用状态
#define EPB_Type                                                          0x008d8001f0000c40    // 0x218, 7.6, EPB类型
#define ActiveVehicleHold                                                 0x008e0001f0000c48    // 0x218, 2.0, ACC车辆保持状态信号
#define ESP_Request_EPB                                                   0x008e8001f0000c50    // 0x218, 2.5-2.6, ESP请求EPB动作信号
#define VehicleStandstill                                                 0x008f0001f0000c58    // 0x218, 3.2-3.3, 车辆静止状态位
#define Rolling_Counter_0x220                                             0x008f8001f0000c60    // 0x218, 7.0-7.3, 滚动计数校验
#define CDD_Active                                                        0x00900001f0000c68    // 0x218, 7.6, VLC（VehicleLongitudinalControl）保压激活状态
#define CDD_Fail                                                          0x00908001f0000c70    // 0x218, 7.7, VLC（VehicleLongitudinalControl）保压有效状态
#define VDC_Active_222_S                                                  0x0091000200000c78    // 0x222, 4.6, VDC功能触发信号
#define ESC_APA_Status                                                    0x0091800200000c80    // 0x222, 5.0-5.2, APA（AutoParkingAssist）状态（IPB确认
#define Rolling_Counter_0x222                                             0x0092000200000c88    // 0x222, 7.0-7.3, 滚动计数校验
#define Vehicle_Torque_FL_241_S                                           0x0092c46210000c90    // 0x241, 1.0-2.7, 前轴总扭矩
#define Driver_Front_Torque_Request                                       0x00934472100080a8    // 0x241, 3.0-4.7, 前轴驾驶员需求指示扭矩
#define Veh_now_max_fb_tq                                                 0x0093c482100080b0    // 0x241, 5.0-6.5, 整车当前最大回馈扭矩
#define Torque_State_FL_241_S                                             0x0094000210000c98    // 0x241, 6.6, 前轴扭矩状态
#define Driver_Front_Torque_Request_Status                                0x0094800210000ca0    // 0x241, 6.7, 前轴驾驶员需求指示扭矩状态
#define Veh_now_act_fb_tq                                                 0x00954492100080b8    // 0x241, 7.0-8.5, 整车当前实际回馈扭矩
#define Veh_now_act_fb_tq_sat                                             0x0095800210000ca8    // 0x241, 8.6, 整车当前实际回馈扭矩状态
#define Veh_now_max_fb_tq_sat                                             0x0096000210000cb0    // 0x241, 8.7, 整车当前最大回馈扭矩状态
#define Front_Motor_Speed                                                 0x0096c4a2200080c0    // 0x242, 1.0-2.7, 前电机转速
#define Veh_adh_sat                                                       0x0097000220000cb8    // 0x242, 3.0-3.2, 车辆附着状态
#define Virt_acce_pedl_perc                                               0x0097800220000cc0    // 0x242, 4.0-4.7, 虚拟油门深度
#define ERROR_Motor_Speed                                                 0x0098000220000cc8    // 0x242, 5.0, 前电机转速状态位
#define Ovrd_flg                                                          0x0098800220000cd0    // 0x242, 5.1, 超越驾驶标志
#define Virt_acce_pedl_perc_efc_flg                                       0x0099000220000cd8    // 0x242, 5.4, 虚拟油门深度有效标志
#define Brake_Pedal_Status_0x242                                          0x0099800220000ce0    // 0x242, 5.5-5.6, 制动踏板状态信号
#define Gear_Position                                                     0x009a000220000ce8    // 0x242, 6.0-6.3, 挡位信号
#define Gear_Status                                                       0x009a800220000cf0    // 0x242, 6.4, 档位信号状态
#define Msg_Cnt_0x242                                                     0x009b000220000cf8    // 0x242, 7.4-7.7, 0x242生命帧
#define Chks_0x242                                                        0x009b800220000d00    // 0x242, 8.0-8.7, 0x242校验码
#define EPS_err_alrm                                                      0x009c000230000d08    // 0x24C, 1.0-1.1, EPS故障报警
#define Rear_Motor_Speed                                                  0x009cc4b2400080c8    // 0x251, 1.0-2.7, 后电机转速
#define Vehicle_Torque_RL_251_S                                           0x009d44c2400080d0    // 0x251, 3.0-4.7, 后轴扭矩
#define Driver_Rear_Torque_Request                                        0x009dc4d2400080d8    // 0x251, 5.0-6.7, 后轴驾驶员需求指示扭矩
#define Error_Rear_Motor_Speed                                            0x009e000240000d10    // 0x251, 7.0, 后电机转速状态位
#define Torque_State_RL_251_S                                             0x009e800240000d18    // 0x251, 7.1, 后轴总扭矩有效性信号
#define Driver_Rear_Torque_Request_Status                                 0x009f000240000d20    // 0x251, 7.2, 后轴驾驶员需求指示扭矩状态
#define Rolling_Counter_251_S                                             0x009f800240000d28    // 0x251, 7.4-7.7, 循环丢包计数器
#define CRC_Checknum_258_S                                                0x0841000070000d30    // 0x258, 1.0-2.7, CRC_Checknum_258_S
#define Alive_Counter_258_S                                               0x0841800070000d38    // 0x258, 3.0-4.7, Alive_Counter_258_S
#define DiSus_Height_Adjust_Status_S                                      0x0842000070000d40    // 0x258, 5.0, 悬架高度可调节状态
#define DiSus_SH_Adjust_Status_S                                          0x0842800070000d48    // 0x258, 5.1, 悬架软硬可调节状态
#define DiSus_Height_Adjust_Fault_S                                       0x0843000070000d50    // 0x258, 5.2, 悬架高度调节故障
#define DiSus_SH_Adjust_Fault_S                                           0x0843800070000d58    // 0x258, 5.3, 悬架软硬调节故障
#define DiSus_Height_Adjust_Process_S                                     0x0844000070000d60    // 0x258, 5.4-5.6, 悬架高度调节过程
#define DiSus_SpeedFollow_Adjust_Process_S                                0x0844800070000d68    // 0x258, 5.7-6.0, 随速调节过程
#define DiSus_Very_Low_Info_S                                             0x0845000070000d70    // 0x258, 6.1, 极低提示
#define DiSus_SH_Adjust_Fault_To_Meter                                    0x0845800070000d78    // 0x258, 6.2, 悬架软硬调节故障仪表灯状态
#define DiSus_SH_Work_Mode_Switch                                         0x0846000070000d80    // 0x258, 6.3, 软硬调节开关模式
#define Actual_DiSus_SH_Adjust_Gear_S                                     0x0846800070000d88    // 0x258, 6.4-6.7, 实际悬架软硬调节挡位
#define Actual_DiSus_Height_Mode_S                                        0x0847000070000d90    // 0x258, 7.0-7.2, 实际悬架高度模式
#define DiSus_Cloud_UpDown_State_S                                        0x0847800070000d98    // 0x258, 7.3, 悬架高度云调节状态
#define Goal_DiSus_Height_Mode_S                                          0x0848000070000da0    // 0x258, 7.4-7.6, 目标悬架高度模式
#define DiSus_Customize_Height_Adjust_Gear_S                              0x0848800070000da8    // 0x258, 8.0-8.3, 用户自定义悬架高度调节挡位
#define Actual_DiSus_Height_Adjust_Gear_S                                 0x0849000070000db0    // 0x258, 8.4-8.7, 实际悬架高度调节挡位
#define DiEye_Assemble_Status_S                                           0x0849800070000db8    // 0x258, 9.0-9.1, 预瞄功能配置
#define DiEye_SettingStates_S                                             0x084a000070000dc0    // 0x258, 9.2, 预瞄功能开启状态信号
#define DiSus_High_Voltage_Request_S                                      0x084a800070000dc8    // 0x258, 9.4-9.5, DiSus高压电请求
#define DiSus_High_Voltage_Request_Valid_S                                0x084b000070000dd0    // 0x258, 9.6, DiSus高压电请求有效位
#define DiSus_Height_Mode_OFF__Invalid_S                                  0x084b800070000dd8    // 0x258, 9.7, 悬架高度模式OFF开关禁用
#define DiSus_Height_Mode_OFF_S                                           0x084c000070000de0    // 0x258, 10.0-10.1, 悬架高度模式OFF开关
#define DiSus_Welcome_Sig_S                                               0x084c800070000de8    // 0x258, 10.2-10.3, 迎宾功能信号
#define DiSus_Welcome_Info_Sig_S                                          0x084d000070000df0    // 0x258, 10.4-10.5, 迎宾功能提示
#define DiSus_Front_Suitcase_S                                            0x084d800070000df8    // 0x258, 10.6-10.7, 前行李箱取物开关
#define DiSus_Rear_Suitcase_S                                             0x084e000070000e00    // 0x258, 11.0-11.1, 后行李箱取物开关
#define DiSus_Balance_Switch_Sig_S                                        0x084e800070000e08    // 0x258, 11.2-11.3, 露营调平功能开关
#define DiSus_Balance_Switch_Info_Sig_S                                   0x084f000070000e10    // 0x258, 11.4-11.5, 露营调平调节过程
#define DiSus_Maintance_Switch_Sig_S                                      0x084f800070000e18    // 0x258, 11.6-11.7, 千斤顶/举升机功能
#define DiSus_Maintance_Switch_OFF_Sig_S                                  0x0850000070000e20    // 0x258, 12.0-12.1, 千斤顶/举升机功能高度抑制
#define DiSus_Height_OFF_Speed_Sig_S                                      0x0850800070000e28    // 0x258, 12.2-12.3, 高度挡位抑制模式-随速
#define DiSus_Height_OFF_Sig_S                                            0x0851000070000e30    // 0x258, 12.4-12.5, 高度挡位抑制模式-固定
#define Welcome_Function_Invalid_S                                        0x0851800070000e38    // 0x258, 12.6, 迎宾功能禁用
#define Trunk_Access_Invalid_S                                            0x0852000070000e40    // 0x258, 12.7, 取物功能禁用
#define DiSus_Towing_Drive_Status_S                                       0x0852800070000e48    // 0x258, 13.0-13.2, 悬架拖拽行驶状态
#define DiSus_Raise_Light_Sig_S                                           0x0853000070000e50    // 0x258, 13.3, 加注功能警示灯
#define DiSus_Raise_Info_Sig_S                                            0x0853800070000e58    // 0x258, 13.4, 加注功能提示信息
#define DiSus_Slow_Down_80kph_S                                           0x0854000070000e60    // 0x258, 13.5, 提示降速至80以下
#define DiSus_IMU_Not_Calibration_S                                       0x0854800070000e68    // 0x258, 13.6, IMU未标定
#define DiSus_Height_Not_Calibration_S                                    0x0855000070000e70    // 0x258, 13.7, 高度未标定
#define DiSus_Combine_Height_Adjust_Gear_FL_S                             0x0855800070000e78    // 0x258, 14.0-14.3, 悬架组合高度调节挡位-左前轮
#define DiSus_Combine_Height_Adjust_Gear_FR_S                             0x0856000070000e80    // 0x258, 14.4-14.7, 悬架组合高度调节挡位-右前轮
#define DiSus_Combine_Height_Adjust_Gear_RL_S                             0x0856800070000e88    // 0x258, 15.0-15.3, 悬架组合高度调节挡位-左后轮
#define DiSus_Combine_Height_Adjust_Gear_RR_S                             0x0857000070000e90    // 0x258, 15.4-15.7, 悬架组合高度调节挡位-右后轮
#define DiSus_Combine_Height_Adjust_Gear_Sing_L_S                         0x0857800070000e98    // 0x258, 16.0-16.3, 悬架组合高度调节挡位-左单边
#define DiSus_Combine_Height_Adjust_Gear_Sing_R_S                         0x0858000070000ea0    // 0x258, 16.4-16.7, 悬架组合高度调节挡位-右单边
#define DiSus_Combine_Height_Adjust_Gear_F_S                              0x0858800070000ea8    // 0x258, 17.0-17.3, 悬架组合高度调节挡位-前轮
#define DiSus_Combine_Height_Adjust_Gear_R_S                              0x0859000070000eb0    // 0x258, 17.4-17.7, 悬架组合高度调节挡位-后轮
#define DiSus_Balance_Switch_Warning_Sig_S                                0x0859800070000eb8    // 0x258, 18.0-18.1, 露营调平报警提示
#define DiDyna_CVC_Switch_mode                                            0x085a000070000ec0    // 0x258, 18.2-18.3, 底盘矢量控制开关模式
#define Balance_Disable_S                                                 0x085a800070000ec8    // 0x258, 18.4, 露营调平禁用_258
#define DiDyna_CVC_Currentworking_mode                                    0x085b000070000ed0    // 0x258, 18.5, 底盘矢量控制开关信号
#define DiSus_SingleWheel_Height_Adjust_Disable_S                         0x085b800070000ed8    // 0x258, 18.6, 单轮调节禁用
#define DiSus_Combine_Height_Adjust_S                                     0x085c000070000ee0    // 0x258, 18.7, 单边调节禁用
#define DiSus_Customize_Combine_Height_Enable_Status_S                    0x085c800070000ee8    // 0x258, 19.0-19.1, DiSus自定义组合高度调节使能信号
#define DiSus_Customize_Single_Height_Enable_Status_S                     0x085d000070000ef0    // 0x258, 19.2-19.3, DiSus自定义单轮高度调节使能信号
#define DiDyna_Switch_CCT                                                 0x085d800070000ef8    // 0x258, 19.4, 舒适控制技术自学习
#define CCT2_0_Function_Switch_IPB                                        0x085e000070000f00    // 0x258, 19.5-19.6, 舒适控制技术
#define DiSus_Maintance_Invalid_S                                         0x085e800070000f08    // 0x258, 19.7, 千斤顶/举升机禁用
#define DiSus_Height_Adjust_prompt_msg                                    0x085f000070000f10    // 0x258, 20.0-20.3, 悬架调节提示语
#define CCT2_0_Function_Switch_PAD                                        0x085f800070000f18    // 0x258, 20.4-20.5, 舒适控制技术
#define Wheel_Arch_Height                                                 0x086044e070000f20    // 0x258, 20.6-21.5, 轮眉高度
#define Wheel_Center_Height                                               0x0860c4f070000f28    // 0x258, 21.6-22.5, 轮心高度
#define Load_Balance_Func_Status                                          0x0861000070000f30    // 0x258, 22.6-22.7, 载荷平衡功能状态
#define DiSus_C_DTC_Reserve1                                              0x0861800070000f38    // 0x258, 23.0-29.7, DiSus_C_DTC预留信号1
#define DiSus_prompt_msg_Send2PAD                                         0x0862000070000f40    // 0x258, 34.0, 悬架提示发送PAD显示
#define DiSus_prompt_msg_Send2Meter                                       0x0862800070000f48    // 0x258, 34.1, 悬架提示发送仪表显示
#define DiSus_prompt_msg_Send2Voice                                       0x0863000070000f50    // 0x258, 34.2, 悬架提示发送语音播放
#define DiSus_prompt_msg_Send2APP                                         0x0863800070000f58    // 0x258, 34.3, 悬架提示发送APP显示
#define ExtraHigh_Req_Result_S                                            0x0864000070000f60    // 0x258, 34.4-34.5, 超高模式请求结果
#define ExtraHigh_Switch_Feedback_S                                       0x0864800070000f68    // 0x258, 34.6-34.7, 超高模式开关反馈信号/状态信号
#define ExtraLow_Switch_Feedback_S                                        0x0865000070000f70    // 0x258, 35.0-35.1, 超低模式开关反馈信号/状态信号
#define DiSus_Extra_Hi_info_S                                             0x0865800070000f78    // 0x258, 35.2-35.3, 超高提示
#define DiSus_Extra_Low_info_S                                            0x0866000070000f80    // 0x258, 35.4-35.5, 超低提示
#define C2DA217_S                                                         0x0866800070000f88    // 0x258, 40.0, C2DA217
#define C2DA316_S                                                         0x0867000070000f90    // 0x258, 40.1, C2DA316
#define U000104_S                                                         0x0867800070000f98    // 0x258, 40.2, U000104
#define C2DA400_S                                                         0x0868000070000fa0    // 0x258, 40.3, C2DA400
#define U019780_S                                                         0x0868800070000fa8    // 0x258, 40.4, U019780
#define U20A387_S                                                         0x0869000070000fb0    // 0x258, 40.5, U20A387
#define U024587_S                                                         0x0869800070000fb8    // 0x258, 40.6, U024587
#define U012200_S                                                         0x086a000070000fc0    // 0x258, 40.7, U012200
#define U018F87_S                                                         0x086a800070000fc8    // 0x258, 41.0, U018F87
#define U110983_S                                                         0x086b000070000fd0    // 0x258, 41.1, U110983
#define C2DB081_S                                                         0x086b800070000fd8    // 0x258, 41.2, C2DB081
#define C2DB100_S                                                         0x086c000070000fe0    // 0x258, 41.3, C2DB100
#define C1E2049_S                                                         0x086c800070000fe8    // 0x258, 41.4, C1E2049
#define C2DB200_S                                                         0x086d000070000ff0    // 0x258, 41.5, C2DB200
#define C1E2146_S                                                         0x086d800070000ff8    // 0x258, 41.6, C1E2146
#define C1E2112_S                                                         0x086e000070001000    // 0x258, 41.7, C1E2112
#define C1E2111_S                                                         0x086e800070001008    // 0x258, 42.0, C1E2111
#define C1E2129_S                                                         0x086f000070001010    // 0x258, 42.1, C1E2129
#define C1E2246_S                                                         0x086f800070001018    // 0x258, 42.2, C1E2246
#define C1E2212_S                                                         0x0870000070001020    // 0x258, 42.3, C1E2212
#define C1E2211_S                                                         0x0870800070001028    // 0x258, 42.4, C1E2211
#define C1E2229_S                                                         0x0871000070001030    // 0x258, 42.5, C1E2229
#define C1E2346_S                                                         0x0871800070001038    // 0x258, 42.6, C1E2346
#define C2DA912_S                                                         0x0872000070001040    // 0x258, 42.7, C2DA912
#define C2DA911_S                                                         0x0872800070001048    // 0x258, 43.0, C2DA911
#define C2DA900_S                                                         0x0873000070001050    // 0x258, 43.1, C2DA900
#define C1E2446_S                                                         0x0873800070001058    // 0x258, 43.2, C1E2446
#define C2DAA12_S                                                         0x0874000070001060    // 0x258, 43.3, C2DAA12
#define C2DAA11_S                                                         0x0874800070001068    // 0x258, 43.4, C2DAA11
#define C2DAA00_S                                                         0x0875000070001070    // 0x258, 43.5, C2DAA00
#define C1E2512_S                                                         0x0875800070001078    // 0x258, 43.6, C1E2512
#define C1E2511_S                                                         0x0876000070001080    // 0x258, 43.7, C1E2511
#define C1E2529_S                                                         0x0876800070001088    // 0x258, 44.0, C1E2529
#define C1E2612_S                                                         0x0877000070001090    // 0x258, 44.1, C1E2612
#define C1E2611_S                                                         0x0877800070001098    // 0x258, 44.2, C1E2611
#define C1E2629_S                                                         0x08780000700010a0    // 0x258, 44.3, C1E2629
#define C2DC709_S                                                         0x08788000700010a8    // 0x258, 44.4, C2DC709
#define C1E2712_S                                                         0x08790000700010b0    // 0x258, 44.5, C1E2712
#define C1E2711_S                                                         0x08798000700010b8    // 0x258, 44.6, C1E2711
#define C1E2713_S                                                         0x087a0000700010c0    // 0x258, 44.7, C1E2713
#define C1E2812_S                                                         0x087a8000700010c8    // 0x258, 45.0, C1E2812
#define C1E2811_S                                                         0x087b0000700010d0    // 0x258, 45.1, C1E2811
#define C1E2813_S                                                         0x087b8000700010d8    // 0x258, 45.2, C1E2813
#define C1E2912_S                                                         0x087c0000700010e0    // 0x258, 45.3, C1E2912
#define C1E2911_S                                                         0x087c8000700010e8    // 0x258, 45.4, C1E2911
#define C1E2913_S                                                         0x087d0000700010f0    // 0x258, 45.5, C1E2913
#define C1E2A12_S                                                         0x087d8000700010f8    // 0x258, 45.6, C1E2A12
#define C1E2A11_S                                                         0x087e000070001100    // 0x258, 45.7, C1E2A11
#define C1E2A13_S                                                         0x087e800070001108    // 0x258, 46.0, C1E2A13
#define C1E2B12_S                                                         0x087f000070001110    // 0x258, 46.1, C1E2B12
#define C1E2B11_S                                                         0x087f800070001118    // 0x258, 46.2, C1E2B11
#define C1E2B13_S                                                         0x0880000070001120    // 0x258, 46.3, C1E2B13
#define C1E2C12_S                                                         0x0880800070001128    // 0x258, 46.4, C1E2C12
#define C1E2C11_S                                                         0x0881000070001130    // 0x258, 46.5, C1E2C11
#define C1E2D12_S                                                         0x0881800070001138    // 0x258, 46.6, C1E2D12
#define C1E2D11_S                                                         0x0882000070001140    // 0x258, 46.7, C1E2D11
#define C1E2E12_S                                                         0x0882800070001148    // 0x258, 47.0, C1E2E12
#define C1E2E11_S                                                         0x0883000070001150    // 0x258, 47.1, C1E2E11
#define C1E2F12_S                                                         0x0883800070001158    // 0x258, 47.2, C1E2F12
#define C1E2F11_S                                                         0x0884000070001160    // 0x258, 47.3, C1E2F11
#define C1E3012_S                                                         0x0884800070001168    // 0x258, 47.4, C1E3012
#define C1E3011_S                                                         0x0885000070001170    // 0x258, 47.5, C1E3011
#define C1E3013_S                                                         0x0885800070001178    // 0x258, 47.6, C1E3013
#define C1E3112_S                                                         0x0886000070001180    // 0x258, 47.7, C1E3112
#define C1E3111_S                                                         0x0886800070001188    // 0x258, 48.0, C1E3111
#define C1E3113_S                                                         0x0887000070001190    // 0x258, 48.1, C1E3113
#define C1E3212_S                                                         0x0887800070001198    // 0x258, 48.2, C1E3212
#define C1E3211_S                                                         0x08880000700011a0    // 0x258, 48.3, C1E3211
#define C1E3213_S                                                         0x08888000700011a8    // 0x258, 48.4, C1E3213
#define C1E3312_S                                                         0x08890000700011b0    // 0x258, 48.5, C1E3312
#define C1E3311_S                                                         0x08898000700011b8    // 0x258, 48.6, C1E3311
#define C1E3313_S                                                         0x088a0000700011c0    // 0x258, 48.7, C1E3313
#define C1E3412_S                                                         0x088a8000700011c8    // 0x258, 49.0, C1E3412
#define C1E3411_S                                                         0x088b0000700011d0    // 0x258, 49.1, C1E3411
#define C1E3413_S                                                         0x088b8000700011d8    // 0x258, 49.2, C1E3413
#define C1E3512_S                                                         0x088c0000700011e0    // 0x258, 49.3, C1E3512
#define C1E3511_S                                                         0x088c8000700011e8    // 0x258, 49.4, C1E3511
#define C1E3513_S                                                         0x088d0000700011f0    // 0x258, 49.5, C1E3513
#define C1E3612_S                                                         0x088d8000700011f8    // 0x258, 49.6, C1E3612
#define C1E3611_S                                                         0x088e000070001200    // 0x258, 49.7, C1E3611
#define C1E3613_S                                                         0x088e800070001208    // 0x258, 50.0, C1E3613
#define C1E3617_S                                                         0x088f000070001210    // 0x258, 50.1, C1E3617
#define C1E3616_S                                                         0x088f800070001218    // 0x258, 50.2, C1E3616
#define C1E3610_S                                                         0x0890000070001220    // 0x258, 50.3, C1E3610
#define C2DAE00_S                                                         0x0890800070001228    // 0x258, 50.4, C2DAE00
#define U10B787_S                                                         0x0891000070001230    // 0x258, 50.5, U10B787
#define C2DB012_S                                                         0x0891800070001238    // 0x258, 50.6, C2DB012
#define C2DB011_S                                                         0x0892000070001240    // 0x258, 50.7, C2DB011
#define C2DB029_S                                                         0x0892800070001248    // 0x258, 51.0, C2DB029
#define C2DB112_S                                                         0x0893000070001250    // 0x258, 51.1, C2DB112
#define C2DB111_S                                                         0x0893800070001258    // 0x258, 51.2, C2DB111
#define C2DB129_S                                                         0x0894000070001260    // 0x258, 51.3, C2DB129
#define C2DC707_S                                                         0x0894800070001268    // 0x258, 51.4, C2DC707
#define C2DB013_S                                                         0x0895000070001270    // 0x258, 51.5, C2DB013
#define C2DB113_S                                                         0x0895800070001278    // 0x258, 51.6, C2DB113
#define C2DAE13_S                                                         0x0896000070001280    // 0x258, 51.7, C2DAE13
#define C2DAF13_S                                                         0x0896800070001288    // 0x258, 52.0, C2DAF13
#define C2DB212_S                                                         0x0897000070001290    // 0x258, 52.1, C2DB212
#define C2DB211_S                                                         0x0897800070001298    // 0x258, 52.2, C2DB211
#define C2DB213_S                                                         0x08980000700012a0    // 0x258, 52.3, C2DB213
#define C2DB312_S                                                         0x08988000700012a8    // 0x258, 52.4, C2DB312
#define C2DB311_S                                                         0x08990000700012b0    // 0x258, 52.5, C2DB311
#define C2DB313_S                                                         0x08998000700012b8    // 0x258, 52.6, C2DB313
#define C2DB412_S                                                         0x089a0000700012c0    // 0x258, 52.7, C2DB412
#define C2DB411_S                                                         0x089a8000700012c8    // 0x258, 53.0, C2DB411
#define C2DB413_S                                                         0x089b0000700012d0    // 0x258, 53.1, C2DB413
#define C2DB512_S                                                         0x089b8000700012d8    // 0x258, 53.2, C2DB512
#define C2DB511_S                                                         0x089c0000700012e0    // 0x258, 53.3, C2DB511
#define C2DB513_S                                                         0x089c8000700012e8    // 0x258, 53.4, C2DB513
#define C2DB612_S                                                         0x089d0000700012f0    // 0x258, 53.5, C2DB612
#define C2DB611_S                                                         0x089d8000700012f8    // 0x258, 53.6, C2DB611
#define C2DB613_S                                                         0x089e000070001300    // 0x258, 53.7, C2DB613
#define C2DB712_S                                                         0x089e800070001308    // 0x258, 54.0, C2DB712
#define C2DB711_S                                                         0x089f000070001310    // 0x258, 54.1, C2DB711
#define C2DB713_S                                                         0x089f800070001318    // 0x258, 54.2, C2DB713
#define C2DB912_S                                                         0x08a0000070001320    // 0x258, 54.3, C2DB912
#define C2DB911_S                                                         0x08a0800070001328    // 0x258, 54.4, C2DB911
#define C2DB913_S                                                         0x08a1000070001330    // 0x258, 54.5, C2DB913
#define C2DBB12_S                                                         0x08a1800070001338    // 0x258, 54.6, C2DBB12
#define C2DBB11_S                                                         0x08a2000070001340    // 0x258, 54.7, C2DBB11
#define C2DBB13_S                                                         0x08a2800070001348    // 0x258, 55.0, C2DBB13
#define C2DBD12_S                                                         0x08a3000070001350    // 0x258, 55.1, C2DBD12
#define C2DBD11_S                                                         0x08a3800070001358    // 0x258, 55.2, C2DBD11
#define C2DBD13_S                                                         0x08a4000070001360    // 0x258, 55.3, C2DBD13
#define C2DBE12_S                                                         0x08a4800070001368    // 0x258, 55.4, C2DBE12
#define C2DBE11_S                                                         0x08a5000070001370    // 0x258, 55.5, C2DBE11
#define C2DBE13_S                                                         0x08a5800070001378    // 0x258, 55.6, C2DBE13
#define C2DC012_S                                                         0x08a6000070001380    // 0x258, 55.7, C2DC012
#define C2DC011_S                                                         0x08a6800070001388    // 0x258, 56.0, C2DC011
#define C2DC013_S                                                         0x08a7000070001390    // 0x258, 56.1, C2DC013
#define C2DC112_S                                                         0x08a7800070001398    // 0x258, 56.2, C2DC112
#define C2DC111_S                                                         0x08a80000700013a0    // 0x258, 56.3, C2DC111
#define C2DC113_S                                                         0x08a88000700013a8    // 0x258, 56.4, C2DC113
#define C2DC212_S                                                         0x08a90000700013b0    // 0x258, 56.5, C2DC212
#define C2DC211_S                                                         0x08a98000700013b8    // 0x258, 56.6, C2DC211
#define C2DC213_S                                                         0x08aa0000700013c0    // 0x258, 56.7, C2DC213
#define C2DC312_S                                                         0x08aa8000700013c8    // 0x258, 57.0, C2DC312
#define C2DC311_S                                                         0x08ab0000700013d0    // 0x258, 57.1, C2DC311
#define C2DC313_S                                                         0x08ab8000700013d8    // 0x258, 57.2, C2DC313
#define C2DBC00_S                                                         0x08ac0000700013e0    // 0x258, 57.3, C2DBC00
#define C2DB300_S                                                         0x08ac8000700013e8    // 0x258, 57.4, C2DB300
#define C2DB400_S                                                         0x08ad0000700013f0    // 0x258, 57.5, C2DB400
#define C2DB500_S                                                         0x08ad8000700013f8    // 0x258, 57.6, C2DB500
#define C2DB600_S                                                         0x08ae000070001400    // 0x258, 57.7, C2DB600
#define C2DB700_S                                                         0x08ae800070001408    // 0x258, 58.0, C2DB700
#define C2DB800_S                                                         0x08af000070001410    // 0x258, 58.1, C2DB800
#define C2DB900_S                                                         0x08af800070001418    // 0x258, 58.2, C2DB900
#define C2DBA00_S                                                         0x08b0000070001420    // 0x258, 58.3, C2DBA00
#define C2DBB00_S                                                         0x08b0800070001428    // 0x258, 58.4, C2DBB00
#define C2DC400_S                                                         0x08b1000070001430    // 0x258, 58.5, C2DC400
#define C1E2C13_S                                                         0x08b1800070001438    // 0x258, 58.6, C1E2C13
#define C1E2D13_S                                                         0x08b2000070001440    // 0x258, 58.7, C1E2D13
#define C1E2E13_S                                                         0x08b2800070001448    // 0x258, 59.0, C1E2E13
#define C1E2F13_S                                                         0x08b3000070001450    // 0x258, 59.1, C1E2F13
#define C2DC500_S                                                         0x08b3800070001458    // 0x258, 59.2, C2DC500
#define C2DC700_S                                                         0x08b4000070001460    // 0x258, 59.3, C2DC700
#define C2DC701_S                                                         0x08b4800070001468    // 0x258, 59.4, C2DC701
#define C2DC702_S                                                         0x08b5000070001470    // 0x258, 59.5, C2DC702
#define C2DC703_S                                                         0x08b5800070001478    // 0x258, 59.6, C2DC703
#define C2DC704_S                                                         0x08b6000070001480    // 0x258, 59.7, C2DC704
#define C2DC705_S                                                         0x08b6800070001488    // 0x258, 60.0, C2DC705
#define C2DC708_S                                                         0x08b7000070001490    // 0x258, 60.1, C2DC708
#define C2DC706_S                                                         0x08b7800070001498    // 0x258, 60.2, C2DC706
#define C2DC800_S                                                         0x08b80000700014a0    // 0x258, 60.3, C2DC800
#define U011187_S                                                         0x08b88000700014a8    // 0x258, 60.4, U011187
#define C2DC900_S                                                         0x08b90000700014b0    // 0x258, 60.5, C2DC900
#define C2DCB92_S                                                         0x08b98000700014b8    // 0x258, 60.6, C2DCB92
#define C2DCA92_S                                                         0x08ba0000700014c0    // 0x258, 60.7, C2DCA92
#define C2DCA31_S                                                         0x08ba8000700014c8    // 0x258, 61.0, C2DCA31
#define C2DB811_S                                                         0x08bb0000700014d0    // 0x258, 61.1, C2DB811
#define C2DB812_S                                                         0x08bb8000700014d8    // 0x258, 61.2, C2DB812
#define C2DB813_S                                                         0x08bc0000700014e0    // 0x258, 61.3, C2DB813
#define C2DCA00_S                                                         0x08bc8000700014e8    // 0x258, 61.4, C2DCA00
#define C2DCB00_S                                                         0x08bd0000700014f0    // 0x258, 61.5, C2DCB00
#define C2DCC00_S                                                         0x08bd8000700014f8    // 0x258, 61.6, C2DCC00
#define C2DCD00_S                                                         0x08be000070001500    // 0x258, 61.7, C2DCD00
#define C2DCE00_S                                                         0x08be800070001508    // 0x258, 62.0, C2DCE00
#define C2DCF00_S                                                         0x08bf000070001510    // 0x258, 62.1, C2DCF00
#define C2DD000_S                                                         0x08bf800070001518    // 0x258, 62.2, C2DD000
#define C2E0000_S                                                         0x08c0000070001520    // 0x258, 62.3, C2E0000
#define C2E0001_S                                                         0x08c0800070001528    // 0x258, 62.4, C2E0001
#define C2E0002_S                                                         0x08c1000070001530    // 0x258, 62.5, C2E0002
#define C2E0003_S                                                         0x08c1800070001538    // 0x258, 62.6, C2E0003
#define C2E0004_S                                                         0x08c2000070001540    // 0x258, 62.7, C2E0004
#define C2E0005_S                                                         0x08c2800070001548    // 0x258, 63.0, C2E0005
#define U040887_S                                                         0x08c3000070001550    // 0x258, 63.1, U040887
#define U040987_S                                                         0x08c3800070001558    // 0x258, 63.2, U040987
#define C2E0006_S                                                         0x08c4000070001560    // 0x258, 63.3, C2E0006
#define C2DA500_S                                                         0x08c4800070001568    // 0x258, 63.4, C2DA500
#define U102A88_S                                                         0x08c5000070001570    // 0x258, 63.5, U102A88
#define U007388_S                                                         0x08c5800070001578    // 0x258, 63.6, U007388
#define U2CB111_S                                                         0x08c6000070001580    // 0x258, 63.7, U2CB111
#define C11710A_S                                                         0x08c6800070001588    // 0x258, 64.0, C11710A
#define PDC_DISTANCE_FL_267_S                                             0x00a0450250001590    // 0x267, 1.0-1.4, 倒车雷达左前区距离显示
#define PDC_DISTANCE_FML_267_S                                            0x00a0c51250001598    // 0x267, 1.5-2.1, 倒车雷达左中前区距离显示
#define PDC_DISTANCE_FMR_267_S                                            0x00a14522500015a0    // 0x267, 2.2-2.6, 倒车雷达右中前区距离显示
#define PDC_DISTANCE_FR_267_S                                             0x00a1c532500015a8    // 0x267, 2.7-3.3, 倒车雷达右前区距离显示
#define PDC_DISTANCE_RL_267_S                                             0x00a24542500015b0    // 0x267, 3.4-4.0, 倒车雷达左后区距离显示
#define PDC_DISTANCE_RML_267_S                                            0x00a2c552500015b8    // 0x267, 4.1-4.5, 倒车雷达左中后区距离显示
#define PDC_DISTANCE_RMR_267_S                                            0x00a34562500015c0    // 0x267, 4.6-5.2, 倒车雷达右中后区距离显示
#define PDC_DISTANCE_RR_267_S                                             0x00a3c572500015c8    // 0x267, 5.3-5.7, 倒车雷达右后区距离显示
#define PAS_Status_267_S                                                  0x00a40002500015d0    // 0x267, 7.0, 驻车辅助系统状态
#define Left_Front_Door_Status_294_S                                      0x00a48002600015d8    // 0x294, 1.0-1.1, 左前门状态
#define Right_Front_Door_Status_294_S                                     0x00a50002600015e0    // 0x294, 1.2-1.3, 右前门状态
#define Left_Back_Door_Status_294_S                                       0x00a58002600015e8    // 0x294, 1.4-1.5, 左后门状态
#define Right_Back_Door_Status_294_S                                      0x00a60002600015f0    // 0x294, 1.6-1.7, 右后门状态
#define LeftF_Door_Stats_Valid_Bit_S                                      0x00a68002600015f8    // 0x294, 2.0, 左前门状态有效位
#define RightF_Door_Sts_Valid_Bit_294_S                                   0x00a7000260001600    // 0x294, 2.1, 右前门状态有效位
#define LeftR_Door_Stats_Valid_Bit_S                                      0x00a7800260001608    // 0x294, 2.2, 左后门状态有效位
#define RightR_Door_Sts_Valid_Bit_294_S                                   0x00a8000260001610    // 0x294, 2.3, 右后门状态有效位
#define driver_seat_belts_significant_bit                                 0x00a8800260001618    // 0x294, 2.4, 驾驶员安全带状态有效位
#define Driver_Belt_Status_S                                              0x00a9000260001620    // 0x294, 3.0-3.1, 驾驶员安全带状态
#define Back_Door_Status_294_S                                            0x00a9800260001628    // 0x294, 3.2-3.3, 后背门状态
#define Former_Hatch_Status_294_S                                         0x00aa000260001630    // 0x294, 3.4-3.5, 前舱盖状态
#define Rolling_Counter_294_S                                             0x00aa800260001638    // 0x294, 7.0-7.3, 循环丢包计数器
#define DiDyna_SMC_PAD                                                    0x08c7000080001640    // 0x2A1, 1.0-1.1, SMC回执给PAD的请求信号
#define Didyna_TBC_Currentworking_State_S                                 0x08c7800080001648    // 0x2A1, 1.4-1.5, 爆胎稳定控制开关信号
#define DiDyna_TBC_Switch_mode                                            0x08c8000080001650    // 0x2A1, 2.7, 爆胎稳定控制开关模式
#define DiDyna_SMC_Switch_mode                                            0x08c8800080001658    // 0x2A1, 3.0, PAD自学习信号
#define g_AFS_out_Com_u8Gear_Z_2A1_S                                      0x08c9000080001660    // 0x2A1, 3.4-3.5, 主驾侧翼功能等级反馈
#define g_AFS_out_Com_u8Gear_F_2A1_S                                      0x08c9800080001668    // 0x2A1, 3.6-3.7, 副驾侧翼功能等级反馈
#define Left_Side_Wing_Of_Main_Driver_S                                   0x08ca000080001670    // 0x2A1, 4.0-4.1, 主驾左气袋充放气指令
#define Right_Side_Wing_Of_Main_Driver_S                                  0x08ca800080001678    // 0x2A1, 4.2-4.3, 主驾右气袋充放气指令
#define Left_Side_Wing_Of_CoPilot_S                                       0x08cb000080001680    // 0x2A1, 4.4-4.5, 副驾左气袋充放气指令
#define Right_Side_Wing_Of_CoPilot_S                                      0x08cb800080001688    // 0x2A1, 4.6-4.7, 副驾右气袋充放气指令
#define Date_Information_Year_2B6_S                                       0x00ab000270001690    // 0x2B6, 1.0-1.7, 日期信息：年
#define Date_Information_Month_2B6_S                                      0x00ab800270001698    // 0x2B6, 2.0-2.7, 日期信息：月
#define Date_Information_Day_2B6_S                                        0x00ac0002700016a0    // 0x2B6, 3.0-3.7, 日期信息：日
#define Date_Information_Hour_2B6_S                                       0x00ac8002700016a8    // 0x2B6, 4.0-4.7, 时间信息：时
#define Date_Information_Minute_2B6_S                                     0x00ad0002700016b0    // 0x2B6, 5.0-5.7, 时间信息：分
#define Date_Information_Second_2B6_S                                     0x00ad8002700016b8    // 0x2B6, 6.0-6.7, 时间信息：秒
#define Week_2B6_S                                                        0x00ae0002700016c0    // 0x2B6, 7.0-7.2, 星期
#define Time_2B6_S                                                        0x00ae8002700016c8    // 0x2B6, 7.3-7.4, 时制
#define Main_Seat_Left_Flank_Pres_Feedback_S                              0x00af0002880216d0    // 0x2E4, 2.0-2.7, 主驾坐垫左侧侧翼压力值反馈
#define Main_Seat_Right_Flank_Pres_Feedback_S                             0x00af8002880216d8    // 0x2E4, 3.0-3.7, 主驾坐垫右侧侧翼压力值反馈
#define Main_Driver_Back_L_Flank_Pres_Feedback_S                          0x00b00002880216e0    // 0x2E4, 4.0-4.7, 主驾靠背左侧侧翼压力值反馈
#define Main_Driver_Back_R_Flank_Pres_Feedback_S                          0x00b08002880216e8    // 0x2E4, 5.0-5.7, 主驾靠背右侧侧翼压力值反馈
#define Main_Driver_Flank_Cond_Sta_Feedback_S                             0x00b10002880216f0    // 0x2E4, 6.0-6.1, 主驾侧翼调节状态反馈
#define First_Officer_Left_Flank_Pres_Feedback_S                          0x00b18002880416f8    // 0x2E4, 2.0-2.7, 副驾坐垫左侧侧翼压力值反馈
#define First_Officer_Right_Flank_Pres_Feedback_S                         0x00b2000288041700    // 0x2E4, 3.0-3.7, 副驾坐垫右侧侧翼压力值反馈
#define First_Officer_Back_L_Flank_Pres_Feedback_S                        0x00b2800288041708    // 0x2E4, 4.0-4.7, 副驾靠背左侧侧翼压力值反馈
#define First_Officer_Back_R_Flank_Pres_Feedback_S                        0x00b3000288041710    // 0x2E4, 5.0-5.7, 副驾靠背右侧侧翼压力值反馈
#define First_Officer_Flank_Cond_Sta_Feedback_S                           0x00b3800288041718    // 0x2E4, 6.0-6.1, 副驾侧翼调节状态反馈
#define LR_Left_Flank_Pres_Feedback_S                                     0x00b4000288061720    // 0x2E4, 2.0-2.7, 左后坐垫左侧侧翼压力值反馈
#define LR_Right_Flank_Pres_Feedback_S                                    0x00b4800288061728    // 0x2E4, 3.0-3.7, 左后坐垫右侧侧翼压力值反馈
#define LR_Backrest_L_Flank_Pres_Feedback_S                               0x00b5000288061730    // 0x2E4, 4.0-4.7, 左后靠背左侧侧翼压力值反馈
#define LR_Backrest_R_Flank_Pres_Feedback_S                               0x00b5800288061738    // 0x2E4, 5.0-5.7, 左后靠背右侧侧翼压力值反馈
#define FR_Flank_Conditioning_Sta_Feedback_S                              0x00b6000288061740    // 0x2E4, 6.0-6.1, 左后侧翼调节状态反馈
#define RR_Left_Flank_Pres_Feedback_S                                     0x00b6800288081748    // 0x2E4, 2.0-2.7, 右后坐垫左侧侧翼压力值反馈
#define RR_Right_Flank_Pres_Feedback_S                                    0x00b7000288081750    // 0x2E4, 3.0-3.7, 右后坐垫右侧侧翼压力值反馈
#define RR_Backrest_L_Flank_Pres_Feedback_S                               0x00b7800288081758    // 0x2E4, 4.0-4.7, 右后靠背左侧侧翼压力值反馈
#define RR_Backrest_R_Flank_Pres_Feedback_S                               0x00b8000288081760    // 0x2E4, 5.0-5.7, 右后靠背右侧侧翼压力值反馈
#define RR_Flank_Conditioning_Sta_Feedback_S                              0x00b8800288081768    // 0x2E4, 6.0-6.1, 右后侧翼调节状态反馈
#define Oil_Life_Reset_Button_2EA_S                                       0x20b9000290001770    // 0x2EA, 1.2, 机油寿命复位按键
#define Transmi_Oil_Life_Reset_Button_S                                   0x20b9800290001778    // 0x2EA, 6.7, 变速器油寿命复位按键
#define Blade_Bat_Cool_Life_Reset_But_S                                   0x20ba000290001780    // 0x2EA, 7.0, 刀片电池冷却液寿命复位按键
#define Longterm_Cool_Life_Reset_Butt_S                                   0x20ba800290001788    // 0x2EA, 7.1, 长效冷却液寿命复位按键
#define Brake_Fluid_Life_Reset_Button_S                                   0x20bb000290001790    // 0x2EA, 7.2, 制动液寿命复位按键
#define Main_Seat_Left_Flank_Pres_Feedback_2FF_S                          0x00bb8002a80a1798    // 0x2FF, 2.0-2.7, 主驾坐垫左侧侧翼压力值反馈
#define Main_Seat_Right_Flank_Pres_Feedback_2FF_S                         0x00bc0002a80a17a0    // 0x2FF, 3.0-3.7, 主驾坐垫右侧侧翼压力值反馈
#define Main_Driver_Back_L_Flank_Pres_Feedback_2FF_S                      0x00bc8002a80a17a8    // 0x2FF, 4.0-4.7, 主驾靠背左侧侧翼压力值反馈
#define Main_Driver_Back_R_Flank_Pres_Feedback_2FF_S                      0x00bd0002a80a17b0    // 0x2FF, 5.0-5.7, 主驾靠背右侧侧翼压力值反馈
#define Main_Driver_Flank_Cond_Status_Feedback_2FF_S                      0x00bd8002a80a17b8    // 0x2FF, 6.0-6.1, 主驾侧翼调节状态反馈
#define First_Officer_Left_Flank_Pres_Feedback_2FF_S                      0x00be0002a80c17c0    // 0x2FF, 2.0-2.7, 副驾坐垫左侧侧翼压力值反馈
#define First_Officer_Right_Flank_Pres_Feedback_2FF_S                     0x00be8002a80c17c8    // 0x2FF, 3.0-3.7, 副驾坐垫右侧侧翼压力值反馈
#define First_Officer_Back_L_Flank_Pres_Feedback_2FF_S                    0x00bf0002a80c17d0    // 0x2FF, 4.0-4.7, 副驾靠背左侧侧翼压力值反馈
#define First_Officer_Back_R_Flank_Pres_Feedback_2FF_S                    0x00bf8002a80c17d8    // 0x2FF, 5.0-5.7, 副驾靠背右侧侧翼压力值反馈
#define First_Officer_Flank_Cond_Status_Feedback_2FF_S                    0x00c00002a80c17e0    // 0x2FF, 6.0-6.1, 副驾侧翼调节状态反馈
#define LR_Left_Flank_Pressure_Feedback_2FF_S                             0x00c08002a80e17e8    // 0x2FF, 2.0-2.7, 左后坐垫左侧侧翼压力值反馈
#define LR_Right_Flank_Pressure_Feedback_2FF_S                            0x00c10002a80e17f0    // 0x2FF, 3.0-3.7, 左后坐垫右侧侧翼压力值反馈
#define LR_Backrest_L_Flank_Pressure_Feedback_2FF_S                       0x00c18002a80e17f8    // 0x2FF, 4.0-4.7, 左后靠背左侧侧翼压力值反馈
#define LR_Backrest_R_Flank_Pressure_Feedback_2FF_S                       0x00c20002a80e1800    // 0x2FF, 5.0-5.7, 左后靠背右侧侧翼压力值反馈
#define FR_Flank_Conditioning_Status_Feedback_2FF_S                       0x00c28002a80e1808    // 0x2FF, 6.0-6.1, 左后侧翼调节状态反馈
#define RR_Left_Flank_Pressure_Feedback_2FF_S                             0x00c30002a8101810    // 0x2FF, 2.0-2.7, 右后坐垫左侧侧翼压力值反馈
#define RR_Right_Flank_Pressure_Feedback_2FF_S                            0x00c38002a8101818    // 0x2FF, 3.0-3.7, 右后坐垫右侧侧翼压力值反馈
#define RR_Backrest_L_Flank_Pressure_Feedback_2FF_S                       0x00c40002a8101820    // 0x2FF, 4.0-4.7, 右后靠背左侧侧翼压力值反馈
#define RR_Backrest_R_Flank_Pressure_Feedback_2FF_S                       0x00c48002a8101828    // 0x2FF, 5.0-5.7, 右后靠背右侧侧翼压力值反馈
#define RR_Flank_Conditioning_Status_Feedback_2FF_S                       0x00c50002a8101830    // 0x2FF, 6.0-6.1, 右后侧翼调节状态反馈
#define Engine_Water_Temp_Thermostat_S                                    0x00c5c582b0001838    // 0x30D, 1.0-1.7, 发动机节温器端水温
#define ECM_Instant_Fuel_Consumption_S                                    0x00c64592b0001840    // 0x30D, 2.0-3.7, 瞬时油耗
#define CoPilot_Seat_Belt_Status_312_S                                    0x00c68002c0001848    // 0x312, 1.0-1.1, 副驾安全带状态312
#define Rear_Left_Second_Row_Seat_Belt_Status_312_S                       0x00c70002c0001850    // 0x312, 1.2-1.3, 左后二排安全带状态312
#define Rear_Right_Second_Row_Seat_Belt_Status_312_S                      0x00c78002c0001858    // 0x312, 1.4-1.5, 右后二排安全带状态312
#define Middle_Rear_Second_Row_Seat_Belt_Status_312_S                     0x00c80002c0001860    // 0x312, 1.6-1.7, 中后二排安全带状态312
#define ThreeRow_Left_Seat_Belt_Status_312_S                              0x00c88002c0001868    // 0x312, 2.0-2.1, 第三排左座安全带状态312
#define ThreeRow_Middle_Seat_Belt_Status_312_S                            0x00c90002c0001870    // 0x312, 2.2-2.3, 第三排中座安全带状态312
#define ThreeRow_Right_Seat_Belt_Status_312_S                             0x00c98002c0001878    // 0x312, 2.4-2.5, 第三排右座安全带状态312
#define CoDriver_Seat_312_S                                               0x00ca0002c0001880    // 0x312, 2.6-2.7, 副驾座椅312
#define Driver_Seat_S                                                     0x00ca8002c0001888    // 0x312, 8.0-8.1, 主驾座椅
#define StrngWhlTorqVD_S                                                  0x00cb0002d0001890    // 0x318, 3.7, 方向盘转矩有效位
#define StrngWhlTorq_S                                                    0x00cbc5a2d0001898    // 0x318, 4.0-5.3, 方向盘转矩
#define Rolling_Counter_318_S                                             0x00cc0002d00018a0    // 0x318, 7.4-7.7, 滚动循环计数器318
#define PAS_status_S                                                      0x00cc8002e81218a8    // 0x31A, 2.4-2.6, 倒车雷达系统状态
#define Show_Car_Mode_31E_S                                               0x00cd0002f00018b0    // 0x31E, 1.0-1.3, 展车模式
#define Camping_Mode_S                                                    0x00cd8002f00018b8    // 0x31E, 2.5-2.7, 露营模式
#define IPB_Simulator_Pressure_S                                          0x00ce45b3000018c0    // 0x321, 1.0-2.3, IPB_SimulatorPressure Simulator压力
#define IPB_Simulator_Pressure_Status_S                                   0x00ce8003000018c8    // 0x321, 2.4, IPB_SimulatorPressure_Sts Simulator压力状态
#define IPB_PlungerPressure_Status_321_S                                  0x00cf0003000018d0    // 0x321, 2.5, IPB_PlungerPressure_status
#define IPB_Plunger_Pressure_321_S                                        0x00cf8003000018d8    // 0x321, 3.0-4.3, IPB_PlungerPressure Plunger压力
#define Rolling_Counter_321_S                                             0x00d00003000018e0    // 0x321, 7.4-7.7, 滚动循环计数器321
#define No_More_Reminder_Of_Oil_Life_S                                    0x20d08003181418e8    // 0x32B, 2.0, 机油寿命不再提醒
#define Transmission_Oil_Life_Is_No_Longer_Reminded_S                     0x20d10003181418f0    // 0x32B, 2.1, 变速器油寿命不再提醒
#define Brake_Fluid_Life_Will_No_Longer_Be_Reminded_S                     0x20d18003181418f8    // 0x32B, 2.2, 制动液寿命不再提醒
#define Long_Life_Coolant_Life_Is_No_Longer_Reminded_S                    0x20d2000318141900    // 0x32B, 2.3, 长效冷却液寿命不再提醒
#define Blade_Battery_Coolant_Life_Is_No_Longer_Reminded_S                0x20d2800318141908    // 0x32B, 2.4, 刀片电池冷却液寿命不再提醒
#define Media_DiDyna_SMC_Work_Switch                                      0x20d3000318141910    // 0x32B, 3.2-3.3, SMC功能软开关标志位
#define Media_DiDyna_TBC_Work_Switch_S                                    0x20d3800318141918    // 0x32B, 3.4-3.5, PAD端TBC功能软开关标志位
#define Rolling_Counter_32F_S                                             0x00d4000320001920    // 0x32F, 7.0-7.3, AliveCounter32F
#define SuspInhibitReq32F_S                                               0x00d4800320001928    // 0x32F, 7.4-7.6, 抑制悬架请求
#define SuspInhibitReq32F_Valid_S                                         0x00d5000320001930    // 0x32F, 7.7, 抑制悬架请求有效位
#define High_Pree_Req_Log_On_Back_Plat_S                                  0x00d5800330001938    // 0x336, 7.2-7.5, 后台上高压申请记录
#define Background_High_Pressure_S                                        0x00d6000330001940    // 0x336, 7.6-7.7, 后台高压
#define VCU_READY_Indicator_Light_S                                       0x00d6800340001948    // 0x341, 2.0-2.1, READY指示灯
#define Actual_Throttle_Depth_S                                           0x00d745c350001950    // 0x342, 1.0-1.7, 实际油门深度
#define VCU_Brake_Depth_S                                                 0x00d7c5d350001958    // 0x342, 2.0-2.7, 制动深度
#define Actual_Throttle_Depth_Effective_Sign_S                            0x00d8000350001960    // 0x342, 3.0, 实际油门深度有效标志
#define VCU_Brake_Depth_Virtual_Value_S                                   0x00d8800350001968    // 0x342, 3.1, 制动深度有效位
#define VCU_Drive_Mode_1_342_S                                            0x00d9000350001970    // 0x342, 3.4-3.5, 整车运行模式
#define VCU_Drive_Mode_2_342_S                                            0x00d9800350001978    // 0x342, 3.6-3.7, 整车运行模式2
#define Tar_rnk                                                           0x00da000350001980    // 0x342, 4.0-4.3, 目标挡位
#define Veh_drv_sty                                                       0x00da800350001988    // 0x342, 5.0-5.3, 整车驱动方式
#define Rolling_Counter_342_S                                             0x00db000350001990    // 0x342, 7.4-7.7, 循环丢包计数器
#define BMC_Charge_Gun_Connect_Status_S                                   0x00db800360001998    // 0x344, 6.2-6.4, 充电枪连接状态
#define Maximum_Vehicle_Torque                                            0x00dc45e3700080e0    // 0x356, 1.0-2.7, 当前最大整车扭矩
#define Minimum_Vehicle_Torque                                            0x00dcc5f3700080e8    // 0x356, 3.0-4.7, 当前最小整车扭矩
#define Maximum_Vehicle_Torque_Status                                     0x00dd0003700019a0    // 0x356, 5.0, 当前最大整车扭矩状态位
#define Minimum_Vehicle_Torque_Status                                     0x00dd8003700019a8    // 0x356, 5.1, 当前最小整车扭矩状态位
#define tar_disus_mode_S                                                  0x00de0003700019b0    // 0x356, 6.0-6.7, 目标悬架模式
#define Rolling_Counter_356_S                                             0x00de8003700019b8    // 0x356, 7.4-7.7, 循环丢包计数器
#define Gear_Recognize_Status_S                                           0x00df0003800019c0    // 0x35C, 2.0-2.2, 档位识别状态
#define Park_Button_Status_S                                              0x00df8003800019c8    // 0x35C, 4.4-4.6, P开关状态
#define Rolling_Counter_35C_S                                             0x00e00003800019d0    // 0x35C, 7.4-7.7, 滚动循环计数器35C
#define DiSus_C_Working_Mode_Set_S                                        0x20e08003900019d8    // 0x385, 7.2-7.3, Disus_C悬架模式控制开关信号
#define Oil_Life_38D_38D_S                                                0x18cc4600900019e0    // 0x38D, 1.0-1.7, 机油寿命
#define Transmission_Oil_Life_38D_38D_S                                   0x18ccc610900019e8    // 0x38D, 2.0-2.7, 变速器油寿命
#define Brake_Fluid_Life_38D_S                                            0x18cd4620900019f0    // 0x38D, 3.0-3.7, 制动液寿命
#define Blade_Battery_Coolant_Life_38D_S                                  0x18cdc630900019f8    // 0x38D, 4.0-4.7, 刀片电池冷却液寿命
#define Longterm_Coolant_Life_38D_S                                       0x18ce464090001a00    // 0x38D, 5.0-5.7, 长效冷却液寿命
#define Engine_Oil_Life_Reset_Feedback_38D_S                              0x18ce800090001a08    // 0x38D, 6.0-6.1, 机油寿命复位反馈
#define Transmission_Oil_Life_Reset_Feedback_38D_S                        0x18cf000090001a10    // 0x38D, 6.2-6.3, 变速器油寿命复位反馈
#define Blade_Battery_Coolant_Life_Reset_Feedback_38D_S                   0x18cf800090001a18    // 0x38D, 6.4-6.5, 刀片电池冷却液寿命复位反馈
#define LongTerm_Coolant_Life_Reset_Feedback_38D_S                        0x18d0000090001a20    // 0x38D, 6.6-6.7, 长效冷却液寿命复位反馈
#define Brake_Fluid_Life_Reset_Feedback_38D_S                             0x18d0800090001a28    // 0x38D, 7.0-7.1, 制动液寿命复位反馈
#define Oil_Maintenance_Reminder_38D_S                                    0x18d1000090001a30    // 0x38D, 7.2-7.3, 机油保养提醒
#define Transmission_Oil_Maintenance_Reminder_38D_S                       0x18d1800090001a38    // 0x38D, 7.4-7.5, 变速器油保养提醒
#define Brake_Fluid_Maintenance_Reminder_38D_S                            0x18d2000090001a40    // 0x38D, 7.6-7.7, 制动液保养提醒
#define LongTerm_Coolant_Maintenance_Reminder_38D_S                       0x18d2800090001a48    // 0x38D, 8.0-8.1, 长效冷却液保养提醒
#define Blade_Battery_Coolant_Maintenance_Reminder_38D_S                  0x18d3000090001a50    // 0x38D, 8.2-8.3, 刀片电池冷却液保养提醒
#define Oil_Life_Development_SelfLearning_Status_Bit_S                    0x18d3800090001a58    // 0x38D, 8.4-8.5, 机油寿命开发自学习状态位
#define Transmission_Oil_Development_SelfLearning_Status_Bit_S            0x18d4000090001a60    // 0x38D, 8.6-8.7, 变速器油开发自学习状态位
#define Brake_Fluid_Life_Development_SelfLearning_Status_Bit_S            0x18d4800090001a68    // 0x38D, 9.0-9.1, 制动液寿命开发自学习状态位
#define Long_Life_Coolant_Life_Development_SelfLearning_Status_Bit_S      0x18d5000090001a70    // 0x38D, 9.2-9.3, 长效冷却液寿命开发自学习状态位
#define Blade_Battery_Cooling_Life_Development_SelfLearning_Status_Bit_S  0x18d5800090001a78    // 0x38D, 9.4-9.5, 刀片电池冷却液寿命开发自学习状态位
#define Main_Driver_Active_Flank_Strength_3CE_S                           0x10e10003a0001a80    // 0x3CE, 7.6-7.7, 主驾主动侧翼强度
#define Pass_Seat_Active_Flank_Strength_S                                 0x10e18003b0001a88    // 0x3D3, 7.6-7.7, 副驾主动侧翼强度
#define Inside_Temperature_3D8_S                                          0x00e24653c0001a90    // 0x3D8, 6.0-6.7, 车内温度
#define Total_Distance_3D9s5_S                                            0x00e2c663d8170000    // 0x3D9, 2.0-5.2, 总里程
#define HEV_Distance_3D9_S                                                0x00e30003d8190008    // 0x3D9, 3.0-5.3, HEV里程
#define Veh_Set_Fact_Dfault_Flg_S                                         0x20e38003e0001a98    // 0x3E3, 3.6, 车辆设置的恢复出厂标志位
#define Driv_Set_Fact_Dfault_Flg_S                                        0x20e40003e0001aa0    // 0x3E3, 3.7, 行驶设置的恢复出厂标志位
#define ACHMC_Outside_Temperature_S                                       0x10e4c673f0001aa8    // 0x404, 7.0-7.7, 车外温度
#define Vehicle_Door_Lock_Logic_Status_S                                  0x10e5000400001ab0    // 0x407, 2.2-2.3, 整车门锁逻辑状态
#define VCU_Drive_Modes_S                                                 0x00e5800410001ab8    // 0x410, 1.0-1.1, 驾驶模式
#define Brake_Request                                                     0x00e6000410001ac0    // 0x410, 1.2-1.3, 遥控驾驶制动请求状态信号
#define Sld_fb_tar_tq_sat                                                 0x00e6800410001ac8    // 0x410, 5.1, 滑行回馈目标扭矩状态
#define Sld_fb_tar_tq_val                                                 0x00e74684100080f0    // 0x410, 5.2-6.7, 滑行回馈目标扭矩值
#define L_BAT_Capacity_Index                                              0x00e7800420001ad0    // 0x444, 6.0-6.7, 电池组当前容量指数(分辨率1%)
#define S_BAT_Power_Mode                                                  0x00e8000430001ad8    // 0x449, 8.2-8.3, 功耗模式
#define Drag_Mode_State_490_S                                             0x00e8800440001ae0    // 0x490, 3.0-3.1, 拖拽模式状态
#define Rolling_Counter_0x490                                             0x00e9000440001ae8    // 0x490, 7.0-7.3, 滚动计数校验
#define Towing_Driving_Mode_Status                                        0x00e9800450001af0    // 0x490, 3.0-3.1, 带拖车行驶模式状态
#define Compressor_Working_Hours_S                                        0x08d60000a8001af8    // 0x495, 2.0-4.1, 压缩机工作时长
#define Exhaust_Valve_Action_Times_S                                      0x08d68000a8001b00    // 0x495, 4.2-6.5, 排气阀开关次数
#define Booster_Valve_Action_Times_S                                      0x08d70000a8001b08    // 0x495, 6.6-9.1, 增压阀开关次数
#define FL_LV_Action_Times_S                                              0x08d78000a8001b10    // 0x495, 9.2-11.5, FL调平阀开关次数
#define FR_LV_Action_Times_S                                              0x08d80000a8001b18    // 0x495, 11.6-14.1, FR调平阀开关次数
#define RL_LV_Action_Times_S                                              0x08d88000a8001b20    // 0x495, 14.2-16.5, RL调平阀开关次数
#define RR_LV_Action_Times_S                                              0x08d90000a8001b28    // 0x495, 16.6-19.1, RR调平阀开关次数
#define ACCV_Action_Times_S                                               0x08d98000a8001b30    // 0x495, 19.2-21.5, 储气罐阀开关次数
#define FourWayValve_Action_Times_S                                       0x08da0000a8001b38    // 0x495, 21.6-24.1, 侧翼四通阀开关次数
#define Replenishment_Frequency_S                                         0x08da8000a8001b40    // 0x495, 24.2-24.7, 补气次数
#define FL_Replenishment_Height_S                                         0x08db0000a8001b48    // 0x495, 25.0-25.7, FL补气高度
#define FR_Replenishment_Height_S                                         0x08db8000a8001b50    // 0x495, 26.0-26.7, FR补气高度
#define RL_Replenishment_Height_S                                         0x08dc0000a8001b58    // 0x495, 27.0-27.7, RL补气高度
#define RR_Replenishment_Height_S                                         0x08dc8000a8001b60    // 0x495, 28.0-28.7, RR补气高度
#define Inspector_Error_ID_S                                              0x08dd0000a8001b68    // 0x495, 29.0-31.1, ASW故障ID
#define Compressor_Action_Times_S                                         0x08dd8000a8001b70    // 0x495, 31.2-33.5, 泵电机开关次数
#define Up_Down_Times_S                                                   0x08de0000a8001b78    // 0x495, 33.6-36.1, 手动举升下降次数
#define FL_KV_Action_Times_S                                              0x08de8000a8001b80    // 0x495, 36.2-38.5, FL刚度阀开关次数
#define FR_KV_Action_Times_S                                              0x08df0000a8001b88    // 0x495, 38.6-41.1, FR刚度阀开关次数
#define RL_KV_Action_Times_S                                              0x08df8000a8001b90    // 0x495, 41.2-43.5, RL刚度阀开关次数
#define RR_KV_Action_Times_S                                              0x08e00000a8001b98    // 0x495, 43.6-46.1, RR刚度阀开关次数
#define Soft_Reset_Type_S                                                 0x08e08000a8001ba0    // 0x495, 46.2-46.7, 软件异常复位类型
#define Soft_Reset_Trigger_Reason_S                                       0x08e10000a8001ba8    // 0x495, 47.0-47.7, 软件异常复位触发原因
#define Soft_Reset_Count_S                                                0x08e18000a8001bb0    // 0x495, 48.0-48.7, 软件异常复位次数
#define Inspector_SlowDown80Kph_S                                         0x08e20000a8001bb8    // 0x495, 49.0, 高速行驶悬架系统安全提示
#define CCT2_0_Function_Switch_PAD_495                                    0x08e28000a8001bc0    // 0x495, 49.1-49.2, 舒适控制技术
#define Inspector_SWActorOutSig_S                                         0x08e30000a8001bc8    // 0x495, 49.3-52.7, 开关阀输出信号
#define Inspector_g_u8_Sts_S                                              0x08e38000a8001bd0    // 0x495, 53.0-53.7, 悬架系统工作状态
#define Inspector_g_u8ASW_accu_ctr_S                                      0x08e40000a8001bd8    // 0x495, 54.0-54.7, 悬架系统工作状态
#define Inspector_PressureSig_S                                           0x08e4c690a8001be0    // 0x495, 55.0-56.7, 悬架压力信号 单位bar
#define Inspector_SBAD_ID_S                                               0x08e50000a8001be8    // 0x495, 57.0-57.3, 悬架高度抑制类型
#define Inspector_ECUMode_S                                               0x08e58000a8001bf0    // 0x495, 57.4-57.7, ECU模式
#define Actual_DiSus_Height_Mode_495_S                                    0x08e60000a8001bf8    // 0x495, 58.0-58.2, 实际悬架高度模式
#define DiSus_Height_Mode_OFF_495_S                                       0x08e68000a8001c00    // 0x495, 58.3-58.4, 悬架高度模式OFF开关
#define DiSus_Welcome_Sig_495_S                                           0x08e70000a8001c08    // 0x495, 58.5-58.6, 迎宾功能信号
#define DiSus_Rear_Suitcase_495_S                                         0x08e78000a8001c10    // 0x495, 58.7-59.0, 后行李箱取物开关
#define DiSus_Balance_Switch_Sig_495_S                                    0x08e80000a8001c18    // 0x495, 59.1-59.2, 露营调平功能开关
#define DiSus_Maintance_Switch_Sig_495_S                                  0x08e88000a8001c20    // 0x495, 59.3-59.4, 千斤顶/举升机功能
#define CVC_Work_Mode_495_S                                               0x08e90000a8001c28    // 0x495, 59.5-59.6, 底盘矢量控制开关
#define Inspector_g_bAllActPwrOff_S                                       0x08e98000a8001c30    // 0x495, 59.7, 悬架系统断电信号
#define Inspector_TempSig_S                                               0x08ea46a0a8001c38    // 0x495, 60.0-60.7, 悬架温度信号 PH=INT-50摄氏度
#define Inspector_IMU_Ax_S                                                0x08eac6b0a8001c40    // 0x495, 61.0-62.7, Inspector_IMU_Ax_S
#define Inspector_IMU_Ay_S                                                0x08eb46c0a8001c48    // 0x495, 63.0-64.7, Inspector_IMU_Ay_S
#define DiSus_Actual_Height_FL_495_S                                      0x08ebc6d0a8021c50    // 0x495, 2.0-3.3, 左前悬架实际高度_495
#define DiSus_Actual_Height_FR_495_S                                      0x08ec46e0a8021c58    // 0x495, 3.4-4.7, 右前悬架实际高度_495
#define DiSus_Actual_Height_RL_495_S                                      0x08ecc6f0a8021c60    // 0x495, 5.0-6.3, 左后悬架实际高度_495
#define DiSus_Actual_Height_RR_495_S                                      0x08ed4700a8021c68    // 0x495, 6.4-7.7, 右后悬架实际高度_495
#define Wakeup_Type_S                                                     0x08ed8000a8021c70    // 0x495, 8.0-8.2, 唤醒方式
#define NVM_Exception_S                                                   0x08ee0000a8021c78    // 0x495, 8.3-8.7, NVM异常
#define DiSus_Soft_Ver_495_S                                              0x08ee8000a8021c80    // 0x495, 9.0-9.5, 悬架软件版本
#define AFS_Scheme_Ver_495_S                                              0x08ef0000a8021c88    // 0x495, 9.6-9.7, 侧翼方案版本
#define DiEye_SettingStates_495_S                                         0x08ef8000a8021c90    // 0x495, 10.0-10.1, 预瞄设置开关
#define DiSus_HighAdj_Reason_495_S                                        0x08f00000a8021c98    // 0x495, 10.2-10.7, 悬架高度调节原因
#define AFS_Fault_Status_S                                                0x08f08000a8021ca0    // 0x495, 11.0-11.3, 侧翼故障状态
#define AFS_Work_Status_S                                                 0x08f10000a8021ca8    // 0x495, 11.4-11.7, 侧翼工作状态
#define DiSus_CofigWord_495_S                                             0x08f18000a8021cb0    // 0x495, 12.0-12.7, 悬架配置字
#define AFS_Press_Sensor_495_S                                            0x08f24710a8021cb8    // 0x495, 13.0-13.7, 侧翼压力传感器
#define Inspector_TempSig02_S                                             0x08f2c720a8021cc0    // 0x495, 14.0-15.0, 悬架温度信号02
#define Inspector_SBAD_ID02_S                                             0x08f30000a8021cc8    // 0x495, 15.1-15.5, 悬架高度抑制类型02
#define DiSus_AX                                                          0x08f3c730a8021cd0    // 0x495, 15.6-16.4, DiSus_AX
#define DiSus_AY                                                          0x08f44740a8021cd8    // 0x495, 16.5-17.3, DiSus_AY
#define AFS_Ctr_S                                                         0x08f48000a8021ce0    // 0x495, 17.4, 侧翼补气请求
#define AFS_Flag_S                                                        0x08f50000a8021ce8    // 0x495, 17.5, 允许侧翼补气标志位
#define AFS_State_S                                                       0x08f58000a8021cf0    // 0x495, 17.6-17.7, 侧翼补气状态
#define Medium_Enter_OTA_Mode_Req_S                                       0x10ea000460001cf8    // 0x49A, 1.4, 进入OTA模式需求
#define Rolling_Counter_0x49A                                             0x10ea800460001d00    // 0x49A, 7.4-7.7, 滚动计数校验
#define Temperature_Unit_4A5_S_S                                          0x00eb0004781a1d08    // 0x4A5, 7.7, 温度单位
#define Outside_Temperatu_Display_4A5_S                                   0x00ebc754781a1d10    // 0x4A5, 8.0-8.7, 车外温度显示
#define Height_Control_Status_4A8_S                                       0x20ec0004881c1d18    // 0x4A8, 2.0-2.7, 悬架高度控制状态
#define Suspension_Adjustment_Button_Additional_Parameters_S              0x10ec800490001d20    // 0x4A9, 7.1, 悬架调节按键附加参数
#define Suspension_Adjustment_Switch_Signal_S                             0x10ed000490001d28    // 0x4A9, 7.2-7.3, 悬架调节开关信号
#define g_ETS_In_Com_u8PADSW_S                                            0x20ed8004a81e1d30    // 0x4C1, 6.4-6.5, 电动尾翼软开关开闭指令（PAD开关下发）
#define g_ETS_In_Com_u8VoiceSW_S                                          0x20ee0004a81e1d38    // 0x4C1, 6.6-6.7, 电动尾翼语音开闭指令（PAD语音开闭下发）
#define g_ETS_In_Com_u8Welcome_S                                          0x20ee8004a81e1d40    // 0x4C1, 7.0-7.1, 尾翼迎宾功能设置项开关(PAD下发迎宾开关)
#define g_ETS_In_Com_u8Repair_S                                           0x20ef0004a81e1d48    // 0x4C1, 7.2-7.3, 电动尾翼维修功能(PAD下发维修开关)
#define g_ETS_In_Com_u8PADAuto_S                                          0x20ef8004a81e1d50    // 0x4C1, 8.4-8.5, 电动尾翼自动模式开关（PAD下发模式开关）
#define g_ETS_In_Com_u8VoiceAuto_S                                        0x20f00004a81e1d58    // 0x4C1, 8.6-8.7, 电动尾翼自动模式语音开关（PAD语音模式开关下发）
#define Working_Strength_Of_Main_Driver_Active_Flanks_S                   0x20f08004a8201d60    // 0x4C1, 3.0-3.1, 主驾主动侧翼工作强度
#define CoPilot_Active_Wing_Working_Strength_S                            0x20f10004a8201d68    // 0x4C1, 8.0-8.1, 副驾主动侧翼工作强度
#define Media_EPB_Switch                                                  0x20f18004b8221d70    // 0x4EF, 2.0-2.1, EPB软开关
#define DiSus_Customize_Height_Set_S                                      0x20f20004b8241d78    // 0x4EF, 2.0-2.2, DiSus自定义高度调节
#define DiSus_Customize_Combine_Height_Set_Enable_S                       0x20f28004b8241d80    // 0x4EF, 2.6-2.7, DiSus自定义组合高度调节使能信号
#define DiSus_Customize_Single_Height_Set_Enable_S                        0x20f30004b8241d88    // 0x4EF, 3.0-3.1, DiSus自定义单轮高度调节使能信号
#define DiSus_Customize_Combine_Height_Set_S                              0x20f38004b8241d90    // 0x4EF, 3.2-3.7, DiSus自定义组合高度调节信号
#define Height_Control_Off_S                                              0x20f40004b8241d98    // 0x4EF, 4.0-4.1, 高度控制OFF开关
#define DiSus_Maintance_Switch_S                                          0x20f48004b8241da0    // 0x4EF, 4.2-4.3, 千斤顶/举升机开关
#define DiSus_Welcome_Switch_S                                            0x20f50004b8241da8    // 0x4EF, 4.4-4.5, 迎宾功能信号
#define DiSus_Balance_Switch_S                                            0x20f58004b8241db0    // 0x4EF, 4.6-4.7, 露营调平功能开关
#define DiSus_Front_Suitcase_Switch_S                                     0x20f60004b8241db8    // 0x4EF, 5.0-5.1, 前行李箱取物开关
#define DiSus_Rear_Suitcase_Switch_S                                      0x20f68004b8241dc0    // 0x4EF, 5.2-5.3, 后行李箱取物开关
#define DiSus_Height_Adjust_Voice_S                                       0x20f70004b8241dc8    // 0x4EF, 5.4-5.5, 语音高度调节
#define Media_DiDyna_CVC_Work_Switch                                      0x20f78004b8241dd0    // 0x4EF, 5.6-5.7, PAD上的CVC开关
#define CCT_Fun_Switch_PAD_S                                              0x20f80004b8241dd8    // 0x4EF, 6.4-6.5, 舒适控制技术PAD开关
#define Media_CVC_Work_Mode_S                                             0x20f88004b8241de0    // 0x4EF, 6.6-6.7, 底盘矢量控制开关
#define Extra_Hi_Active_Status                                            0x20f90004b8241de8    // 0x4EF, 7.4-7.5, 超高激活状态
#define Extra_Low_Active_Status                                           0x20f98004b8241df0    // 0x4EF, 7.6-7.7, 超低激活状态
#define Media_DiSteer_A_Work_Mode_S                                       0x20fa0004b8261df8    // 0x4EF, 2.0-2.1, PAD后轮转向控制开关信号
#define Media_DiSteer_A_CrabWalk_Mode_S                                   0x20fa8004b8261e00    // 0x4EF, 2.2-2.3, PAD蟹行模式控制开关信号
#define FR_Safty_Blts_Remind_4FA_S                                        0x00fb0004c0001e08    // 0x4FA, 2.6-2.7, 副驾安全带未系提醒状态
#define RR_Sec_Safty_Blts_Remind_4FA_S                                    0x00fb8004c0001e10    // 0x4FA, 3.4-3.5, 右后二排安全带未系提醒状态
#define MR_Sec_Safty_Blts_Remind_4FA_S                                    0x00fc0004c0001e18    // 0x4FA, 3.2-3.3, 中后二排安全带未系提醒状态
#define RL_Sec_Safty_Blts_Remind_4FA_S                                    0x00fc8004c0001e20    // 0x4FA, 3.0-3.1, 左后二排安全带未系提醒状态

// IO信号内容
// 测试服务
#define T1_TEST                                                           0x3000000000000000    // Port: 182, T1测试信号

#endif
