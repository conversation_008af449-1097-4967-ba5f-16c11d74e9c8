#ifndef _SIGMAP_HEV_H_
#define _SIGMAP_HEV_H_

// CAN信号内容
#define DiDyna_LongitudinalTorque_S                                       0x0800400000000000    // 0x049, 1.0-2.7, 目标纵向制动力矩
#define Didyna_LFI_fault_status_S                                         0x0800800000000008    // 0x049, 3.0, 纵向制动力矩接口故障状态
#define DiDyna_LongitudinalTorqueRqst_S                                   0x0801000000000010    // 0x049, 3.1, 纵向制动力矩请求状态
#define DiDyna_Func_Active_S                                              0x0801800000000018    // 0x049, 3.2-3.5, DiDyna功能激活状态
#define Didyna_Sys_fault_status_S                                         0x0802000000000020    // 0x049, 4.0, DiDyna系统故障状态
#define DiDyna_Prefill_Req_S                                              0x0802800000000028    // 0x049, 4.1, 制动预充压请求标志
#define Rolling_counter_049_S                                             0x0803000000000030    // 0x049, 7.4-7.7, 计数器
#define LFI_Longitudinal_Torque_Interface_Function_Execution_Estimate_S   0x0000401000000038    // 0x04B, 1.0-2.7, LFI_纵向力矩接口功能执行估算值
#define LFI_Longitudinal_Torque_Interface_Function_Fault_Flag_S           0x0000800000000040    // 0x04B, 3.0, LFI_纵向力矩接口功能故障标志位
#define LFI_Longitudinal_Torque_Interface_Function_Activation_Flag_S      0x0001000000000048    // 0x04B, 3.1, LFI_纵向力矩接口功能激活标志位
#define Rolling_counter_04B_S                                             0x0001800000000050    // 0x04B, 7.4-7.7, 0x04B生命帧
#define Checksum04B_S                                                     0x0002000000000058    // 0x04B, 8.0-8.7, 0x04B校验码
#define Tv_actv_flg_S                                                     0x0002800010000060    // 0x04F, 1.0, TV激活标志
#define Tv_err_flg_S                                                      0x0003000010000068    // 0x04F, 1.1, TV故障标志
#define Dtcs_actv_flg_S                                                   0x0003800010000070    // 0x04F, 1.2, DTCS激活标志
#define Dtcs_err_flg_S                                                    0x0004000010000078    // 0x04F, 1.3, DTCS故障标志
#define Drift_Func_OnOff_State_S                                          0x0004800010000080    // 0x04F, 1.4-1.5, 漂移功能开关状态
#define MasterCylTq_Req_04F_S                                             0x0005000010000088    // 0x04F, 1.6-1.7, 主缸控制请求状态
#define MasterCylTq_Target_S                                              0x0005c02010000090    // 0x04F, 2.0-3.7, 主缸目标制动扭矩
#define CCU_Prefill_Req_S                                                 0x0006000010000098    // 0x04F, 4.0, 制动预充压请求标志
#define CCU_ADAS_ABA_Level_S                                              0x00068000100000a0    // 0x04F, 4.1-4.2, ADAS自适应制动辅助等级
#define CCU_ADAS_ABA_Req_S                                                0x00070000100000a8    // 0x04F, 4.3, ADAS自适应制动辅助有效位
#define CCU_VCURqstIPBFcn                                                 0x00078000100000b0    // 0x04F, 4.4-4.7, VCU请求IPB功能
#define Rolling_counter_04F_S                                             0x00080000100000b8    // 0x04F, 7.4-7.7, 0x04B生命帧
#define Checksum04F_S                                                     0x00088000100000c0    // 0x04F, 8.0-8.7, 0x04B校验码
#define Checknum_S                                                        0x00090000200000c8    // 0x051, 1.0-2.7, 校验码
#define Alive_Counter_S                                                   0x00098000200000d0    // 0x051, 3.0-4.7, Alive_counter
#define ACU_Wx_RollRate_S                                                 0x000a403020008000    // 0x051, 5.0-6.7, wx侧倾角速度
#define ACU_LongitudeAccelerationSensor_St_S                              0x000a8000200000d8    // 0x051, 7.0-7.1, X方向加速度状态
#define ACU_Wx_RollRateSensor_St_S                                        0x000b0000200000e0    // 0x051, 7.2-7.3, Wx侧倾角速度状态
#define YRS_YawRateCalSts_051_S                                           0x000b8000200000e8    // 0x051, 7.4, 偏航率传感器标定状态
#define ACU_Reserved01_S                                                  0x000c0000200000f0    // 0x051, 7.5-7.7, 信号扩展预留位
#define ACU_LongitudeAcceleration_S                                       0x000cc04020008008    // 0x051, 8.0-9.7, X方向加速度
#define ACU_Reserved02_S                                                  0x000d0000200000f8    // 0x051, 10.0-10.7, 信号扩展预留位
#define ACU_LateralAcceleration_S                                         0x000dc05020008010    // 0x051, 11.0-12.7, Y方向加速
#define ACU_Wy_PitchRate_S                                                0x000e406020008018    // 0x051, 13.0-14.7, Wy俯仰角速度
#define ACU_LateralAccelerationSensor_St_S                                0x000e800020000100    // 0x051, 15.0-15.1, LateralAccelarationSensorState Y方向加速度状态
#define ACU_Wy_PitchRateSensor_St_S                                       0x000f000020000108    // 0x051, 15.2-15.3, Wy_俯仰角速度状
#define ACU_2_Reserved4_S                                                 0x000f800020000110    // 0x051, 15.4-17.7, 信号扩展预留
#define ACU_VerticalAcceleration_S                                        0x0010407020008020    // 0x051, 18.0-19.7, Z方向加速
#define ACU_YawRate_S                                                     0x0010c08020008028    // 0x051, 20.0-21.7, Wz横摆角速
#define ACU_VerticalAccelerationSensor_St_S                               0x0011000020000118    // 0x051, 22.0-22.1, Z方向加速度状态
#define ACU_YawRateSensor_St_S                                            0x0011800020000120    // 0x051, 22.2-22.3, Wz横摆角速度状态
#define Reserved_S                                                        0x0012000020000128    // 0x051, 22.4-32.7, Reserved
#define Suspension_Motor_Switching_s_S                                    0x0803800010000130    // 0x05D, 1.0-1.1, 悬架泵电机开关信号
#define Suspension_Motor_Switching_Status_s_S                             0x0804000010000138    // 0x05D, 1.2, 悬架泵电机开关信号有效位
#define Rolling_Counter_05D_S                                             0x0804800010000140    // 0x05D, 7.4-7.7, 计数器
#define SRS_Collosion_Signal_S                                            0x1012800030000148    // 0x08C, 1.0-1.7, SRS_碰撞信号
#define Collosion_Signal_Checksum_S                                       0x1013000030000150    // 0x08C, 2.0-2.7, 碰撞信号校验码
#define Loss_Control_Loop_Counter_08C_S                                   0x1013800030000158    // 0x08C, 8.4-8.7, 丢包控制循环计数器_08C
#define DiSus_Cloud_UpDown_S                                              0x2014000048000160    // 0x0A2, 2.0-2.2, 手机云悬架高度调节
#define CRC_Checknum_0AB_S                                                0x0014800050000168    // 0x0AB, 1.0-2.7, 校验码0AB
#define Alive_Counter_0AB_S                                               0x0015000050000170    // 0x0AB, 3.0-4.7, 滚动循环计数器0AB
#define RCS_EPSAD_ScrewDisplacement_Req_S                                 0x0015800050000178    // 0x0AB, 5.0-5.2, RCS工作状态
#define RCS_EPSAD_Request_Type_S                                          0x0016000050000180    // 0x0AB, 5.3-5.4, 遥控后转控制类型
#define RCS_EPSAD_SteeringCtrlReqForEPSAD_S                               0x0016800050000188    // 0x0AB, 5.5, 遥控后转请求状态位
#define RCS_EPSAD_SteeringCtrlReqForEPSAD_VD_S                            0x0017000050000190    // 0x0AB, 5.6, 遥控驾驶对后轮角度请求有效位
#define RCS_Tar_StrAgl_S                                                  0x0017c09050000198    // 0x0AB, 6.0-7.7, 遥控驾驶对后轮角度请求
#define L_CRC_Checknum_0B6_S                                              0x00180000600001a0    // 0x0B6, 1.0-2.7, 左后轮校验码L_0B6
#define L_Alive_Counter_0B6_S                                             0x00188000600001a8    // 0x0B6, 3.0-4.7, 左后轮滚动循环计数器L_0B6
#define L_RWS_Status_S                                                    0x00190000600001b0    // 0x0B6, 5.0-5.2, 左后轮转向系统状态
#define L_RWS_Ctrl_Status_S                                               0x00198000600001b8    // 0x0B6, 5.3-5.4, 左后轮转向系统控制状态
#define L_RWS_Ctrl_Status_VD_S                                            0x001a0000600001c0    // 0x0B6, 5.5, 左后轮转向系统控制状态有效性
#define L_RWS_Screw_Displacement_VD_S                                     0x001a8000600001c8    // 0x0B6, 5.6, 左后轮实际丝杆位移量有效性
#define L_RWS_Screw_Displacement_Speed_VD_S                               0x001b0000600001d0    // 0x0B6, 5.7, 左后轮丝杆位移速度有效性
#define L_RWS_Screw_Displacement_S                                        0x001bc0a0600001d8    // 0x0B6, 6.0-7.3, 左后轮实际丝杆位移量
#define L_RWS_Screw_Displacement_Speed_S                                  0x001c40b0600001e0    // 0x0B6, 7.4-8.7, 左后轮丝杆位移速度
#define L_End_Protection_Value_S                                          0x001c8000600001e8    // 0x0B6, 9.0-9.7, 左后轮末端保护值
#define L_RWS_Motor_Rotation_Num_S                                        0x001d40c0600001f0    // 0x0B6, 10.0-11.7, 左后轮转向电机转动圈数
#define L_RWS_Motor_Rotation_S                                            0x001d8000600001f8    // 0x0B6, 12.0-13.7, 左后轮转向电机转速
#define L_RWS_ECU_Temperature_S                                           0x001e000060000200    // 0x0B6, 14.0-15.3, 左后轮转向ECU温度
#define L_Supply_Voltage_S                                                0x001ec0d060000208    // 0x0B6, 15.4-16.7, 左后轮供电电压
#define L_Line_Sensor_Value_S                                             0x001f000060000210    // 0x0B6, 18.0-19.3, 左后轮直线位移传感器值
#define L_RWS_Fail_Grade_S                                                0x001f800060000218    // 0x0B6, 19.4-19.5, 左后轮转向系统故障等级
#define L_RWS_Alarm_Light_S                                               0x0020000060000220    // 0x0B6, 19.6, 左后轮转向报警灯信号
#define L_ECU_Software_Version_S                                          0x0020800060000228    // 0x0B6, 20.0-21.7, 左后轮系统集成软件版本号
#define L_ECU_Software_Year_S                                             0x0021000060000230    // 0x0B6, 22.0-22.7, 左后轮软件版本年份
#define L_ECU_Software_Month_S                                            0x0021800060000238    // 0x0B6, 23.0-23.7, 左后轮软件版本月份
#define L_ECU_Software_Day_S                                              0x0022000060000240    // 0x0B6, 24.0-24.7, 左后轮软件版本日
#define L_ECU_Software_Number_S                                           0x0022800060000248    // 0x0B6, 25.0-25.7, 左后轮软件版本修改次数
#define L_RWS_Fault_Mask_S                                                0x0023000060000250    // 0x0B6, 26.0-29.7, 左后轮故障掩码
#define L_Motor_Q_Current_S                                               0x0023800060000258    // 0x0B6, 30.0-31.3, 左后轮电机Q轴电流值
#define L_Motor_D_Current_S                                               0x0024000060000260    // 0x0B6, 31.4-32.7, 左后轮电机D轴电流值
#define R_CRC_Checknum_0C0_S                                              0x0024800070000268    // 0x0C0, 1.0-2.7, 右后轮校验码R_0C0
#define R_Alive_Counter_0C0_S                                             0x0025000070000270    // 0x0C0, 3.0-4.7, 右后轮滚动循环计数器R_0C0
#define R_RWS_Status_S                                                    0x0025800070000278    // 0x0C0, 5.0-5.2, 右后轮转向系统状态
#define R_RWS_Ctrl_Status_S                                               0x0026000070000280    // 0x0C0, 5.3-5.4, 右后轮转向系统控制状态
#define R_RWS_Ctrl_Status_VD_S                                            0x0026800070000288    // 0x0C0, 5.5, 右后轮转向系统控制状态有效性
#define R_RWS_Screw_Displacement_VD_S                                     0x0027000070000290    // 0x0C0, 5.6, 右后轮实际丝杆位移量有效性
#define R_RWS_Screw_Displacement_Speed_VD_S                               0x0027800070000298    // 0x0C0, 5.7, 右后轮丝杆位移速度有效性
#define R_RWS_Screw_Displacement_S                                        0x002840e0700002a0    // 0x0C0, 6.0-7.3, 右后轮实际丝杆位移量
#define R_RWS_Screw_Displacement_Speed_S                                  0x0028c0f0700002a8    // 0x0C0, 7.4-8.7, 右后轮丝杆位移速度
#define R_End_Protection_Value_S                                          0x00290000700002b0    // 0x0C0, 9.0-9.7, 右后轮末端保护值
#define R_RWS_Motor_Rotation_Num_S                                        0x0029c100700002b8    // 0x0C0, 10.0-11.7, 右后轮转向电机转动圈数
#define R_RWS_Motor_Rotation_S                                            0x002a0000700002c0    // 0x0C0, 12.0-13.7, 右后轮转向电机转速
#define R_RWS_ECU_Temperature_S                                           0x002a8000700002c8    // 0x0C0, 14.0-15.3, 右后轮转向ECU温度
#define R_Supply_Voltage_S                                                0x002b4110700002d0    // 0x0C0, 15.4-16.7, 右后轮供电电压
#define R_Line_Sensor_Value_S                                             0x002b8000700002d8    // 0x0C0, 18.0-19.3, 右后轮直线位移传感器值
#define R_RWS_Fail_Grade_S                                                0x002c0000700002e0    // 0x0C0, 19.4-19.5, 右后轮转向系统故障等级
#define R_RWS_Alarm_Light_S                                               0x002c8000700002e8    // 0x0C0, 19.6, 右后轮转向报警灯信号
#define R_ECU_Software_Version_S                                          0x002d0000700002f0    // 0x0C0, 20.0-21.7, 右后轮系统集成软件版本号
#define R_ECU_Software_Year_S                                             0x002d8000700002f8    // 0x0C0, 22.0-22.7, 右后轮软件版本年份
#define R_ECU_Software_Month_S                                            0x002e000070000300    // 0x0C0, 23.0-23.7, 右后轮软件版本月份
#define R_ECU_Software_Day_S                                              0x002e800070000308    // 0x0C0, 24.0-24.7, 右后轮软件版本日
#define R_ECU_Software_Number_S                                           0x002f000070000310    // 0x0C0, 25.0-25.7, 右后轮软件版本修改次数
#define R_RWS_Fault_Mask_S                                                0x002f800070000318    // 0x0C0, 26.0-29.7, 右后轮故掩码
#define R_Motor_Q_Current_S                                               0x0030000070000320    // 0x0C0, 30.0-31.3, 右后轮电机Q轴电流
#define R_Motor_D_Current_S                                               0x0030800070000328    // 0x0C0, 31.4-32.7, 右后轮电机D轴电流
#define Suspension_Pump_Motor_Drive_Fault_Signal_S                        0x0031000080000330    // 0x0C4, 3.0-3.2, 悬架泵电机驱动故障信号
#define Suspension_Pump_Motor_Working_Status_Signal_S                     0x0031800080000338    // 0x0C4, 3.3-3.4, 悬架泵电机工作状态信号
#define Disus_Signal_Input_Status_S                                       0x0032000080000340    // 0x0C4, 3.5-3.6, Disus信号输入状态
#define DisusASU_working_current_S                                        0x0032c12080000348    // 0x0C4, 4.0-5.7, DisusASU工作电流
#define Rolling_Counter_0C4_S                                             0x0033000080000350    // 0x0C4, 7.4-7.7, 计数器
#define u16RodStroke_V                                                    0x0033c13090000358    // 0x0D5, 1.0-2.1, 制动踏板行程
#define bBrkRodStroke_S                                                   0x0034000090000360    // 0x0D5, 2.2, 制动踏板行程有效位
#define IPB_u8Status                                                      0x0034800090000368    // 0x0D5, 3.2-3.4, IPB状态信号MBB
#define IPB_Supplier                                                      0x0035000090000370    // 0x0D5, 4.7, IPB_Supplier
#define Decelerate_Demand                                                 0x0035800090000378    // 0x0D5, 5.0-5.7, 减速度需求
#define Brake_Req_Status                                                  0x0036000090000380    // 0x0D5, 7.0, 制动请求指令
#define Rolling_Counter_0x0D5                                             0x0036800090000388    // 0x0D5, 7.4-7.7, 滚动计数校验
#define CRC_Checknum_0F4_S                                                0x00370000a0000390    // 0x0F4, 1.0-2.7, 0x0F4校验码
#define Alive_Counter_0F4_S                                               0x00378000a0000398    // 0x0F4, 3.0-4.7, 0x0F4生命帧
#define OK_Lamp                                                           0x00380000a00003a0    // 0x0F4, 16.4-16.5, OK指示灯
#define VCU_Goal_Gear_S                                                   0x00388000a00003a8    // 0x0F4, 17.0-17.3, 目标挡位
#define TCS_Active_S                                                      0x00390000a00003b0    // 0x0F4, 19.2, CCU_DTCS激活标志
#define TCS_Fault_0F4_S                                                   0x00398000a00003b8    // 0x0F4, 19.3, CCU_DTCS故障标志
#define CCU_Pkg_FuncEcho_S                                                0x003a0000a00003c0    // 0x0F4, 20.2-20.3, CCU泊车状态
#define CCU_Pkg_Available_S                                               0x003a8000a00003c8    // 0x0F4, 20.4, CCU泊车有效标志
#define CCU_EPBBrReq_S                                                    0x003b0000a00003d0    // 0x0F4, 20.6-20.7, CCU请求EPB状态
#define Veh_drv_mod_S                                                     0x003b8000a00003d8    // 0x0F4, 21.0-21.7, 整车驾驶模式
#define CCU_Vehicle_DrivingMode_Valid_S                                   0x003c0000a00003e0    // 0x0F4, 22.0, 整车驾驶模式有效标志
#define CCU_RddtbrakeActive_S                                             0x003c8000a00003e8    // 0x0F4, 22.3, 备份制动激活标志
#define CCU_EPBBrReq_VD_S                                                 0x003d0000a00003f0    // 0x0F4, 24.1, 请求EPB状态有效标志
#define VOT_Actv_Sat                                                      0x003d8000a00003f8    // 0x0F4, 24.7, VOT功能激活标志
#define CCU_System_Status_S                                               0x003e0000a0000400    // 0x0F4, 25.1-25.4, CCU制动系统状态
#define VehicleHold_Active                                                0x003e8000a0000408    // 0x0F4, 26.7, 静止保持功能激活
#define CCU_Vehicle_Hold_Active                                           0x003f0000a0000410    // 0x0F4, 27.2, 静止保持功能激活标志
#define VOT_PreReq_S                                                      0x003f8000a0000418    // 0x0F4, 35.6, VOT保压功能激活标志
#define Drift_Func_OnOff_State                                            0x00400000a0000420    // 0x0F4, 46.3-46.4, 漂移功能开/关状态
#define CRC_Checknum_0FC_S                                                0x00408000b0000428    // 0x0FC, 1.0-2.7, CRC_Checknum_0FC_S
#define Alive_Counter_0FC_S                                               0x00410000b0000430    // 0x0FC, 3.0-4.7, Alive_counter_0FC
#define RL_mot_whl_tar_tq_S                                               0x00418000b0000438    // 0x0FC, 9.0-10.7, 左后电机轮端目标扭矩
#define RR_mot_whl_tar_tq_S                                               0x00420000b0000440    // 0x0FC, 11.0-12.7, 右后电机轮端目标扭矩
#define Vehicle_Torque_FL_0FC_S                                           0x00428000b0000448    // 0x0FC, 13.0-14.7, 左前电机实际扭矩
#define Vehicle_Torque_FR_0FC_S                                           0x00430000b0000450    // 0x0FC, 15.0-16.7, 右前电机实际扭矩
#define Vehicle_Torque_RL_0FC_S                                           0x00438000b0000458    // 0x0FC, 17.0-18.7, 左后电机实际扭矩
#define Vehicle_Torque_RR_0FC_S                                           0x00440000b0000460    // 0x0FC, 19.0-20.7, 右后电机实际扭矩
#define RL_mot_whl_tar_tq_efc_flg_S                                       0x00448000b0000468    // 0x0FC, 21.2, 左后电机轮端目标扭矩有效标志
#define RR_mot_whl_tar_tq_efc_flg_S                                       0x00450000b0000470    // 0x0FC, 21.3, 右后电机轮端目标扭矩有效标志
#define Torque_State_FL_0FC_S                                             0x00458000b0000478    // 0x0FC, 21.4, 左前电机实际扭矩有效标志
#define Torque_State_FR_0FC_S                                             0x00460000b0000480    // 0x0FC, 21.5, 右前电机实际扭矩有效标志
#define Torque_State_RL_0FC_S                                             0x00468000b0000488    // 0x0FC, 21.6, 左后电机实际扭矩有效标志
#define Torque_State_RR_0FC_S                                             0x00470000b0000490    // 0x0FC, 21.7, 右后电机实际扭矩有效标志
#define RL_mot_spd_S                                                      0x00478000b0000498    // 0x0FC, 26.0-27.7, 左后电机转速
#define RR_mot_spd_S                                                      0x00480000b00004a0    // 0x0FC, 28.0-29.7, 右后电机转速
#define RL_mot_spd_efc_flg_S                                              0x00488000b00004a8    // 0x0FC, 30.2, 左后电机转速有效标志
#define RR_mot_spd_efc_flg_S                                              0x00490000b00004b0    // 0x0FC, 30.3, 右后电机转速有效标志
#define whl_spd_FL_S                                                      0x00498000b00004b8    // 0x0FC, 30.4-31.7, IPB左前轮速
#define whl_spd_FR_S                                                      0x004a0000b00004c0    // 0x0FC, 32.0-33.3, IPB右前轮速
#define whl_spd_RL_S                                                      0x004a8000b00004c8    // 0x0FC, 33.4-34.7, IPB左后轮速
#define whl_spd_RR_S                                                      0x004b0000b00004d0    // 0x0FC, 35.0-36.3, IPB右后轮速
#define FL_whl_spd_efc_flg_S                                              0x004b8000b00004d8    // 0x0FC, 36.4, IPB左前轮速有效标志
#define FR_whl_spd_efc_flg_S                                              0x004c0000b00004e0    // 0x0FC, 36.5, IPB右前轮速有效标志
#define RL_whl_spd_efc_flg_S                                              0x004c8000b00004e8    // 0x0FC, 36.6, IPB左后轮速有效标志
#define RR_whl_spd_efc_flg_S                                              0x004d0000b00004f0    // 0x0FC, 36.7, IPB右后轮速有效标志
#define CRC_Checknum_0FE_S                                                0x0805000020008030    // 0x0FE, 1.0-2.7, 校验码0FE
#define Alive_Counter_0FE_S                                               0x0805800020008038    // 0x0FE, 3.0-4.7, 滚动循环计数器0FE
#define EPSA_Con_Status_RWS_S                                             0x08060000200004f8    // 0x0FE, 5.0-5.1, EPSA对RWS执行器的控制状态
#define EPSA_Con_Status_VD_RWS_S                                          0x0806800020000500    // 0x0FE, 5.2, EPSA对RWS执行器控制状态有效性
#define EPSA_Screw_Disp_Request_RWS_S                                     0x0807000020000508    // 0x0FE, 5.3, EPSA对RWS执行器丝杆位移控制请求
#define EPSA_Screw_Disp_Request_VD_RWS_S                                  0x0807800020000510    // 0x0FE, 5.4, EPSA对RWS执行器丝杆位移控制请求有效性
#define EPSA_Screw_Disp_Demand_RWS_S                                      0x0808414020000518    // 0x0FE, 6.0-7.7, EPSA对RWS执行器丝杆位移请求
#define EPSA_Screw_Disp_Demand_VD_RWS_S                                   0x0808800020000520    // 0x0FE, 8.0, EPSA对RWS执行器丝杆位移请求有效性
#define EPSA_Work_Configuration_S                                         0x0809000020000528    // 0x0FE, 8.1, 后轮转向控制开关配置
#define EPSA_Work_Signal_S                                                0x0809800020000530    // 0x0FE, 8.2-8.3, 后轮转向控制开关信号
#define EPSA_CrabWalk_Configuration_S                                     0x080a000020000538    // 0x0FE, 8.4, 蟹行模式控制开关配置
#define EPSA_CrabWalk_Signal_S                                            0x080a800020000540    // 0x0FE, 8.5-8.6, 蟹行模式控制开关信号
#define EPSA_Error_Signal_S                                               0x080b000020000548    // 0x0FE, 9.0-9.2, 后轮转向相关功能异常信号
#define EPSA_Gray_Signal_S                                                0x080b800020000550    // 0x0FE, 9.3, 后轮转向相关按钮置灰信号
#define EPSA_Out_Mon_bHandSAbnormalFault                                  0x080c000020000558    // 0x0FE, 9.4, 握手状态异常
#define EPSA_Out_Mon_bHandSTimeoutFault                                   0x080c800020000560    // 0x0FE, 9.5, 握手超时故障
#define EPSA_Out_Mon_bFunEnaFault1                                        0x080d000020000568    // 0x0FE, 9.6, 握手失败功能异常使能故障
#define EPSA_Out_Mon_bFunEnaFault2                                        0x080d800020000570    // 0x0FE, 9.7, 多功能异常使能故障
#define EPSA_Out_Mon_bFunEnaCntFault                                      0x080e000020000578    // 0x0FE, 10.0, 功能使能计数异常故障
#define EPSA_Out_Mon_bADASFunEnaFault                                     0x080e800020000580    // 0x0FE, 10.1, ADAS异常使能状态位
#define EPSA_Out_Mon_bArbiAngDisableFault                                 0x080f000020000588    // 0x0FE, 10.2, 转角归零仲裁故障状态位
#define EPSA_Out_Mon_bArbiAngLimitFault                                   0x080f800020000590    // 0x0FE, 10.3, 转角变化速率超限仲裁故障状态位
#define EPSA_Out_Mon_bArbiAngAmpFault                                     0x0810000020000598    // 0x0FE, 10.4, 转角阈值仲裁故障状态位
#define EPSA_Out_Mon_bAngDirFail                                          0x08108000200005a0    // 0x0FE, 10.5, 高速转角方向异常故障状态位
#define EPSA_Out_Mon_bAngExecuteFault                                     0x08110000200005a8    // 0x0FE, 10.6, 后轮转角执行故障状态位
#define EPSA_Out_Mon_bAngRateRespFault                                    0x08118000200005b0    // 0x0FE, 10.7, 后轮转角控制反向故障状态位
#define EPSA_Out_Mon_bRWSComLossFault                                     0x08120000200005b8    // 0x0FE, 11.0, EPSA与RWS通信丢失
#define EPSA_Out_Mon_bBCMComLossFault                                     0x08128000200005c0    // 0x0FE, 11.1, EPSA与BCM通信丢失
#define EPSA_Out_Mon_bVCUComLossFault                                     0x08130000200005c8    // 0x0FE, 11.2, EPSA与VCU通信丢失
#define EPSA_Out_Mon_bEPSComLossFault                                     0x08138000200005d0    // 0x0FE, 11.3, EPSA与EPS通信丢失
#define EPSA_Out_Mon_bIPBComLossFault                                     0x08140000200005d8    // 0x0FE, 11.4, EPSA与IPB通信丢失
#define EPSA_Out_Mon_bSCUComLossFault                                     0x08148000200005e0    // 0x0FE, 11.5, EPSA与SCU通信丢失
#define EPSA_Out_Mon_bADASComLossFault                                    0x08150000200005e8    // 0x0FE, 11.6, EPSA与ADAS通信丢失
#define EPSA_Out_Mon_bSRSComLossFault                                     0x08158000200005f0    // 0x0FE, 11.7, EPSA与SRS通信丢失
#define EPSA_Out_Mon_bRWSComLossFault_L                                   0x08160000200005f8    // 0x0FE, 12.0, EPSA与左侧后轮转向器丢失通信
#define EPSA_Out_Mon_bRWSComLossFault_R                                   0x0816800020000600    // 0x0FE, 12.1, EPSA与右侧后轮转向器丢失通信
#define EPSA_Out_Mon_bRCSComLossFault                                     0x0817000020000608    // 0x0FE, 12.2, EPSA与RCS通信丢失
#define EPSA_Out_Mon_bCCUComLossFault                                     0x0817800020000610    // 0x0FE, 12.3, EPSA与CCU通信丢失
#define EPSA_Out_Mon_bConfigBitLossFault                                  0x0818000020000618    // 0x0FE, 13.0, EPSA配置字丢失
#define EPSA_Out_Mon_bEPSA_Self_Fault                                     0x0818800020000620    // 0x0FE, 13.1, EPSA安全机制自检故障
#define EPSA_Con_Status_RWS_L_S                                           0x0819000020000628    // 0x0FE, 15.0-15.1, EPSA对RWS左执行器的控制状态
#define EPSA_Con_Status_VD_RWS_L_S                                        0x0819800020000630    // 0x0FE, 15.2, EPSA对RWS左执行器控制状态有效性
#define EPSA_Screw_Disp_Request_RWS_L_S                                   0x081a000020000638    // 0x0FE, 15.3, EPSA对RWS左执行器丝杆位移控制请求
#define EPSA_Screw_Disp_Request_VD_RWS_L_S                                0x081a800020000640    // 0x0FE, 15.4, EPSA对RWS左执行器丝杆位移控制请求有效性
#define EPSA_Screw_Disp_Demand_VD_RWS_L_S                                 0x081b000020000648    // 0x0FE, 15.5, EPSA对RWS左执行器丝杆位移请求有效性
#define EPSA_Screw_Disp_Demand_RWS_L_S                                    0x081bc15020000650    // 0x0FE, 16.0-17.7, EPSA对RWS左执行器丝杆位移请求
#define EPSA_Con_Status_RWS_R_S                                           0x081c000020000658    // 0x0FE, 19.0-19.1, EPSA对RWS右执行器的控制状态
#define EPSA_Con_Status_VD_RWS_R_S                                        0x081c800020000660    // 0x0FE, 19.2, EPSA对RWS右执行器控制状态有效性
#define EPSA_Screw_Disp_Request_RWS_R_S                                   0x081d000020000668    // 0x0FE, 19.3, EPSA对RWS右执行器丝杆位移控制请求
#define EPSA_Screw_Disp_Request_VD_RWS_R_S                                0x081d800020000670    // 0x0FE, 19.4, EPSA对RWS右执行器丝杆位移控制请求有效性
#define EPSA_Screw_Disp_Demand_VD_RWS_R_S                                 0x081e000020000678    // 0x0FE, 19.5, EPSA对RWS右执行器丝杆位移请求有效性
#define EPSA_Screw_Disp_Demand_RWS_R_S                                    0x081ec16020000680    // 0x0FE, 20.0-21.7, EPSA对RWS右执行器丝杆位移请求
#define EPSA_Security_Detect                                              0x081f000020000688    // 0x0FE, 22.0-22.2, EPSA安全机制检测
#define EPSA_ActFcnMod                                                    0x081f800020000690    // 0x0FE, 22.3-22.6, EPSA实际功能模式
#define EPSA_ActualRearWhlAgl_VD_S                                        0x0820000020000698    // 0x0FE, 22.7, EPSA实际后轮转动角度有效性
#define EPSA_ActualRearWhlAgl_L                                           0x0820c170200006a0    // 0x0FE, 23.0-24.3, EPSA左后轮实际转动角度
#define EPSA_ActualRearWhlAgl_R                                           0x08214180200006a8    // 0x0FE, 24.4-25.7, EPSA右后轮实际转动角度
#define CRC_Checknum_109_S                                                0x08218000300006b0    // 0x109, 1.0-2.7, CRC_Checknum_109_S
#define Alive_Counter_109_S                                               0x08220000300006b8    // 0x109, 3.0-4.7, Alive_Counter_109_S
#define DiSus_APP_Self_Learning_S                                         0x08228000300006c0    // 0x109, 5.0, 云辇APP配置信号
#define DiSus_Actual_Height_Invalid_FL_S                                  0x08230000300006c8    // 0x109, 9.0, 左前悬架实际高度无效
#define DiSus_Actual_Height_Invalid_FR_S                                  0x08238000300006d0    // 0x109, 9.1, 右前悬架实际高度无效
#define DiSus_Actual_Height_Invalid_RL_S                                  0x08240000300006d8    // 0x109, 9.2, 左后悬架实际高度无效
#define DiSus_Actual_Height_Invalid_RR_S                                  0x08248000300006e0    // 0x109, 9.3, 右后悬架实际高度无效
#define DiSus_Mode_Adjust_Status_S                                        0x08250000300006e8    // 0x109, 9.4, 悬架可调节状态
#define DiSus_Mode_Execute_Status_S                                       0x08258000300006f0    // 0x109, 9.5-9.7, 悬架调节状态
#define DiSus_Actual_Driving_Mode_S                                       0x08260000300006f8    // 0x109, 10.0-10.7, 悬架实际驾驶模式 （集成爆胎模式）
#define DiSus_Actual_Height_FL_S                                          0x0826c19030000700    // 0x109, 11.0-12.7, 左前悬架实际高度
#define DiSus_Actual_Height_FR_S                                          0x082741a030000708    // 0x109, 13.0-14.7, 右前悬架实际高度
#define DiSus_Actual_Height_RL_S                                          0x0827c1b030000710    // 0x109, 15.0-16.7, 左后悬架实际高度
#define DiSus_Actual_Height_RR_S                                          0x082841c030000718    // 0x109, 17.0-18.7, 右后悬架实际高度
#define IMU_Ax_S                                                          0x0828c1d030000720    // 0x109, 19.0-20.7, IMU_Ax
#define IMU_Ay_S                                                          0x082941e030000728    // 0x109, 21.0-22.7, IMU_Ay
#define IMU_Az_S                                                          0x0829c1f030000730    // 0x109, 23.0-24.7, IMU_Az
#define IMU_RollRate_S                                                    0x082a420030000738    // 0x109, 25.0-26.7, IMU_RollRate
#define IMU_PitchRate_S                                                   0x082ac21030000740    // 0x109, 27.0-28.7, IMU_PitchRate
#define IMU_YawRate_S                                                     0x082b422030000748    // 0x109, 29.0-30.7, IMU_YawRate
#define DiSus_Type_S                                                      0x082b800030000750    // 0x109, 35.0-35.3, DiSus的类型区分
#define DiSus_Pitch_Angle                                                 0x082c423030000758    // 0x109, 35.4-36.6, 车身俯仰角
#define DiSus_Roll_Angle                                                  0x082cc24030000760    // 0x109, 36.7-38.1, 车身侧倾角
#define DiSus_Ground_Clearance                                            0x082d000030000768    // 0x109, 38.2-39.3, 离地间隙
#define DiSus_Vehicle_Height                                              0x082d800030000770    // 0x109, 39.4-40.7, 车身高度
#define Inspector_Damper_FL_S                                             0x082e000030000778    // 0x109, 41.0-42.7, 左前线性阀输出电流
#define Inspector_Damper_FR_S                                             0x082e800030000780    // 0x109, 43.0-44.7, 右前线性阀输出电流
#define Inspector_Damper_RL_S                                             0x082f000030000788    // 0x109, 45.0-46.7, 左后线性阀输出电流
#define Inspector_Damper_RR_S                                             0x082f800030000790    // 0x109, 47.0-48.7, 右后线性阀输出电流
#define Inspector_APP_DriveSig_S                                          0x0830000030000798    // 0x109, 49.0-50.7, ASW高度执行器控制信号
#define Inspector_ECUMode_109_S                                           0x08308000300007a0    // 0x109, 51.0-51.3, ECU模式
#define Inspector_SJ_ID_S                                                 0x08310000300007a8    // 0x109, 51.4-52.3, 悬架抑制类型2
#define Inspector_PressureSig_109_S                                       0x08318000300007b0    // 0x109, 52.4-53.0, 悬架压力信号 单位bar
#define Inspector_TempSig_109_S                                           0x08320000300007b8    // 0x109, 53.1-54.1, 悬架温度信号 PH=INT-200摄氏度
#define Inspector_SBAD_ID_109_S                                           0x08328000300007c0    // 0x109, 54.2-55.1, 悬架高度抑制类型
#define Inspector_Error_ID_109_S                                          0x08330000300007c8    // 0x109, 55.2-57.1, ASW故障ID
#define Inspector_ActorOutSig_S                                           0x08338000300007d0    // 0x109, 58.0-59.7, 所有执行器输出信号
#define AFS_Pressure_109_S                                                0x08340000300007d8    // 0x109, 60.0-61.7, 侧翼储气罐压力信号
#define AFS_Ctr_109_S                                                     0x08348000300007e0    // 0x109, 62.0-62.7, 侧翼补气请求
#define Alive_counter_10C_S                                               0x004d8000c00007e8    // 0x10C, 3.0-4.7, 滚动计数
#define IPB_Vehicle_Speed_10C_S                                           0x004e4250c00007f0    // 0x10C, 5.3-6.6, Vehicle_speed
#define Vehicle_Speed_Stats_10C_S                                         0x004e8000c00007f8    // 0x10C, 6.7, VehicleSpeed_Status
#define Wheel_Speed_FL_S                                                  0x004f4260c0000800    // 0x10C, 7.0-8.3, WheelSpeed_FL
#define Wheel_Speed_FR_S                                                  0x004fc270c0000808    // 0x10C, 8.4-9.7, WheelSpeed_FR
#define Wheel_Speed_RL_S                                                  0x00504280c0008040    // 0x10C, 10.0-11.3, WheelSpeed_RL
#define Wheel_Speed_RR_S                                                  0x0050c290c0008048    // 0x10C, 11.4-12.7, WheelSpeed_RR
#define Wheel_Speed_FR_Stats_S                                            0x00510000c0000810    // 0x10C, 13.0, WheelSpeed_FR_Status
#define Wheel_Speed_FL_Stats_S                                            0x00518000c0000818    // 0x10C, 13.1, WheelSpeed_FL_Status
#define Wheel_Speed_RR_Stats_S                                            0x00520000c0000820    // 0x10C, 13.2, WheelSpeed_RR_Status
#define WheelSpeed_RL_Status_S                                            0x00528000c0000828    // 0x10C, 13.3, WheelSpeed_RL_Status
#define EBD_Active_10C_S                                                  0x00530000c0000830    // 0x10C, 20.4, EBD_Active
#define EBD_Fault_10C_S                                                   0x00538000c0000838    // 0x10C, 20.5, EBD_Fault
#define ABS_Active_10C_S                                                  0x00540000c0000840    // 0x10C, 20.6, ABS_Active
#define ABS_Fault_10C_S                                                   0x00548000c0000848    // 0x10C, 20.7, ABS_Fault
#define VDC_Active_10C_S                                                  0x00550000c0000850    // 0x10C, 21.0, VDCActive
#define VDC_Fault_10C_S                                                   0x00558000c0000858    // 0x10C, 21.1, VDC_Fault
#define Brake_Pedal_Status_10C_S                                          0x00560000c0000860    // 0x10C, 44.5-44.6, IPB_BRAKE_PEDAL_STARUS
#define IPB_Plunger_Pres_status_10C_S                                     0x00568000c0000868    // 0x10C, 46.4, IPB_PlungerPressure_status
#define IPB_Plunger_Pressure_10C_S                                        0x005742a0c0000870    // 0x10C, 46.6-48.1, IPB_PlungerPressure Plunger压力
#define ECM_Engine_Rev10D_S                                               0x0057c2b0d0000878    // 0x10D, 1.0-2.7, 发动机转速
#define Engine_Indicated_Trq_S                                            0x005842c0e0000880    // 0x10E, 1.0-1.7, 发动机指示扭矩
#define CRC_Checknum_112_S                                                0x0835000040008050    // 0x112, 1.0-2.7, 校验码112
#define Alive_Counter_112_S                                               0x0835800040008058    // 0x112, 3.0-4.7, 滚动循环计数器112
#define EPSA_Angle_S                                                      0x083642d040000888    // 0x112, 5.0-6.2, 后轮实际转动角度
#define EPSA_Angle_VD_S                                                   0x0836800040000890    // 0x112, 6.3, 后轮实际转动角度有效性
#define EPSA_AngularVelocity_S                                            0x083742e040000898    // 0x112, 6.4-7.3, 后轮转动角速度
#define EPSA_Controller_Status_ADAS_S                                     0x08378000400008a0    // 0x112, 7.4-7.6, EPSA控制器状态
#define EPSA_Controller_Status_VD_ADAS_S                                  0x08380000400008a8    // 0x112, 7.7, EPSA控制器状态有效性
#define EPSA_LockCondition_ADAS_S                                         0x08388000400008b0    // 0x112, 8.0, 后轮锁止状态
#define EPSA_Controller_Status_RCS_S                                      0x08390000400008b8    // 0x112, 10.0-10.2, EPSA控制器与RCS交互控制状态
#define EPSA_Controller_Status_VD_RCS_S                                   0x08398000400008c0    // 0x112, 10.3, EPSA控制器与RCS交互控制状态有效性
#define EPSA_LockCondition_RCS_S                                          0x083a0000400008c8    // 0x112, 10.4, 后轮RCS锁止状态
#define Vehicle_speed                                                     0x0058c2f0f00008d0    // 0x116, 7.6-9.2, Vehicle_speed
#define WheelSpeed_FL                                                     0x00594300f00008d8    // 0x116, 9.3-10.7, WheelSpeed_FL
#define WheelSpeed_FR                                                     0x0059c310f00008e0    // 0x116, 11.0-12.4, WheelSpeed_FR
#define WheelSpeed_RL                                                     0x005a4320f00008e8    // 0x116, 12.5-14.1, WheelSpeed_RL
#define WheelSpeed_RR                                                     0x005ac330f00008f0    // 0x116, 14.2-15.6, WheelSpeed_RR
#define WheelSpeed_FL_Status                                              0x005b0000f00008f8    // 0x116, 15.7 , WheelSpeed_FL_Status
#define WheelSpeed_FR_Status                                              0x005b8000f0000900    // 0x116, 16.0 , WheelSpeed_FR_Status
#define WheelSpeed_RL_Status                                              0x005c0000f0000908    // 0x116, 16.1 , WheelSpeed_RL_Status
#define WheelSpeed_RR_Status                                              0x005c8000f0000910    // 0x116, 16.2 , WheelSpeed_RR_Status
#define VDC_Actual_Style_State                                            0x005d0000f0000918    // 0x116, 16.3-16.4, VDC实际风格状态
#define Rolling_Counter_116_S                                             0x005d8000f0000920    // 0x116, 63.4-63.7, 滚动计数器
#define Steering_Wheel_Angle_S                                            0x005e434100000928    // 0x11F, 1.0-2.7, 方向盘角度 Steeringwheelangle
#define Steering_Wheel_Rotation_Speed_S                                   0x005ec35100000930    // 0x11F, 3.0-3.7, 方向盘旋转速度 Steeringwheelrotationspeed
#define Failure_Stats_OK_S                                                0x005f000100000938    // 0x11F, 4.0, Failure_StatusOK
#define Sensor_Calibration_Stats_S                                        0x005f800100000940    // 0x11F, 4.1, 传感器校准状态 CalibrationStatus
#define TRIM_Trimming_Stats_S                                             0x0060000100000948    // 0x11F, 4.2, 传感器平衡状态
#define Rolling_Counter_11F_S                                             0x0060800100000950    // 0x11F, 5.0-5.3, 滚动循环计数器11F
#define IPB_Vehicle_Speed_121_S                                           0x0061436110000958    // 0x121, 1.0-2.3, 车速
#define Vehicle_Speed_Stats_121_S                                         0x0061800110000960    // 0x121, 2.7, 车速信号状态位
#define Prefill_Available                                                 0x0062000110000968    // 0x121, 5.0, Prefill可用信号
#define Prefill_Active                                                    0x0062800110000970    // 0x121, 5.1, Prefill响应信号
#define Rolling_Counter_121_S                                             0x0063000110000978    // 0x121, 7.0-7.3, 滚动循环计数器121
#define WheelSpeed_FL_122_S                                               0x0063c37120008060    // 0x122, 1.0-2.3, 左前轮速
#define WheelSpeed_FR_Status_122_S                                        0x0064000120000980    // 0x122, 2.4, 右前轮速有效位
#define WheelSpeed_FL_Status_122_S                                        0x0064800120000988    // 0x122, 2.5, 左前轮速有效位
#define WheelSpeed_RR_Status_122_S                                        0x0065000120000990    // 0x122, 2.6, 右后轮速有效位
#define WheelSpeed_RL_Status_122_S                                        0x0065800120000998    // 0x122, 2.7, 左后轮速有效位
#define WheelSpeed_FR_122_S                                               0x0066438120008068    // 0x122, 3.0-4.3, 右前轮速
#define EBD_Active_122_S                                                  0x00668001200009a0    // 0x122, 4.4, EBD_Active_122
#define ABS_Active_122_S                                                  0x00670001200009a8    // 0x122, 4.5, ABS功能触发信号
#define EBD_Fault_122_S                                                   0x00678001200009b0    // 0x122, 4.6, EBD_Fault_122
#define ABS_Fault_122_S                                                   0x00680001200009b8    // 0x122, 4.7, ABS功能触发信号有效性信号
#define WheelSpeed_RL_122_S                                               0x0068c39120008070    // 0x122, 5.0-6.3, 左后轮速
#define AWD_Trq_Req_Method_S                                              0x00690001200009c0    // 0x122, 6.4-6.5, AWDTrqReqMethod
#define TCS_Active_122_S                                                  0x00698001200009c8    // 0x122, 6.6, TCSActive_122
#define DTC_Active_122_S                                                  0x006a0001200009d0    // 0x122, 6.7, DTCActive_122
#define WheelSpeed_RR_122_S                                               0x006ac3a120008078    // 0x122, 7.0-8.3, 右后轮速
#define VDC_Active_122_S                                                  0x006b0001200009d8    // 0x122, 8.4, VDCActive_122
#define AVH_Failure                                                       0x006b8001200009e0    // 0x122, 8.5, AVH故障判断状态位
#define AVH_CtrlStatus                                                    0x006c0001200009e8    // 0x122, 8.6-8.7, AVH功能状态位
#define HHC_Fault                                                         0x006c8001300009f0    // 0x123, 1.0, HHC（Hill-Hold-Control）功能状态
#define IPB_BRAKE_PEDAL_STATUS                                            0x006d0001300009f8    // 0x123, 6.3-6.4, 制动踏板状态信号
#define Rolling_Counter_0x123                                             0x006d800130000a00    // 0x123, 7.0-7.3, 滚动计数校验
#define ESC_Active                                                        0x006e000130000a08    // 0x123, 7.5, ESC（ElectronicStabilityController）工作状态
#define TCS_Fault_123_S                                                   0x006e800130000a10    // 0x123, 7.6, TCS功能触发信号有效性信号
#define VDC_Fault_123_S                                                   0x006f000130000a18    // 0x123, 7.7, VDC功能触发信号有效性信号
#define Didyna_LDI_fault_status                                           0x083a800050000a20    // 0x129, 2.7, 横摆力矩接口故障状态
#define DiDyna_CVC_YawTorqueRqst_S                                        0x083b000050000a28    // 0x129, 5.3, 横摆力矩请求状态
#define DiDyna_CVC_TarYawTorque_S                                         0x083bc3b050008080    // 0x129, 5.4-7.3, 目标横摆力矩
#define IG1_Relay_Status_S                                                0x006f800140000a30    // 0x12D, 1.6-1.7, IG1继电器状态
#define Gateway_Engine_Stop_Inform_12D_S                                  0x0070000140000a38    // 0x12D, 4.6, 退电通知
#define BCMPower_Gear_12D_S                                               0x0070800140000a40    // 0x12D, 5.2-5.4, 电源档位
#define Rolling_Counter_12D_S                                             0x0071000140000a48    // 0x12D, 7.4-7.7, 循环丢包计数器
#define Didyna_TBC_Wheelstate_S                                           0x083c000060000a50    // 0x132, 7.0-7.2, 爆胎轮识别信号
#define Didyna_TBC_Status_S                                               0x083c800060000a58    // 0x132, 7.3, 爆胎稳定控制激活状态信号
#define DiDyna_TBC_StrTrqRqst_S                                           0x083d000060000a60    // 0x132, 7.4-7.5, EPS附加助力力矩请求状态
#define DiDyna_TBC_StrTrqt_S                                              0x083dc3c060000a68    // 0x132, 7.6-9.1, EPS附加转向助力矩请求值
#define DiDyna_TBC_EPS_Shkhds                                             0x083e000060000a70    // 0x132, 9.2, EPS握手请求信号
#define DiDyna_MBB_EPB_i16Ax_V                                            0x083e800060000a78    // 0x132, 9.3-9.4, EPB请求状态
#define DiDyna_MBB_bStatus                                                0x083f000060000a80    // 0x132, 11.4, MBB开启状态
#define Str_act_tq                                                        0x0071c3d150000a88    // 0x135, 4.0-5.3, 方向盘实际扭矩
#define CRC_Checknum_139_S                                                0x0072000160000a90    // 0x139, 1.0-2.7, 校验码139
#define Alive_Counter_139_S                                               0x0072800160000a98    // 0x139, 3.0-4.7, 滚动循环计数器139
#define ACC_DecTo_Stop_Req_S                                              0x0073000160000aa0    // 0x139, 10.0, ACC停车请求
#define ACC_Mode_S                                                        0x0073800160000aa8    // 0x139, 10.3-10.5, ACC模式
#define ADS_PkgRequest_S                                                  0x0074000160000ab0    // 0x139, 11.2, 泊车功能握手请求
#define ADS_PkgFunctions_S                                                0x0074800160000ab8    // 0x139, 11.3-11.4, 当前请求的功能
#define ADS_MEBBrakeEmgcy_S                                               0x0075000160000ac0    // 0x139, 16.7, MEB紧急制动请求
#define ADS_MEB_Status_S                                                  0x0075800160000ac8    // 0x139, 18.0-18.2, MEB状态信号
#define ADS2RWS_RearSteeringCtrlReq_S                                     0x0076000160000ad0    // 0x139, 18.7, 交互握手 控制请求
#define ADS2RWS_TargetRWheelAngle_S                                       0x0076c3e160000ad8    // 0x139, 19.0-20.2, 控制信号 后轮转角请求目标值
#define ADS2RWS_RearSteeringCtrlReqVD_S                                   0x0077000160000ae0    // 0x139, 20.4, 交互握手 控制请求有效位
#define Park2RWS_Function_S                                               0x0077800160000ae8    // 0x139, 33.0-33.2, 泊车功能请求
#define ADS2RWS_Request_Type_S                                            0x0078000160000af0    // 0x139, 33.3-33.4, 交互握手请求类型
#define Driving2RWS_Function_S                                            0x0078800160000af8    // 0x139, 33.5-33.7, 行车功能请求
#define DiDyna_EHI_Switch_S                                               0x083f800070000b00    // 0x143, 4.3-4.4, EHI开关状态
#define Rolling_counter_143_S                                             0x0840000070000b08    // 0x143, 7.4-7.7, 生命帧
#define Drift_State_S                                                     0x0079000170000b10    // 0x147, 3.1-3.2, 漂移功能状态信号
#define Drift_PAD_Switch_Request_S                                        0x0079800170000b18    // 0x147, 3.7-4.0, 漂移开关模式返回值
#define EPB_to_DiDyna_Req_S                                               0x007a000180000b20    // 0x149, 1.0, EPB响应Didyna请求
#define Pre_Clamp_Available_S                                             0x007a800180000b28    // 0x149, 1.1, 预夹紧可用状态
#define Clamp_Available_S                                                 0x007b000180000b30    // 0x149, 1.2, 夹紧可用状态
#define Rolling_counter_149_S                                             0x007b800180000b38    // 0x149, 7.0-7.3, 滚动计数器
#define Act_drv_mod                                                       0x007c000190000b40    // 0x17C, 2.0-2.7, 实际驾驶模式
#define Key_Cls_To_The_Vechile_S                                          0x207c8001a0000b48    // 0x180, 2.2-2.3, 钥匙靠近车辆
#define Vehicle_Torque_183_S                                              0x007d43f1b0000b50    // 0x183, 1.0-2.7, 前轴总扭矩
#define F_Indicated_Driver_Req_Torq_183_S                                 0x007dc401b0000b58    // 0x183, 3.0-4.7, 前轴驾驶员需求指示扭矩
#define Front_Torque_State_183_S                                          0x007e0001b0000b60    // 0x183, 5.0, 前轴扭矩状态
#define F_Indicat_Driver_Req_Torq_Stat_183_S                              0x007e8001b0000b68    // 0x183, 5.1, 前轴驾驶员需求指示扭矩状态
#define Sld_fb_tar_tq_sat                                                 0x007f0001b0000b70    // 0x183, 7.0, 滑行回馈目标扭矩状态
#define Sld_fb_tar_tq_val                                                 0x007fc411b0008088    // 0x183, 5.2-6.7, 滑行回馈目标扭矩值
#define Msg_Cnt_0x183                                                     0x00800001b0000b78    // 0x183, 7.4-7.7, 0x183生命帧
#define Checksum183_S                                                     0x00808001b0000b80    // 0x183, 8.0-8.7, 0x183校验码
#define LDI_imateYawTorque                                                0x00814421c0008090    // 0x1A2, 5.0-6.7, 附加横摆力矩值
#define LDI_Available                                                     0x00818001c0000b88    // 0x1A2, 7.4, LDI可用信号
#define LDI_Active                                                        0x00820001c0000b90    // 0x1A2, 7.5, LDI激活信号
#define LDI_State                                                         0x00828001c0000b98    // 0x1A2, 7.6-7.7, 附加横摆力矩状态
#define YRS_LatAcce_S                                                     0x00834431d0000ba0    // 0x1CC, 1.0-2.7, 横向加速度AY
#define YRS_YawRate_S                                                     0x0083c441d0000ba8    // 0x1CC, 3.0-4.7, 指示偏航率
#define YRS_LatSnsrSts_S                                                  0x00840001d0000bb0    // 0x1CC, 5.0-5.1, 横向通道的状态
#define YRS_YawRateSnsrSts_1CC_S                                          0x00848001d0000bb8    // 0x1CC, 5.2-5.3, 偏航率通道的状态
#define YRS_YawRateCalSts_1CC_S                                           0x00850001d0000bc0    // 0x1CC, 6.0, 偏航率传感器标定状态
#define Rolling_Counter_1CC_S                                             0x00858001d0000bc8    // 0x1CC, 7.4-7.7, 滚动循环计数器1CC
#define SuspInhibitReq1E2_S                                               0x00860001e0000bd0    // 0x1E2, 7.0-7.2, 抑制悬架请求
#define SuspInhibitReq1E2_Valid_S                                         0x00868001e0000bd8    // 0x1E2, 7.3, 抑制悬架请求有效位
#define Rolling_Counter_1E2_S                                             0x00870001e0000be0    // 0x1E2, 7.4-7.7, MsgCounter1E2
#define Wheel_Speed_FL_1F0_S                                              0x0087c451f0000be8    // 0x1F0, 1.0-2.3, WheelSpeed_FL_1F0
#define Wheel_Speed_FR_Stats_1F0_S                                        0x00880001f0000bf0    // 0x1F0, 2.4, WheelSpeed_FR_Status_1F0
#define Wheel_Speed_FL_Stats_1F0_S                                        0x00888001f0000bf8    // 0x1F0, 2.5, WheelSpeed_FL_Status_1F0
#define Wheel_Speed_RR_Stats_1F0_S                                        0x00890001f0000c00    // 0x1F0, 2.6, WheelSpeed_RR_Status_1F0
#define WheelSpeed_RL_Status_1F0_S                                        0x00898001f0000c08    // 0x1F0, 2.7, WheelSpeed_RL_Status_1F0
#define Wheel_Speed_FR_1F0_S                                              0x008a4461f0000c10    // 0x1F0, 3.0-4.3, WheelSpeed_FR_1F0
#define Wheel_Speed_RL_1F0_S                                              0x008ac471f0000c18    // 0x1F0, 4.4-5.7, WheelSpeed_RL_1F0
#define Wheel_Speed_RR_1F0_S                                              0x008b4481f0000c20    // 0x1F0, 6.0-7.3, WheelSpeed_RR_1F0
#define Rolling_Counter_1F0_S                                             0x008b8001f0000c28    // 0x1F0, 7.4-7.7, Messagecounter_1F0
#define YRS_LgtAcce_S                                                     0x008c449200000c30    // 0x1FA, 1.0-2.7, 纵向加速度AX
#define YRS_LgtSnsrSts_S                                                  0x008c800200000c38    // 0x1FA, 3.0-3.1, 纵向通道的状态
#define Rolling_Counter_1FA_S                                             0x008d000200000c40    // 0x1FA, 7.4-7.7, 滚动循环计数器1FA
#define DiEye_Left_Road_1_S                                               0x008d800218020c48    // 0x213, 2.0-2.2, 左路面特征信号1
#define DiEye_Left_Road_2_S                                               0x008e000218020c50    // 0x213, 2.3-2.5, 左路面特征信号2
#define DiEye_Internal_Fault_S                                            0x008e800218020c58    // 0x213, 2.6, 内部故障
#define DiEye_Camera_Signal_Fault_S                                       0x008f000218020c60    // 0x213, 2.7, 预瞄摄像头信号异常
#define DiEye_Left_Road_3_S                                               0x008f800218020c68    // 0x213, 3.0-3.2, 左路面特征信号3
#define DiEye_Left_Elevation_Grade_1_S                                    0x0090000218020c70    // 0x213, 3.3-3.5, 左高程等级1
#define DiEye_Left_Elevation_Grade_2_S                                    0x0090800218020c78    // 0x213, 3.6-4.0, 左高程等级2
#define DiEye_Right_Road_Signal_I_S                                       0x0091000218020c80    // 0x213, 4.1-4.3, 右路面特征信号1
#define DiEye_Left_Elevation_Grade_3_S                                    0x0091800218020c88    // 0x213, 4.4-4.6, 左高程等级3
#define DiEye_Right_Road_Signal_2_S                                       0x0092000218020c90    // 0x213, 4.7-5.1, 右路面特征信号2
#define DiEye_Right_Road_Signal_3_S                                       0x0092800218020c98    // 0x213, 5.2-5.4, 右路面特征信号3
#define DiEye_Left_lengthways_Dis_1_S                                     0x0093000218028098    // 0x213, 5.5-6.7, 左特征纵向距离1
#define DiEye_Left_lengthways_Dis_2_S                                     0x00938002180280a0    // 0x213, 7.0-8.2, 左特征纵向距离2
#define DiEye_Left_lengthways_Dis_3_S                                     0x00940002180280a8    // 0x213, 8.3-9.5, 左特征纵向距离3
#define DiEye_Right_Elevation_Grade_1_S                                   0x0094800218020ca0    // 0x213, 9.6-10.0, 右高程等级1
#define DiEye_Right_Elevation_Grade_2_S                                   0x0095000218020ca8    // 0x213, 10.1-10.3, 右高程等级2
#define DiEye_Dieye_System_Status_S                                       0x0095800218020cb0    // 0x213, 10.4-10.5, 预瞄系统状态
#define DiEye_Terrain_System_Status_S                                     0x0096000218020cb8    // 0x213, 10.6-10.7, 全地形检测系统状态
#define DiEye_Right_Elevation_Grade_3_S                                   0x0096800218020cc0    // 0x213, 11.0-11.2, 右高程等级3
#define DiEye_Left_Road_Scene_Quality_S                                   0x0097000218020cc8    // 0x213, 11.3-11.5, 左轮道路场景类型品质
#define DiEye_Right_Road_Scene_Quality_S                                  0x0097800218020cd0    // 0x213, 11.6-12.0, 右轮道路场景类型品质
#define DiEye_Right_lengthways_Dis_1_S                                    0x00980002180280b0    // 0x213, 12.1-13.3, 右特征纵向距离1
#define DiEye_Right_lengthways_Dis_2_S                                    0x00988002180280b8    // 0x213, 13.4-14.6, 右特征纵向距离2
#define DiEye_Right_lengthways_Dis_3_S                                    0x00990002180280c0    // 0x213, 14.7-16.1, 右特征纵向距离3
#define DiEye_Road_Scene_Decide_S                                         0x0099800218020cd8    // 0x213, 16.2-16.3, 道路场景判定
#define DiEye_Left_Road_Type_S                                            0x009a000218020ce0    // 0x213, 16.4-16.7, 左轮路面类型
#define DiEye_Right_Road_Type_S                                           0x009a800218020ce8    // 0x213, 17.0-17.3, 右轮路面类型
#define Alive_counter213_01_S                                             0x009b0002180280c8    // 0x213, 61.0-62.7, 生命帧
#define DiEye_Left_Elevation_Line_0_S                                     0x009bc4a2280480d0    // 0x217, 2.0-3.0, 左高程曲线数据点0
#define DiEye_Left_Elevation_Line_1_S                                     0x009c44b2280480d8    // 0x217, 3.1-4.1, 左高程曲线数据点1
#define DiEye_Left_Elevation_Line_2_S                                     0x009cc4c2280480e0    // 0x217, 4.2-5.2, 左高程曲线数据点2
#define DiEye_Left_Elevation_Line_3_S                                     0x009d44d2280480e8    // 0x217, 5.3-6.3, 左高程曲线数据点3
#define DiEye_Left_Elevation_Line_4_S                                     0x009dc4e2280480f0    // 0x217, 6.4-7.4, 左高程曲线数据点4
#define DiEye_Left_Elevation_Line_5_S                                     0x009e44f2280480f8    // 0x217, 7.5-8.5, 左高程曲线数据点5
#define DiEye_Left_Elevation_Line_6_S                                     0x009ec50228048100    // 0x217, 8.6-9.6, 左高程曲线数据点6
#define DiEye_Left_Elevation_Line_7_S                                     0x009f451228048108    // 0x217, 9.7-10.7, 左高程曲线数据点7
#define DiEye_Left_Elevation_Line_8_S                                     0x009fc52228048110    // 0x217, 11.0-12.0, 左高程曲线数据点8
#define DiEye_Left_Elevation_Line_9_S                                     0x00a0453228048118    // 0x217, 12.1-13.1, 左高程曲线数据点9
#define DiEye_Left_Elevation_Line_10_S                                    0x00a0c54228048120    // 0x217, 13.2-14.2, 左高程曲线数据点10
#define DiEye_Left_Elevation_Line_11_S                                    0x00a1455228048128    // 0x217, 14.3-15.3, 左高程曲线数据点11
#define DiEye_Left_Elevation_Line_12_S                                    0x00a1c56228048130    // 0x217, 15.4-16.4, 左高程曲线数据点12
#define DiEye_Left_Elevation_Line_13_S                                    0x00a2457228048138    // 0x217, 16.5-17.5, 左高程曲线数据点13
#define DiEye_Left_Elevation_Line_14_S                                    0x00a2c58228048140    // 0x217, 17.6-18.6, 左高程曲线数据点14
#define DiEye_Left_Elevation_Line_15_S                                    0x00a3459228048148    // 0x217, 18.7-19.7, 左高程曲线数据点15
#define DiEye_Left_Elevation_Line_16_S                                    0x00a3c5a228048150    // 0x217, 20.0-21.0, 左高程曲线数据点16
#define DiEye_Left_Elevation_Line_17_S                                    0x00a445b228048158    // 0x217, 21.1-22.1, 左高程曲线数据点17
#define DiEye_Left_Elevation_Line_18_S                                    0x00a4c5c228048160    // 0x217, 22.2-23.2, 左高程曲线数据点18
#define DiEye_Left_Elevation_Line_19_S                                    0x00a545d228048168    // 0x217, 23.3-24.3, 左高程曲线数据点19
#define DiEye_Left_Elevation_Line_20_S                                    0x00a5c5e228048170    // 0x217, 24.4-25.4, 左高程曲线数据点20
#define DiEye_Left_Elevation_Line_21_S                                    0x00a645f228048178    // 0x217, 25.5-26.5, 左高程曲线数据点21
#define DiEye_Left_Elevation_Line_22_S                                    0x00a6c60228048180    // 0x217, 26.6-27.6, 左高程曲线数据点22
#define DiEye_Left_Elevation_Line_23_S                                    0x00a7461228048188    // 0x217, 27.7-28.7, 左高程曲线数据点23
#define DiEye_Left_Elevation_Line_24_S                                    0x00a7c62228048190    // 0x217, 29.0-30.0, 左高程曲线数据点24
#define DiEye_Left_Elevation_Line_25_S                                    0x00a8463228048198    // 0x217, 30.1-31.1, 左高程曲线数据点25
#define DiEye_Left_Elevation_Line_26_S                                    0x00a8c642280481a0    // 0x217, 31.2-32.2, 左高程曲线数据点26
#define DiEye_Left_Elevation_Line_27_S                                    0x00a94652280481a8    // 0x217, 32.3-33.3, 左高程曲线数据点27
#define DiEye_Left_Elevation_Line_28_S                                    0x00a9c662280481b0    // 0x217, 33.4-34.4, 左高程曲线数据点28
#define DiEye_Left_Elevation_Line_29_S                                    0x00aa4672280481b8    // 0x217, 34.5-35.5, 左高程曲线数据点29
#define DiEye_Left_Elevation_Line_30_S                                    0x00aac682280481c0    // 0x217, 35.6-36.6, 左高程曲线数据点30
#define DiEye_Left_Elevation_Line_31_S                                    0x00ab4692280481c8    // 0x217, 36.7-37.7, 左高程曲线数据点31
#define DiEye_Left_Elevation_Line_32_S                                    0x00abc6a2280481d0    // 0x217, 38.0-39.0, 左高程曲线数据点32
#define DiEye_Left_Elevation_Line_33_S                                    0x00ac46b2280481d8    // 0x217, 39.1-40.1, 左高程曲线数据点33
#define DiEye_Left_Elevation_Line_34_S                                    0x00acc6c2280481e0    // 0x217, 40.2-41.2, 左高程曲线数据点34
#define DiEye_Left_Elevation_Line_35_S                                    0x00ad46d2280481e8    // 0x217, 41.3-42.3, 左高程曲线数据点35
#define DiEye_Left_Elevation_Line_36_S                                    0x00adc6e2280481f0    // 0x217, 42.4-43.4, 左高程曲线数据点36
#define DiEye_Left_Elevation_Line_37_S                                    0x00ae46f2280481f8    // 0x217, 43.5-44.5, 左高程曲线数据点37
#define DiEye_Left_Elevation_Line_38_S                                    0x00aec70228048200    // 0x217, 44.6-45.6, 左高程曲线数据点38
#define DiEye_Left_Elevation_Line_39_S                                    0x00af471228048208    // 0x217, 45.7-46.7, 左高程曲线数据点39
#define DiEye_Left_Elevation_Line_40_S                                    0x00afc72228048210    // 0x217, 47.0-48.0, 左高程曲线数据点40
#define DiEye_Left_Elevation_Line_41_S                                    0x00b0473228048218    // 0x217, 48.1-49.1, 左高程曲线数据点41
#define DiEye_Left_Elevation_Line_42_S                                    0x00b0c74228048220    // 0x217, 49.2-50.2, 左高程曲线数据点42
#define DiEye_Left_Elevation_Line_43_S                                    0x00b1475228048228    // 0x217, 50.3-51.3, 左高程曲线数据点43
#define DiEye_Left_Elevation_Line_44_S                                    0x00b1c76228048230    // 0x217, 51.4-52.4, 左高程曲线数据点44
#define DiEye_Left_Elevation_Line_45_S                                    0x00b2477228048238    // 0x217, 52.5-53.5, 左高程曲线数据点45
#define DiEye_Left_Elevation_Line_46_S                                    0x00b2c78228048240    // 0x217, 53.6-54.6, 左高程曲线数据点46
#define DiEye_Left_Elevation_Line_47_S                                    0x00b3479228048248    // 0x217, 54.7-55.7, 左高程曲线数据点47
#define DiEye_Left_Elevation_Line_48_S                                    0x00b3c7a228048250    // 0x217, 56.0-57.0, 左高程曲线数据点48
#define DiEye_Left_Elevation_Line_49_S                                    0x00b447b228048258    // 0x217, 57.1-58.1, 左高程曲线数据点49
#define DiEye_Left_Elevation_Line_50_S                                    0x00b4c7c228048260    // 0x217, 58.2-59.2, 左高程曲线数据点50
#define DiEye_Left_Elevation_Line_51_S                                    0x00b547d228048268    // 0x217, 59.3-60.3, 左高程曲线数据点51
#define Alive_counter217_01_S                                             0x00b5800228048270    // 0x217, 61.0-62.7, 生命帧
#define DiEye_Left_Elevation_Line_52_S                                    0x00b647e228068278    // 0x217, 2.0-3.0, 左高程曲线数据点52
#define DiEye_Left_Elevation_Line_53_S                                    0x00b6c7f228068280    // 0x217, 3.1-4.1, 左高程曲线数据点53
#define DiEye_Left_Elevation_Line_54_S                                    0x00b7480228068288    // 0x217, 4.2-5.2, 左高程曲线数据点54
#define DiEye_Left_Elevation_Line_55_S                                    0x00b7c81228068290    // 0x217, 5.3-6.3, 左高程曲线数据点55
#define DiEye_Left_Elevation_Line_56_S                                    0x00b8482228068298    // 0x217, 6.4-7.4, 左高程曲线数据点56
#define DiEye_Left_Elevation_Line_57_S                                    0x00b8c832280682a0    // 0x217, 7.5-8.5, 左高程曲线数据点57
#define DiEye_Left_Elevation_Line_58_S                                    0x00b94842280682a8    // 0x217, 8.6-9.6, 左高程曲线数据点58
#define DiEye_Left_Elevation_Line_59_S                                    0x00b9c852280682b0    // 0x217, 9.7-10.7, 左高程曲线数据点59
#define DiEye_Left_Elevation_Line_60_S                                    0x00ba4862280682b8    // 0x217, 11.0-12.0, 左高程曲线数据点60
#define DiEye_Left_Elevation_Line_61_S                                    0x00bac872280682c0    // 0x217, 12.1-13.1, 左高程曲线数据点61
#define DiEye_Left_Elevation_Line_62_S                                    0x00bb4882280682c8    // 0x217, 13.2-14.2, 左高程曲线数据点62
#define DiEye_Left_Elevation_Line_63_S                                    0x00bbc892280682d0    // 0x217, 14.3-15.3, 左高程曲线数据点63
#define DiEye_Left_Elevation_Line_64_S                                    0x00bc48a2280682d8    // 0x217, 15.4-16.4, 左高程曲线数据点64
#define DiEye_Left_Elevation_Line_65_S                                    0x00bcc8b2280682e0    // 0x217, 16.5-17.5, 左高程曲线数据点65
#define DiEye_Left_Elevation_Line_66_S                                    0x00bd48c2280682e8    // 0x217, 17.6-18.6, 左高程曲线数据点66
#define DiEye_Left_Elevation_Line_67_S                                    0x00bdc8d2280682f0    // 0x217, 18.7-19.7, 左高程曲线数据点67
#define DiEye_Left_Elevation_Line_68_S                                    0x00be48e2280682f8    // 0x217, 20.0-21.0, 左高程曲线数据点68
#define DiEye_Left_Elevation_Line_69_S                                    0x00bec8f228068300    // 0x217, 21.1-22.1, 左高程曲线数据点69
#define DiEye_Left_Elevation_Line_70_S                                    0x00bf490228068308    // 0x217, 22.2-23.2, 左高程曲线数据点70
#define DiEye_Left_Elevation_Line_71_S                                    0x00bfc91228068310    // 0x217, 23.3-24.3, 左高程曲线数据点71
#define DiEye_Left_Elevation_Line_72_S                                    0x00c0492228068318    // 0x217, 24.4-25.4, 左高程曲线数据点72
#define DiEye_Left_Elevation_Line_73_S                                    0x00c0c93228068320    // 0x217, 25.5-26.5, 左高程曲线数据点73
#define DiEye_Left_Elevation_Line_74_S                                    0x00c1494228068328    // 0x217, 26.6-27.6, 左高程曲线数据点74
#define DiEye_Left_Elevation_Line_75_S                                    0x00c1c95228068330    // 0x217, 27.7-28.7, 左高程曲线数据点75
#define DiEye_Left_Elevation_Line_76_S                                    0x00c2496228068338    // 0x217, 29.0-30.0, 左高程曲线数据点76
#define DiEye_Left_Elevation_Line_77_S                                    0x00c2c97228068340    // 0x217, 30.1-31.1, 左高程曲线数据点77
#define DiEye_Left_Elevation_Line_78_S                                    0x00c3498228068348    // 0x217, 31.2-32.2, 左高程曲线数据点78
#define DiEye_Left_Elevation_Line_79_S                                    0x00c3c99228068350    // 0x217, 32.3-33.3, 左高程曲线数据点79
#define DiEye_Left_Elevation_Line_80_S                                    0x00c449a228068358    // 0x217, 33.4-34.4, 左高程曲线数据点80
#define DiEye_Left_Elevation_Line_81_S                                    0x00c4c9b228068360    // 0x217, 34.5-35.5, 左高程曲线数据点81
#define DiEye_Left_Elevation_Line_82_S                                    0x00c549c228068368    // 0x217, 35.6-36.6, 左高程曲线数据点82
#define DiEye_Left_Elevation_Line_83_S                                    0x00c5c9d228068370    // 0x217, 36.7-37.7, 左高程曲线数据点83
#define DiEye_Left_Elevation_Line_84_S                                    0x00c649e228068378    // 0x217, 38.0-39.0, 左高程曲线数据点84
#define DiEye_Left_Elevation_Line_85_S                                    0x00c6c9f228068380    // 0x217, 39.1-40.1, 左高程曲线数据点85
#define DiEye_Left_Elevation_Line_86_S                                    0x00c74a0228068388    // 0x217, 40.2-41.2, 左高程曲线数据点86
#define DiEye_Left_Elevation_Line_87_S                                    0x00c7ca1228068390    // 0x217, 41.3-42.3, 左高程曲线数据点87
#define DiEye_Left_Elevation_Line_88_S                                    0x00c84a2228068398    // 0x217, 42.4-43.4, 左高程曲线数据点88
#define DiEye_Left_Elevation_Line_89_S                                    0x00c8ca32280683a0    // 0x217, 43.5-44.5, 左高程曲线数据点89
#define DiEye_Left_Elevation_Line_90_S                                    0x00c94a42280683a8    // 0x217, 44.6-45.6, 左高程曲线数据点90
#define DiEye_Left_Elevation_Line_91_S                                    0x00c9ca52280683b0    // 0x217, 45.7-46.7, 左高程曲线数据点91
#define DiEye_Left_Elevation_Line_92_S                                    0x00ca4a62280683b8    // 0x217, 47.0-48.0, 左高程曲线数据点92
#define DiEye_Left_Elevation_Line_93_S                                    0x00caca72280683c0    // 0x217, 48.1-49.1, 左高程曲线数据点93
#define DiEye_Left_Elevation_Line_94_S                                    0x00cb4a82280683c8    // 0x217, 49.2-50.2, 左高程曲线数据点94
#define DiEye_Left_Elevation_Line_95_S                                    0x00cbca92280683d0    // 0x217, 50.3-51.3, 左高程曲线数据点95
#define DiEye_Left_Elevation_Line_96_S                                    0x00cc4aa2280683d8    // 0x217, 51.4-52.4, 左高程曲线数据点96
#define DiEye_Left_Elevation_Line_97_S                                    0x00cccab2280683e0    // 0x217, 52.5-53.5, 左高程曲线数据点97
#define DiEye_Left_Elevation_Line_98_S                                    0x00cd4ac2280683e8    // 0x217, 53.6-54.6, 左高程曲线数据点98
#define DiEye_Left_Elevation_Line_99_S                                    0x00cdcad2280683f0    // 0x217, 54.7-55.7, 左高程曲线数据点99
#define DiEye_Left_Elevation_Line_100_S                                   0x00ce4ae2280683f8    // 0x217, 56.0-57.0, 左高程曲线数据点100
#define DiEye_Left_Elevation_Line_101_S                                   0x00cecaf228068400    // 0x217, 57.1-58.1, 左高程曲线数据点101
#define DiEye_Left_Elevation_Line_102_S                                   0x00cf4b0228068408    // 0x217, 58.2-59.2, 左高程曲线数据点102
#define DiEye_Left_Elevation_Line_103_S                                   0x00cfcb1228068410    // 0x217, 59.3-60.3, 左高程曲线数据点103
#define Alive_counter217_02_S                                             0x00d0000228068418    // 0x217, 61.0-62.7, 生命帧
#define DiEye_Left_Elevation_Line_104_S                                   0x00d0cb2228088420    // 0x217, 2.0-3.0, 左高程曲线数据点104
#define DiEye_Left_Elevation_Line_105_S                                   0x00d14b3228088428    // 0x217, 3.1-4.1, 左高程曲线数据点105
#define DiEye_Left_Elevation_Line_106_S                                   0x00d1cb4228088430    // 0x217, 4.2-5.2, 左高程曲线数据点106
#define DiEye_Left_Elevation_Line_107_S                                   0x00d24b5228088438    // 0x217, 5.3-6.3, 左高程曲线数据点107
#define DiEye_Left_Elevation_Line_108_S                                   0x00d2cb6228088440    // 0x217, 6.4-7.4, 左高程曲线数据点108
#define DiEye_Left_Elevation_Line_109_S                                   0x00d34b7228088448    // 0x217, 7.5-8.5, 左高程曲线数据点109
#define DiEye_Left_Elevation_Line_110_S                                   0x00d3cb8228088450    // 0x217, 8.6-9.6, 左高程曲线数据点110
#define DiEye_Left_Elevation_Line_111_S                                   0x00d44b9228088458    // 0x217, 9.7-10.7, 左高程曲线数据点111
#define DiEye_Left_Elevation_Line_112_S                                   0x00d4cba228088460    // 0x217, 11.0-12.0, 左高程曲线数据点112
#define DiEye_Left_Elevation_Line_113_S                                   0x00d54bb228088468    // 0x217, 12.1-13.1, 左高程曲线数据点113
#define DiEye_Left_Elevation_Line_114_S                                   0x00d5cbc228088470    // 0x217, 13.2-14.2, 左高程曲线数据点114
#define DiEye_Left_Elevation_Line_115_S                                   0x00d64bd228088478    // 0x217, 14.3-15.3, 左高程曲线数据点115
#define DiEye_Left_Elevation_Line_116_S                                   0x00d6cbe228088480    // 0x217, 15.4-16.4, 左高程曲线数据点116
#define DiEye_Left_Elevation_Line_117_S                                   0x00d74bf228088488    // 0x217, 16.5-17.5, 左高程曲线数据点117
#define DiEye_Left_Elevation_Line_118_S                                   0x00d7cc0228088490    // 0x217, 17.6-18.6, 左高程曲线数据点118
#define DiEye_Left_Elevation_Line_119_S                                   0x00d84c1228088498    // 0x217, 18.7-19.7, 左高程曲线数据点119
#define DiEye_Left_Elevation_Line_120_S                                   0x00d8cc22280884a0    // 0x217, 20.0-21.0, 左高程曲线数据点120
#define DiEye_Left_Elevation_Line_121_S                                   0x00d94c32280884a8    // 0x217, 21.1-22.1, 左高程曲线数据点121
#define DiEye_Left_Elevation_Line_122_S                                   0x00d9cc42280884b0    // 0x217, 22.2-23.2, 左高程曲线数据点122
#define DiEye_Left_Elevation_Line_123_S                                   0x00da4c52280884b8    // 0x217, 23.3-24.3, 左高程曲线数据点123
#define DiEye_Left_Elevation_Line_124_S                                   0x00dacc62280884c0    // 0x217, 24.4-25.4, 左高程曲线数据点124
#define DiEye_Left_Elevation_Line_125_S                                   0x00db4c72280884c8    // 0x217, 25.5-26.5, 左高程曲线数据点125
#define DiEye_Left_Elevation_Line_126_S                                   0x00dbcc82280884d0    // 0x217, 26.6-27.6, 左高程曲线数据点126
#define DiEye_Left_Elevation_Line_127_S                                   0x00dc4c92280884d8    // 0x217, 27.7-28.7, 左高程曲线数据点127
#define DiEye_Left_Elevation_Line_128_S                                   0x00dccca2280884e0    // 0x217, 29.0-30.0, 左高程曲线数据点128
#define DiEye_Left_Elevation_Line_129_S                                   0x00dd4cb2280884e8    // 0x217, 30.1-31.1, 左高程曲线数据点129
#define DiEye_Left_Elevation_Line_130_S                                   0x00ddccc2280884f0    // 0x217, 31.2-32.2, 左高程曲线数据点130
#define DiEye_Left_Elevation_Line_131_S                                   0x00de4cd2280884f8    // 0x217, 32.3-33.3, 左高程曲线数据点131
#define DiEye_Left_Elevation_Line_132_S                                   0x00decce228088500    // 0x217, 33.4-34.4, 左高程曲线数据点132
#define DiEye_Left_Elevation_Line_133_S                                   0x00df4cf228088508    // 0x217, 34.5-35.5, 左高程曲线数据点133
#define DiEye_Left_Elevation_Line_134_S                                   0x00dfcd0228088510    // 0x217, 35.6-36.6, 左高程曲线数据点134
#define DiEye_Left_Elevation_Line_135_S                                   0x00e04d1228088518    // 0x217, 36.7-37.7, 左高程曲线数据点135
#define DiEye_Left_Elevation_Line_136_S                                   0x00e0cd2228088520    // 0x217, 38.0-39.0, 左高程曲线数据点136
#define DiEye_Left_Elevation_Line_137_S                                   0x00e14d3228088528    // 0x217, 39.1-40.1, 左高程曲线数据点137
#define DiEye_Left_Elevation_Line_138_S                                   0x00e1cd4228088530    // 0x217, 40.2-41.2, 左高程曲线数据点138
#define DiEye_Left_Elevation_Line_139_S                                   0x00e24d5228088538    // 0x217, 41.3-42.3, 左高程曲线数据点139
#define DiEye_Left_Elevation_Line_140_S                                   0x00e2cd6228088540    // 0x217, 42.4-43.4, 左高程曲线数据点140
#define DiEye_Left_Elevation_Line_141_S                                   0x00e34d7228088548    // 0x217, 43.5-44.5, 左高程曲线数据点141
#define DiEye_Left_Elevation_Line_142_S                                   0x00e3cd8228088550    // 0x217, 44.6-45.6, 左高程曲线数据点142
#define DiEye_Left_Elevation_Line_143_S                                   0x00e44d9228088558    // 0x217, 45.7-46.7, 左高程曲线数据点143
#define DiEye_Left_Elevation_Line_144_S                                   0x00e4cda228088560    // 0x217, 47.0-48.0, 左高程曲线数据点144
#define DiEye_Left_Elevation_Line_145_S                                   0x00e54db228088568    // 0x217, 48.1-49.1, 左高程曲线数据点145
#define DiEye_Left_Elevation_Line_146_S                                   0x00e5cdc228088570    // 0x217, 49.2-50.2, 左高程曲线数据点146
#define DiEye_Left_Elevation_Line_147_S                                   0x00e64dd228088578    // 0x217, 50.3-51.3, 左高程曲线数据点147
#define DiEye_Left_Elevation_Line_148_S                                   0x00e6cde228088580    // 0x217, 51.4-52.4, 左高程曲线数据点148
#define DiEye_Left_Elevation_Line_149_S                                   0x00e74df228088588    // 0x217, 52.5-53.5, 左高程曲线数据点149
#define Alive_counter217_03_S                                             0x00e7800228088590    // 0x217, 61.0-62.7, 生命帧
#define EPB_Switch_Status                                                 0x00e8000230000cf0    // 0x218, 1.0-1.1, EPB开关状态
#define EPB_Switch_Status_Validity                                        0x00e8800230000cf8    // 0x218, 1.2, EPB开关状态有效位
#define EPB_Status                                                        0x00e9000230000d00    // 0x218, 2.1-2.3, EPB状态
#define EPB_Automatic_Braking_Requested                                   0x00e9800230000d08    // 0x218, 3.6, EPB动态制动请求有效位
#define Status_of_left_park_brake                                         0x00ea000230000d10    // 0x218, 6.2-6.4, 左卡钳状态
#define Status_of_right_park_brake                                        0x00ea800230000d18    // 0x218, 6.5-6.7, 右卡钳状态
#define EPB_Available_State                                               0x00eb000230000d20    // 0x218, 7.4-7.5, 电子手刹可用状态
#define DiEye_Right_Elevation_Line_0_S                                    0x00ebce02480a8598    // 0x21B, 2.0-3.0, 右高程曲线数据点0
#define DiEye_Right_Elevation_Line_1_S                                    0x00ec4e12480a85a0    // 0x21B, 3.1-4.1, 右高程曲线数据点1
#define DiEye_Right_Elevation_Line_2_S                                    0x00ecce22480a85a8    // 0x21B, 4.2-5.2, 右高程曲线数据点2
#define DiEye_Right_Elevation_Line_3_S                                    0x00ed4e32480a85b0    // 0x21B, 5.3-6.3, 右高程曲线数据点3
#define DiEye_Right_Elevation_Line_4_S                                    0x00edce42480a85b8    // 0x21B, 6.4-7.4, 右高程曲线数据点4
#define DiEye_Right_Elevation_Line_5_S                                    0x00ee4e52480a85c0    // 0x21B, 7.5-8.5, 右高程曲线数据点5
#define DiEye_Right_Elevation_Line_6_S                                    0x00eece62480a85c8    // 0x21B, 8.6-9.6, 右高程曲线数据点6
#define DiEye_Right_Elevation_Line_7_S                                    0x00ef4e72480a85d0    // 0x21B, 9.7-10.7, 右高程曲线数据点7
#define DiEye_Right_Elevation_Line_8_S                                    0x00efce82480a85d8    // 0x21B, 11.0-12.0, 右高程曲线数据点8
#define DiEye_Right_Elevation_Line_9_S                                    0x00f04e92480a85e0    // 0x21B, 12.1-13.1, 右高程曲线数据点9
#define DiEye_Right_Elevation_Line_10_S                                   0x00f0cea2480a85e8    // 0x21B, 13.2-14.2, 右高程曲线数据点10
#define DiEye_Right_Elevation_Line_11_S                                   0x00f14eb2480a85f0    // 0x21B, 14.3-15.3, 右高程曲线数据点11
#define DiEye_Right_Elevation_Line_12_S                                   0x00f1cec2480a85f8    // 0x21B, 15.4-16.4, 右高程曲线数据点12
#define DiEye_Right_Elevation_Line_13_S                                   0x00f24ed2480a8600    // 0x21B, 16.5-17.5, 右高程曲线数据点13
#define DiEye_Right_Elevation_Line_14_S                                   0x00f2cee2480a8608    // 0x21B, 17.6-18.6, 右高程曲线数据点14
#define DiEye_Right_Elevation_Line_15_S                                   0x00f34ef2480a8610    // 0x21B, 18.7-19.7, 右高程曲线数据点15
#define DiEye_Right_Elevation_Line_16_S                                   0x00f3cf02480a8618    // 0x21B, 20.0-21.0, 右高程曲线数据点16
#define DiEye_Right_Elevation_Line_17_S                                   0x00f44f12480a8620    // 0x21B, 21.1-22.1, 右高程曲线数据点17
#define DiEye_Right_Elevation_Line_18_S                                   0x00f4cf22480a8628    // 0x21B, 22.2-23.2, 右高程曲线数据点18
#define DiEye_Right_Elevation_Line_19_S                                   0x00f54f32480a8630    // 0x21B, 23.3-24.3, 右高程曲线数据点19
#define DiEye_Right_Elevation_Line_20_S                                   0x00f5cf42480a8638    // 0x21B, 24.4-25.4, 右高程曲线数据点20
#define DiEye_Right_Elevation_Line_21_S                                   0x00f64f52480a8640    // 0x21B, 25.5-26.5, 右高程曲线数据点21
#define DiEye_Right_Elevation_Line_22_S                                   0x00f6cf62480a8648    // 0x21B, 26.6-27.6, 右高程曲线数据点22
#define DiEye_Right_Elevation_Line_23_S                                   0x00f74f72480a8650    // 0x21B, 27.7-28.7, 右高程曲线数据点23
#define DiEye_Right_Elevation_Line_24_S                                   0x00f7cf82480a8658    // 0x21B, 29.0-30.0, 右高程曲线数据点24
#define DiEye_Right_Elevation_Line_25_S                                   0x00f84f92480a8660    // 0x21B, 30.1-31.1, 右高程曲线数据点25
#define DiEye_Right_Elevation_Line_26_S                                   0x00f8cfa2480a8668    // 0x21B, 31.2-32.2, 右高程曲线数据点26
#define DiEye_Right_Elevation_Line_27_S                                   0x00f94fb2480a8670    // 0x21B, 32.3-33.3, 右高程曲线数据点27
#define DiEye_Right_Elevation_Line_28_S                                   0x00f9cfc2480a8678    // 0x21B, 33.4-34.4, 右高程曲线数据点28
#define DiEye_Right_Elevation_Line_29_S                                   0x00fa4fd2480a8680    // 0x21B, 34.5-35.5, 右高程曲线数据点29
#define DiEye_Right_Elevation_Line_30_S                                   0x00facfe2480a8688    // 0x21B, 35.6-36.6, 右高程曲线数据点30
#define DiEye_Right_Elevation_Line_31_S                                   0x00fb4ff2480a8690    // 0x21B, 36.7-37.7, 右高程曲线数据点31
#define DiEye_Right_Elevation_Line_32_S                                   0x00fbd002480a8698    // 0x21B, 38.0-39.0, 右高程曲线数据点32
#define DiEye_Right_Elevation_Line_33_S                                   0x00fc5012480a86a0    // 0x21B, 39.1-40.1, 右高程曲线数据点33
#define DiEye_Right_Elevation_Line_34_S                                   0x00fcd022480a86a8    // 0x21B, 40.2-41.2, 右高程曲线数据点34
#define DiEye_Right_Elevation_Line_35_S                                   0x00fd5032480a86b0    // 0x21B, 41.3-42.3, 右高程曲线数据点35
#define DiEye_Right_Elevation_Line_36_S                                   0x00fdd042480a86b8    // 0x21B, 42.4-43.4, 右高程曲线数据点36
#define DiEye_Right_Elevation_Line_37_S                                   0x00fe5052480a86c0    // 0x21B, 43.5-44.5, 右高程曲线数据点37
#define DiEye_Right_Elevation_Line_38_S                                   0x00fed062480a86c8    // 0x21B, 44.6-45.6, 右高程曲线数据点38
#define DiEye_Right_Elevation_Line_39_S                                   0x00ff5072480a86d0    // 0x21B, 45.7-46.7, 右高程曲线数据点39
#define DiEye_Right_Elevation_Line_40_S                                   0x00ffd082480a86d8    // 0x21B, 47.0-48.0, 右高程曲线数据点40
#define DiEye_Right_Elevation_Line_41_S                                   0x01005092480a86e0    // 0x21B, 48.1-49.1, 右高程曲线数据点41
#define DiEye_Right_Elevation_Line_42_S                                   0x0100d0a2480a86e8    // 0x21B, 49.2-50.2, 右高程曲线数据点42
#define DiEye_Right_Elevation_Line_43_S                                   0x010150b2480a86f0    // 0x21B, 50.3-51.3, 右高程曲线数据点43
#define DiEye_Right_Elevation_Line_44_S                                   0x0101d0c2480a86f8    // 0x21B, 51.4-52.4, 右高程曲线数据点44
#define DiEye_Right_Elevation_Line_45_S                                   0x010250d2480a8700    // 0x21B, 52.5-53.5, 右高程曲线数据点45
#define DiEye_Right_Elevation_Line_46_S                                   0x0102d0e2480a8708    // 0x21B, 53.6-54.6, 右高程曲线数据点46
#define DiEye_Right_Elevation_Line_47_S                                   0x010350f2480a8710    // 0x21B, 54.7-55.7, 右高程曲线数据点47
#define DiEye_Right_Elevation_Line_48_S                                   0x0103d102480a8718    // 0x21B, 56.0-57.0, 右高程曲线数据点48
#define DiEye_Right_Elevation_Line_49_S                                   0x01045112480a8720    // 0x21B, 57.1-58.1, 右高程曲线数据点49
#define DiEye_Right_Elevation_Line_50_S                                   0x0104d122480a8728    // 0x21B, 58.2-59.2, 右高程曲线数据点50
#define DiEye_Right_Elevation_Line_51_S                                   0x01055132480a8730    // 0x21B, 59.3-60.3, 右高程曲线数据点51
#define Alive_counter21B_01_S                                             0x01058002480a8738    // 0x21B, 61.0-62.7, 生命帧
#define DiEye_Right_Elevation_Line_52_S                                   0x01065142480c8740    // 0x21B, 2.0-3.0, 右高程曲线数据点52
#define DiEye_Right_Elevation_Line_53_S                                   0x0106d152480c8748    // 0x21B, 3.1-4.1, 右高程曲线数据点53
#define DiEye_Right_Elevation_Line_54_S                                   0x01075162480c8750    // 0x21B, 4.2-5.2, 右高程曲线数据点54
#define DiEye_Right_Elevation_Line_55_S                                   0x0107d172480c8758    // 0x21B, 5.3-6.3, 右高程曲线数据点55
#define DiEye_Right_Elevation_Line_56_S                                   0x01085182480c8760    // 0x21B, 6.4-7.4, 右高程曲线数据点56
#define DiEye_Right_Elevation_Line_57_S                                   0x0108d192480c8768    // 0x21B, 7.5-8.5, 右高程曲线数据点57
#define DiEye_Right_Elevation_Line_58_S                                   0x010951a2480c8770    // 0x21B, 8.6-9.6, 右高程曲线数据点58
#define DiEye_Right_Elevation_Line_59_S                                   0x0109d1b2480c8778    // 0x21B, 9.7-10.7, 右高程曲线数据点59
#define DiEye_Right_Elevation_Line_60_S                                   0x010a51c2480c8780    // 0x21B, 11.0-12.0, 右高程曲线数据点60
#define DiEye_Right_Elevation_Line_61_S                                   0x010ad1d2480c8788    // 0x21B, 12.1-13.1, 右高程曲线数据点61
#define DiEye_Right_Elevation_Line_62_S                                   0x010b51e2480c8790    // 0x21B, 13.2-14.2, 右高程曲线数据点62
#define DiEye_Right_Elevation_Line_63_S                                   0x010bd1f2480c8798    // 0x21B, 14.3-15.3, 右高程曲线数据点63
#define DiEye_Right_Elevation_Line_64_S                                   0x010c5202480c87a0    // 0x21B, 15.4-16.4, 右高程曲线数据点64
#define DiEye_Right_Elevation_Line_65_S                                   0x010cd212480c87a8    // 0x21B, 16.5-17.5, 右高程曲线数据点65
#define DiEye_Right_Elevation_Line_66_S                                   0x010d5222480c87b0    // 0x21B, 17.6-18.6, 右高程曲线数据点66
#define DiEye_Right_Elevation_Line_67_S                                   0x010dd232480c87b8    // 0x21B, 18.7-19.7, 右高程曲线数据点67
#define DiEye_Right_Elevation_Line_68_S                                   0x010e5242480c87c0    // 0x21B, 20.0-21.0, 右高程曲线数据点68
#define DiEye_Right_Elevation_Line_69_S                                   0x010ed252480c87c8    // 0x21B, 21.1-22.1, 右高程曲线数据点69
#define DiEye_Right_Elevation_Line_70_S                                   0x010f5262480c87d0    // 0x21B, 22.2-23.2, 右高程曲线数据点70
#define DiEye_Right_Elevation_Line_71_S                                   0x010fd272480c87d8    // 0x21B, 23.3-24.3, 右高程曲线数据点71
#define DiEye_Right_Elevation_Line_72_S                                   0x01105282480c87e0    // 0x21B, 24.4-25.4, 右高程曲线数据点72
#define DiEye_Right_Elevation_Line_73_S                                   0x0110d292480c87e8    // 0x21B, 25.5-26.5, 右高程曲线数据点73
#define DiEye_Right_Elevation_Line_74_S                                   0x011152a2480c87f0    // 0x21B, 26.6-27.6, 右高程曲线数据点74
#define DiEye_Right_Elevation_Line_75_S                                   0x0111d2b2480c87f8    // 0x21B, 27.7-28.7, 右高程曲线数据点75
#define DiEye_Right_Elevation_Line_76_S                                   0x011252c2480c8800    // 0x21B, 29.0-30.0, 右高程曲线数据点76
#define DiEye_Right_Elevation_Line_77_S                                   0x0112d2d2480c8808    // 0x21B, 30.1-31.1, 右高程曲线数据点77
#define DiEye_Right_Elevation_Line_78_S                                   0x011352e2480c8810    // 0x21B, 31.2-32.2, 右高程曲线数据点78
#define DiEye_Right_Elevation_Line_79_S                                   0x0113d2f2480c8818    // 0x21B, 32.3-33.3, 右高程曲线数据点79
#define DiEye_Right_Elevation_Line_80_S                                   0x01145302480c8820    // 0x21B, 33.4-34.4, 右高程曲线数据点80
#define DiEye_Right_Elevation_Line_81_S                                   0x0114d312480c8828    // 0x21B, 34.5-35.5, 右高程曲线数据点81
#define DiEye_Right_Elevation_Line_82_S                                   0x01155322480c8830    // 0x21B, 35.6-36.6, 右高程曲线数据点82
#define DiEye_Right_Elevation_Line_83_S                                   0x0115d332480c8838    // 0x21B, 36.7-37.7, 右高程曲线数据点83
#define DiEye_Right_Elevation_Line_84_S                                   0x01165342480c8840    // 0x21B, 38.0-39.0, 右高程曲线数据点84
#define DiEye_Right_Elevation_Line_85_S                                   0x0116d352480c8848    // 0x21B, 39.1-40.1, 右高程曲线数据点85
#define DiEye_Right_Elevation_Line_86_S                                   0x01175362480c8850    // 0x21B, 40.2-41.2, 右高程曲线数据点86
#define DiEye_Right_Elevation_Line_87_S                                   0x0117d372480c8858    // 0x21B, 41.3-42.3, 右高程曲线数据点87
#define DiEye_Right_Elevation_Line_88_S                                   0x01185382480c8860    // 0x21B, 42.4-43.4, 右高程曲线数据点88
#define DiEye_Right_Elevation_Line_89_S                                   0x0118d392480c8868    // 0x21B, 43.5-44.5, 右高程曲线数据点89
#define DiEye_Right_Elevation_Line_90_S                                   0x011953a2480c8870    // 0x21B, 44.6-45.6, 右高程曲线数据点90
#define DiEye_Right_Elevation_Line_91_S                                   0x0119d3b2480c8878    // 0x21B, 45.7-46.7, 右高程曲线数据点91
#define DiEye_Right_Elevation_Line_92_S                                   0x011a53c2480c8880    // 0x21B, 47.0-48.0, 右高程曲线数据点92
#define DiEye_Right_Elevation_Line_93_S                                   0x011ad3d2480c8888    // 0x21B, 48.1-49.1, 右高程曲线数据点93
#define DiEye_Right_Elevation_Line_94_S                                   0x011b53e2480c8890    // 0x21B, 49.2-50.2, 右高程曲线数据点94
#define DiEye_Right_Elevation_Line_95_S                                   0x011bd3f2480c8898    // 0x21B, 50.3-51.3, 右高程曲线数据点95
#define DiEye_Right_Elevation_Line_96_S                                   0x011c5402480c88a0    // 0x21B, 51.4-52.4, 右高程曲线数据点96
#define DiEye_Right_Elevation_Line_97_S                                   0x011cd412480c88a8    // 0x21B, 52.5-53.5, 右高程曲线数据点97
#define DiEye_Right_Elevation_Line_98_S                                   0x011d5422480c88b0    // 0x21B, 53.6-54.6, 右高程曲线数据点98
#define DiEye_Right_Elevation_Line_99_S                                   0x011dd432480c88b8    // 0x21B, 54.7-55.7, 右高程曲线数据点99
#define DiEye_Right_Elevation_Line_100_S                                  0x011e5442480c88c0    // 0x21B, 56.0-57.0, 右高程曲线数据点100
#define DiEye_Right_Elevation_Line_101_S                                  0x011ed452480c88c8    // 0x21B, 57.1-58.1, 右高程曲线数据点101
#define DiEye_Right_Elevation_Line_102_S                                  0x011f5462480c88d0    // 0x21B, 58.2-59.2, 右高程曲线数据点102
#define DiEye_Right_Elevation_Line_103_S                                  0x011fd472480c88d8    // 0x21B, 59.3-60.3, 右高程曲线数据点103
#define Alive_counter21B_02_S                                             0x01200002480c88e0    // 0x21B, 61.0-62.7, 生命帧
#define DiEye_Right_Elevation_Line_104_S                                  0x0120d482480e88e8    // 0x21B, 2.0-3.0, 右高程曲线数据点104
#define DiEye_Right_Elevation_Line_105_S                                  0x01215492480e88f0    // 0x21B, 3.1-4.1, 右高程曲线数据点105
#define DiEye_Right_Elevation_Line_106_S                                  0x0121d4a2480e88f8    // 0x21B, 4.2-5.2, 右高程曲线数据点106
#define DiEye_Right_Elevation_Line_107_S                                  0x012254b2480e8900    // 0x21B, 5.3-6.3, 右高程曲线数据点107
#define DiEye_Right_Elevation_Line_108_S                                  0x0122d4c2480e8908    // 0x21B, 6.4-7.4, 右高程曲线数据点108
#define DiEye_Right_Elevation_Line_109_S                                  0x012354d2480e8910    // 0x21B, 7.5-8.5, 右高程曲线数据点109
#define DiEye_Right_Elevation_Line_110_S                                  0x0123d4e2480e8918    // 0x21B, 8.6-9.6, 右高程曲线数据点110
#define DiEye_Right_Elevation_Line_111_S                                  0x012454f2480e8920    // 0x21B, 9.7-10.7, 右高程曲线数据点111
#define DiEye_Right_Elevation_Line_112_S                                  0x0124d502480e8928    // 0x21B, 11.0-12.0, 右高程曲线数据点112
#define DiEye_Right_Elevation_Line_113_S                                  0x01255512480e8930    // 0x21B, 12.1-13.1, 右高程曲线数据点113
#define DiEye_Right_Elevation_Line_114_S                                  0x0125d522480e8938    // 0x21B, 13.2-14.2, 右高程曲线数据点114
#define DiEye_Right_Elevation_Line_115_S                                  0x01265532480e8940    // 0x21B, 14.3-15.3, 右高程曲线数据点115
#define DiEye_Right_Elevation_Line_116_S                                  0x0126d542480e8948    // 0x21B, 15.4-16.4, 右高程曲线数据点116
#define DiEye_Right_Elevation_Line_117_S                                  0x01275552480e8950    // 0x21B, 16.5-17.5, 右高程曲线数据点117
#define DiEye_Right_Elevation_Line_118_S                                  0x0127d562480e8958    // 0x21B, 17.6-18.6, 右高程曲线数据点118
#define DiEye_Right_Elevation_Line_119_S                                  0x01285572480e8960    // 0x21B, 18.7-19.7, 右高程曲线数据点119
#define DiEye_Right_Elevation_Line_120_S                                  0x0128d582480e8968    // 0x21B, 20.0-21.0, 右高程曲线数据点120
#define DiEye_Right_Elevation_Line_121_S                                  0x01295592480e8970    // 0x21B, 21.1-22.1, 右高程曲线数据点121
#define DiEye_Right_Elevation_Line_122_S                                  0x0129d5a2480e8978    // 0x21B, 22.2-23.2, 右高程曲线数据点122
#define DiEye_Right_Elevation_Line_123_S                                  0x012a55b2480e8980    // 0x21B, 23.3-24.3, 右高程曲线数据点123
#define DiEye_Right_Elevation_Line_124_S                                  0x012ad5c2480e8988    // 0x21B, 24.4-25.4, 右高程曲线数据点124
#define DiEye_Right_Elevation_Line_125_S                                  0x012b55d2480e8990    // 0x21B, 25.5-26.5, 右高程曲线数据点125
#define DiEye_Right_Elevation_Line_126_S                                  0x012bd5e2480e8998    // 0x21B, 26.6-27.6, 右高程曲线数据点126
#define DiEye_Right_Elevation_Line_127_S                                  0x012c55f2480e89a0    // 0x21B, 27.7-28.7, 右高程曲线数据点127
#define DiEye_Right_Elevation_Line_128_S                                  0x012cd602480e89a8    // 0x21B, 29.0-30.0, 右高程曲线数据点128
#define DiEye_Right_Elevation_Line_129_S                                  0x012d5612480e89b0    // 0x21B, 30.1-31.1, 右高程曲线数据点129
#define DiEye_Right_Elevation_Line_130_S                                  0x012dd622480e89b8    // 0x21B, 31.2-32.2, 右高程曲线数据点130
#define DiEye_Right_Elevation_Line_131_S                                  0x012e5632480e89c0    // 0x21B, 32.3-33.3, 右高程曲线数据点131
#define DiEye_Right_Elevation_Line_132_S                                  0x012ed642480e89c8    // 0x21B, 33.4-34.4, 右高程曲线数据点132
#define DiEye_Right_Elevation_Line_133_S                                  0x012f5652480e89d0    // 0x21B, 34.5-35.5, 右高程曲线数据点133
#define DiEye_Right_Elevation_Line_134_S                                  0x012fd662480e89d8    // 0x21B, 35.6-36.6, 右高程曲线数据点134
#define DiEye_Right_Elevation_Line_135_S                                  0x01305672480e89e0    // 0x21B, 36.7-37.7, 右高程曲线数据点135
#define DiEye_Right_Elevation_Line_136_S                                  0x0130d682480e89e8    // 0x21B, 38.0-39.0, 右高程曲线数据点136
#define DiEye_Right_Elevation_Line_137_S                                  0x01315692480e89f0    // 0x21B, 39.1-40.1, 右高程曲线数据点137
#define DiEye_Right_Elevation_Line_138_S                                  0x0131d6a2480e89f8    // 0x21B, 40.2-41.2, 右高程曲线数据点138
#define DiEye_Right_Elevation_Line_139_S                                  0x013256b2480e8a00    // 0x21B, 41.3-42.3, 右高程曲线数据点139
#define DiEye_Right_Elevation_Line_140_S                                  0x0132d6c2480e8a08    // 0x21B, 42.4-43.4, 右高程曲线数据点140
#define DiEye_Right_Elevation_Line_141_S                                  0x013356d2480e8a10    // 0x21B, 43.5-44.5, 右高程曲线数据点141
#define DiEye_Right_Elevation_Line_142_S                                  0x0133d6e2480e8a18    // 0x21B, 44.6-45.6, 右高程曲线数据点142
#define DiEye_Right_Elevation_Line_143_S                                  0x013456f2480e8a20    // 0x21B, 45.7-46.7, 右高程曲线数据点143
#define DiEye_Right_Elevation_Line_144_S                                  0x0134d702480e8a28    // 0x21B, 47.0-48.0, 右高程曲线数据点144
#define DiEye_Right_Elevation_Line_145_S                                  0x01355712480e8a30    // 0x21B, 48.1-49.1, 右高程曲线数据点145
#define DiEye_Right_Elevation_Line_146_S                                  0x0135d722480e8a38    // 0x21B, 49.2-50.2, 右高程曲线数据点146
#define DiEye_Right_Elevation_Line_147_S                                  0x01365732480e8a40    // 0x21B, 50.3-51.3, 右高程曲线数据点147
#define DiEye_Right_Elevation_Line_148_S                                  0x0136d742480e8a48    // 0x21B, 51.4-52.4, 右高程曲线数据点148
#define DiEye_Right_Elevation_Line_149_S                                  0x01375752480e8a50    // 0x21B, 52.5-53.5, 右高程曲线数据点149
#define Alive_counter21B_03_S                                             0x01378002480e8a58    // 0x21B, 61.0-62.7, 生命帧
#define VDC_Active_222_S                                                  0x0138000250000d28    // 0x222, 4.6, VDC功能触发信号
#define ESC_APA_Status                                                    0x0138800250000d30    // 0x222, 5.0-5.2, APA（AutoParkingAssist）状态（IPB确认
#define Rolling_Counter_0x222                                             0x0139000250000d38    // 0x222, 7.0-7.3, 滚动计数校验
#define Ehection_Start_fcn_Active_flg_S                                   0x0139800260000d40    // 0x240, 2.6, 弹射起步功能激活标志
#define Vehicle_Torque_FL_241_S                                           0x013a576270000d48    // 0x241, 1.0-2.7, 前轴总扭矩
#define Driver_Front_Torque_Request                                       0x013ad77270008a60    // 0x241, 3.0-4.7, 前轴驾驶员需求指示扭矩
#define Veh_now_max_fb_tq                                                 0x013b578270008a68    // 0x241, 5.0-6.5, 整车当前最大回馈扭矩
#define Torque_State_FL_241_S                                             0x013b800270000d50    // 0x241, 6.6, 前轴扭矩状态
#define Driver_Front_Torque_Request_Status                                0x013c000270000d58    // 0x241, 6.7, 前轴驾驶员需求指示扭矩状态
#define Veh_now_act_fb_tq                                                 0x013cd79270008a70    // 0x241, 7.0-8.5, 整车当前实际回馈扭矩
#define Veh_now_act_fb_tq_sat                                             0x013d000270000d60    // 0x241, 8.6, 整车当前实际回馈扭矩状态
#define Veh_now_max_fb_tq_sat                                             0x013d800270000d68    // 0x241, 8.7, 整车当前最大回馈扭矩状态
#define Front_Motor_Speed                                                 0x013e57a280008a78    // 0x242, 1.0-2.7, 前电机转速
#define Veh_adh_sat                                                       0x013e800280000d70    // 0x242, 3.0-3.2, 车辆附着状态
#define Virt_acce_pedl_perc                                               0x013f000280000d78    // 0x242, 4.0-4.7, 虚拟油门深度
#define ERROR_Motor_Speed                                                 0x013f800280000d80    // 0x242, 5.0, 前电机转速状态位
#define Ovrd_flg                                                          0x0140000280000d88    // 0x242, 5.1, 超越驾驶标志
#define Virt_acce_pedl_perc_efc_flg                                       0x0140800280000d90    // 0x242, 5.4, 虚拟油门深度有效标志
#define Brake_Pedal_Status_0x242                                          0x0141000280000d98    // 0x242, 5.5-5.6, 制动踏板状态信号
#define Gear_Position                                                     0x0141800280000da0    // 0x242, 6.0-6.3, 挡位信号
#define Gear_Status                                                       0x0142000280000da8    // 0x242, 6.4, 档位信号状态
#define Msg_Cnt_0x242                                                     0x0142800280000db0    // 0x242, 7.4-7.7, 0x242生命帧
#define Checksum242_S                                                     0x0143000280000db8    // 0x242, 8.0-8.7, 0x242校验码
#define EPS_err_alrm                                                      0x0143800290000dc0    // 0x24C, 1.0-1.1, EPS故障报警
#define Rear_Motor_Speed                                                  0x014457b2a0008a80    // 0x251, 1.0-2.7, 后电机转速
#define Vehicle_Torque_RL_251_S                                           0x0144d7c2a0008a88    // 0x251, 3.0-4.7, 后轴扭矩
#define Driver_Rear_Torque_Request                                        0x014557d2a0008a90    // 0x251, 5.0-6.7, 后轴驾驶员需求指示扭矩
#define Error_Rear_Motor_Speed                                            0x01458002a0000dc8    // 0x251, 7.0, 后电机转速状态位
#define Torque_State_RL_251_S                                             0x01460002a0000dd0    // 0x251, 7.1, 后轴总扭矩有效性信号
#define Driver_Rear_Torque_Request_Status                                 0x01468002a0000dd8    // 0x251, 7.2, 后轴驾驶员需求指示扭矩状态
#define Rolling_Counter_251_S                                             0x01470002a0000de0    // 0x251, 7.4-7.7, 循环丢包计数器
#define CRC_Checknum_258_S                                                0x0840800080000de8    // 0x258, 1.0-2.7, CRC_Checknum_258_S
#define Alive_Counter_258_S                                               0x0841000080000df0    // 0x258, 3.0-4.7, Alive_Counter_258_S
#define DiSus_Height_Adjust_Status_S                                      0x0841800080000df8    // 0x258, 5.0, 悬架高度可调节状态
#define DiSus_SH_Adjust_Status_S                                          0x0842000080000e00    // 0x258, 5.1, 悬架软硬可调节状态
#define DiSus_Height_Adjust_Fault_S                                       0x0842800080000e08    // 0x258, 5.2, 悬架高度调节故障
#define DiSus_SH_Adjust_Fault_S                                           0x0843000080000e10    // 0x258, 5.3, 悬架软硬调节故障
#define DiSus_Height_Adjust_Process_S                                     0x0843800080000e18    // 0x258, 5.4-5.6, 悬架高度调节过程
#define DiSus_SpeedFollow_Adjust_Process_S                                0x0844000080000e20    // 0x258, 5.7-6.0, 随速调节过程
#define DiSus_SH_Adjust_Switch_Invalid                                    0x0844800080000e28    // 0x258, 6.1, 软硬调节开关灰显
#define DiSus_SH_Adjust_Fault_To_Meter                                    0x0845000080000e30    // 0x258, 6.2, 悬架软硬调节故障仪表灯状态
#define DiSus_SH_Work_Mode_Switch                                         0x0845800080000e38    // 0x258, 6.3, 软硬调节开关模式
#define Actual_DiSus_SH_Adjust_Gear_S                                     0x0846000080000e40    // 0x258, 6.4-6.7, 实际悬架软硬调节挡位
#define Actual_DiSus_Height_Mode_S                                        0x0846800080000e48    // 0x258, 7.0-7.2, 实际悬架高度模式
#define DiSus_Cloud_UpDown_State_S                                        0x0847000080000e50    // 0x258, 7.3, 悬架高度云调节状态
#define Goal_DiSus_Height_Mode_S                                          0x0847800080000e58    // 0x258, 7.4-7.6, 目标悬架高度模式
#define Extra_Low_Self_Learning_S                                         0x0848000080000e60    // 0x258, 7.7, 超低自学习
#define DiSus_Customize_Height_Adjust_Gear_S                              0x0848800080000e68    // 0x258, 8.0-8.3, 用户自定义悬架高度调节挡位
#define Actual_DiSus_Height_Adjust_Gear_S                                 0x0849000080000e70    // 0x258, 8.4-8.7, 实际悬架高度调节挡位
#define DiEye_Assemble_Status_S                                           0x0849800080000e78    // 0x258, 9.0-9.1, 预瞄功能配置
#define DiEye_SettingStates_S                                             0x084a000080000e80    // 0x258, 9.2, 预瞄功能开启状态信号
#define DiSus_Very_Low_Info_S                                             0x084a800080000e88    // 0x258, 9.3, 极低提示
#define DiSus_High_Voltage_Request_S                                      0x084b000080000e90    // 0x258, 9.4-9.5, DiSus高压电请求
#define DiSus_High_Voltage_Request_Valid_S                                0x084b800080000e98    // 0x258, 9.6, DiSus高压电请求有效位
#define DiSus_Height_Mode_OFF__Invalid_S                                  0x084c000080000ea0    // 0x258, 9.7, 悬架高度模式OFF开关禁用
#define DiSus_Height_Mode_OFF_S                                           0x084c800080000ea8    // 0x258, 10.0-10.1, 悬架高度模式OFF开关
#define DiSus_Welcome_Sig_S                                               0x084d000080000eb0    // 0x258, 10.2-10.3, 迎宾功能信号
#define DiSus_Welcome_Info_Sig_S                                          0x084d800080000eb8    // 0x258, 10.4-10.5, 迎宾功能提示
#define DiSus_Front_Suitcase_S                                            0x084e000080000ec0    // 0x258, 10.6-10.7, 前行李箱取物开关
#define DiSus_Rear_Suitcase_S                                             0x084e800080000ec8    // 0x258, 11.0-11.1, 后行李箱取物开关
#define DiSus_Balance_Switch_Sig_S                                        0x084f000080000ed0    // 0x258, 11.2-11.3, 露营调平功能开关
#define DiSus_Balance_Switch_Info_Sig_S                                   0x084f800080000ed8    // 0x258, 11.4-11.5, 露营调平调节过程
#define DiSus_Maintance_Switch_Sig_S                                      0x0850000080000ee0    // 0x258, 11.6-11.7, 千斤顶/举升机功能
#define DiSus_Maintance_Switch_OFF_Sig_S                                  0x0850800080000ee8    // 0x258, 12.0-12.1, 千斤顶/举升机功能高度抑制
#define DiSus_Height_OFF_Speed_Sig_S                                      0x0851000080000ef0    // 0x258, 12.2-12.3, 高度挡位抑制模式-随速
#define DiSus_Height_OFF_Sig_S                                            0x0851800080000ef8    // 0x258, 12.4-12.5, 高度挡位抑制模式-固定
#define Welcome_Function_Invalid_S                                        0x0852000080000f00    // 0x258, 12.6, 迎宾功能禁用
#define Trunk_Access_Invalid_S                                            0x0852800080000f08    // 0x258, 12.7, 取物功能禁用
#define DiSus_Towing_Drive_Status_S                                       0x0853000080000f10    // 0x258, 13.0-13.2, 悬架拖拽行驶状态
#define DiSus_Raise_Light_Sig_S                                           0x0853800080000f18    // 0x258, 13.3, 加注功能警示灯
#define DiSus_Raise_Info_Sig_S                                            0x0854000080000f20    // 0x258, 13.4, 加注功能提示信息
#define DiSus_Slow_Down_80kph_S                                           0x0854800080000f28    // 0x258, 13.5, 提示降速至80以下
#define DiSus_IMU_Not_Calibration_S                                       0x0855000080000f30    // 0x258, 13.6, IMU未标定
#define DiSus_Height_Not_Calibration_S                                    0x0855800080000f38    // 0x258, 13.7, 高度未标定
#define DiSus_Combine_Height_Adjust_Gear_FL_S                             0x0856000080000f40    // 0x258, 14.0-14.3, 悬架组合高度调节挡位-左前轮
#define DiSus_Combine_Height_Adjust_Gear_FR_S                             0x0856800080000f48    // 0x258, 14.4-14.7, 悬架组合高度调节挡位-右前轮
#define DiSus_Combine_Height_Adjust_Gear_RL_S                             0x0857000080000f50    // 0x258, 15.0-15.3, 悬架组合高度调节挡位-左后轮
#define DiSus_Combine_Height_Adjust_Gear_RR_S                             0x0857800080000f58    // 0x258, 15.4-15.7, 悬架组合高度调节挡位-右后轮
#define DiSus_Combine_Height_Adjust_Gear_Sing_L_S                         0x0858000080000f60    // 0x258, 16.0-16.3, 悬架组合高度调节挡位-左单边
#define DiSus_Combine_Height_Adjust_Gear_Sing_R_S                         0x0858800080000f68    // 0x258, 16.4-16.7, 悬架组合高度调节挡位-右单边
#define DiSus_Combine_Height_Adjust_Gear_F_S                              0x0859000080000f70    // 0x258, 17.0-17.3, 悬架组合高度调节挡位-前轮
#define DiSus_Combine_Height_Adjust_Gear_R_S                              0x0859800080000f78    // 0x258, 17.4-17.7, 悬架组合高度调节挡位-后轮
#define DiSus_Balance_Switch_Warning_Sig_S                                0x085a000080000f80    // 0x258, 18.0-18.1, 露营调平报警提示
#define CVC_Work_Mode_S                                                   0x085a800080000f88    // 0x258, 18.2-18.3, 底盘矢量控制开关
#define Balance_Disable_S                                                 0x085b000080000f90    // 0x258, 18.4, 露营调平禁用_258
#define DiDyna_CVC_Work_work_mode                                         0x085b800080000f98    // 0x258, 18.5, 底盘矢量控制开关模式
#define DiSus_SingleWheel_Height_Adjust_Disable_S                         0x085c000080000fa0    // 0x258, 18.6, 单轮调节禁用
#define DiSus_Combine_Height_Adjust_S                                     0x085c800080000fa8    // 0x258, 18.7, 单边调节禁用
#define DiSus_Customize_Combine_Height_Enable_Status_S                    0x085d000080000fb0    // 0x258, 19.0-19.1, DiSus自定义组合高度调节使能信号
#define DiSus_Customize_Single_Height_Enable_Status_S                     0x085d800080000fb8    // 0x258, 19.2-19.3, DiSus自定义单轮高度调节使能信号
#define DiDyna_Switch_CCT                                                 0x085e000080000fc0    // 0x258, 19.4, 舒适控制技术自学习
#define CCT2_0_Function_Switch_IPB                                        0x085e800080000fc8    // 0x258, 19.5-19.6, 舒适控制技术
#define DiSus_Maintance_Invalid_S                                         0x085f000080000fd0    // 0x258, 19.7, 千斤顶/举升机禁用
#define DiSus_Height_Adjust_prompt_msg                                    0x085f800080000fd8    // 0x258, 20.0-20.3, 悬架调节提示语
#define CCT2_0_Function_Switch_PAD                                        0x0860000080000fe0    // 0x258, 20.4-20.5, 舒适控制技术
#define Wheel_Arch_Height                                                 0x0860d7e080000fe8    // 0x258, 20.6-21.5, 轮眉高度
#define Wheel_Center_Height                                               0x086157f080000ff0    // 0x258, 21.6-22.5, 轮心高度
#define Load_Balance_Func_Status                                          0x0861800080000ff8    // 0x258, 22.6-22.7, 载荷平衡功能状态
#define DiSus_C_DTC_Reserve1                                              0x0862000080001000    // 0x258, 23.0-29.7, DiSus_C_DTC预留信号1
#define DiSus_prompt_msg_Send2PAD                                         0x0862800080001008    // 0x258, 34.0, 悬架提示发送PAD显示
#define DiSus_prompt_msg_Send2Meter                                       0x0863000080001010    // 0x258, 34.1, 悬架提示发送仪表显示
#define DiSus_prompt_msg_Send2Voice                                       0x0863800080001018    // 0x258, 34.2, 悬架提示发送语音播放
#define DiSus_prompt_msg_Send2APP                                         0x0864000080001020    // 0x258, 34.3, 悬架提示发送APP显示
#define DiSus_HAdjust_Status_S                                            0x0864800080001028    // 0x258, 34.4-34.5, 悬架可调节状态
#define ExtraHigh_Switch_Feedback_S                                       0x0865000080001030    // 0x258, 34.6-34.7, 超高模式开关反馈信号/状态信号
#define ExtraLow_Switch_Feedback_S                                        0x0865800080001038    // 0x258, 35.0-35.1, 超低模式开关反馈信号/状态信号
#define DiSus_Extra_Hi_info_S                                             0x0866000080001040    // 0x258, 35.2-35.3, 超高提示
#define DiSus_Extra_Low_info_S                                            0x0866800080001048    // 0x258, 35.4-35.5, 超低提示
#define Extra_High_Self_Learning_S                                        0x0867000080001050    // 0x258, 35.6, 超高自学习
#define DiSus_Extra_Hi_Instrct                                            0x0867800080001058    // 0x258, 35.7, 超高操作指引
#define DiSus_Drift_Status_Feedback_S                                     0x0868000080001060    // 0x258, 36.0-36.2, 悬架状态反馈
#define DiSus_Switch_SH_Adjust                                            0x0868800080001068    // 0x258, 36.3-36.4, 软硬调节类型配置信号
#define DiSus_Extra_Low_Invalid_S                                         0x0869000080001070    // 0x258, 36.5, 超低功能禁用
#define DiSus_Extra_Hi_Invalid_S                                          0x0869800080001078    // 0x258, 36.6, 超高功能禁用
#define C2DA217_S                                                         0x086a000080001080    // 0x258, 40.0, C2DA217
#define C2DA316_S                                                         0x086a800080001088    // 0x258, 40.1, C2DA316
#define U000104_S                                                         0x086b000080001090    // 0x258, 40.2, U000104
#define C2DA400_S                                                         0x086b800080001098    // 0x258, 40.3, C2DA400
#define U019780_S                                                         0x086c0000800010a0    // 0x258, 40.4, U019780
#define U20A387_S                                                         0x086c8000800010a8    // 0x258, 40.5, U20A387
#define U024587_S                                                         0x086d0000800010b0    // 0x258, 40.6, U024587
#define U012200_S                                                         0x086d8000800010b8    // 0x258, 40.7, U012200
#define U018F87_S                                                         0x086e0000800010c0    // 0x258, 41.0, U018F87
#define U110983_S                                                         0x086e8000800010c8    // 0x258, 41.1, U110983
#define C2DB081_S                                                         0x086f0000800010d0    // 0x258, 41.2, C2DB081
#define C2DB100_S                                                         0x086f8000800010d8    // 0x258, 41.3, C2DB100
#define C1E2049_S                                                         0x08700000800010e0    // 0x258, 41.4, C1E2049
#define C2DB200_S                                                         0x08708000800010e8    // 0x258, 41.5, C2DB200
#define C1E2146_S                                                         0x08710000800010f0    // 0x258, 41.6, C1E2146
#define C1E2112_S                                                         0x08718000800010f8    // 0x258, 41.7, C1E2112
#define C1E2111_S                                                         0x0872000080001100    // 0x258, 42.0, C1E2111
#define C1E2129_S                                                         0x0872800080001108    // 0x258, 42.1, C1E2129
#define C1E2246_S                                                         0x0873000080001110    // 0x258, 42.2, C1E2246
#define C1E2212_S                                                         0x0873800080001118    // 0x258, 42.3, C1E2212
#define C1E2211_S                                                         0x0874000080001120    // 0x258, 42.4, C1E2211
#define C1E2229_S                                                         0x0874800080001128    // 0x258, 42.5, C1E2229
#define C1E2346_S                                                         0x0875000080001130    // 0x258, 42.6, C1E2346
#define C2DA912_S                                                         0x0875800080001138    // 0x258, 42.7, C2DA912
#define C2DA911_S                                                         0x0876000080001140    // 0x258, 43.0, C2DA911
#define C2DA900_S                                                         0x0876800080001148    // 0x258, 43.1, C2DA900
#define C1E2446_S                                                         0x0877000080001150    // 0x258, 43.2, C1E2446
#define C2DAA12_S                                                         0x0877800080001158    // 0x258, 43.3, C2DAA12
#define C2DAA11_S                                                         0x0878000080001160    // 0x258, 43.4, C2DAA11
#define C2DAA00_S                                                         0x0878800080001168    // 0x258, 43.5, C2DAA00
#define C1E2512_S                                                         0x0879000080001170    // 0x258, 43.6, C1E2512
#define C1E2511_S                                                         0x0879800080001178    // 0x258, 43.7, C1E2511
#define C1E2529_S                                                         0x087a000080001180    // 0x258, 44.0, C1E2529
#define C1E2612_S                                                         0x087a800080001188    // 0x258, 44.1, C1E2612
#define C1E2611_S                                                         0x087b000080001190    // 0x258, 44.2, C1E2611
#define C1E2629_S                                                         0x087b800080001198    // 0x258, 44.3, C1E2629
#define C2DC709_S                                                         0x087c0000800011a0    // 0x258, 44.4, C2DC709
#define C1E2712_S                                                         0x087c8000800011a8    // 0x258, 44.5, C1E2712
#define C1E2711_S                                                         0x087d0000800011b0    // 0x258, 44.6, C1E2711
#define C1E2713_S                                                         0x087d8000800011b8    // 0x258, 44.7, C1E2713
#define C1E2812_S                                                         0x087e0000800011c0    // 0x258, 45.0, C1E2812
#define C1E2811_S                                                         0x087e8000800011c8    // 0x258, 45.1, C1E2811
#define C1E2813_S                                                         0x087f0000800011d0    // 0x258, 45.2, C1E2813
#define C1E2912_S                                                         0x087f8000800011d8    // 0x258, 45.3, C1E2912
#define C1E2911_S                                                         0x08800000800011e0    // 0x258, 45.4, C1E2911
#define C1E2913_S                                                         0x08808000800011e8    // 0x258, 45.5, C1E2913
#define C1E2A12_S                                                         0x08810000800011f0    // 0x258, 45.6, C1E2A12
#define C1E2A11_S                                                         0x08818000800011f8    // 0x258, 45.7, C1E2A11
#define C1E2A13_S                                                         0x0882000080001200    // 0x258, 46.0, C1E2A13
#define C1E2B12_S                                                         0x0882800080001208    // 0x258, 46.1, C1E2B12
#define C1E2B11_S                                                         0x0883000080001210    // 0x258, 46.2, C1E2B11
#define C1E2B13_S                                                         0x0883800080001218    // 0x258, 46.3, C1E2B13
#define C1E2C12_S                                                         0x0884000080001220    // 0x258, 46.4, C1E2C12
#define C1E2C11_S                                                         0x0884800080001228    // 0x258, 46.5, C1E2C11
#define C1E2D12_S                                                         0x0885000080001230    // 0x258, 46.6, C1E2D12
#define C1E2D11_S                                                         0x0885800080001238    // 0x258, 46.7, C1E2D11
#define C1E2E12_S                                                         0x0886000080001240    // 0x258, 47.0, C1E2E12
#define C1E2E11_S                                                         0x0886800080001248    // 0x258, 47.1, C1E2E11
#define C1E2F12_S                                                         0x0887000080001250    // 0x258, 47.2, C1E2F12
#define C1E2F11_S                                                         0x0887800080001258    // 0x258, 47.3, C1E2F11
#define C1E3012_S                                                         0x0888000080001260    // 0x258, 47.4, C1E3012
#define C1E3011_S                                                         0x0888800080001268    // 0x258, 47.5, C1E3011
#define C1E3013_S                                                         0x0889000080001270    // 0x258, 47.6, C1E3013
#define C1E3112_S                                                         0x0889800080001278    // 0x258, 47.7, C1E3112
#define C1E3111_S                                                         0x088a000080001280    // 0x258, 48.0, C1E3111
#define C1E3113_S                                                         0x088a800080001288    // 0x258, 48.1, C1E3113
#define C1E3212_S                                                         0x088b000080001290    // 0x258, 48.2, C1E3212
#define C1E3211_S                                                         0x088b800080001298    // 0x258, 48.3, C1E3211
#define C1E3213_S                                                         0x088c0000800012a0    // 0x258, 48.4, C1E3213
#define C1E3312_S                                                         0x088c8000800012a8    // 0x258, 48.5, C1E3312
#define C1E3311_S                                                         0x088d0000800012b0    // 0x258, 48.6, C1E3311
#define C1E3313_S                                                         0x088d8000800012b8    // 0x258, 48.7, C1E3313
#define C1E3412_S                                                         0x088e0000800012c0    // 0x258, 49.0, C1E3412
#define C1E3411_S                                                         0x088e8000800012c8    // 0x258, 49.1, C1E3411
#define C1E3413_S                                                         0x088f0000800012d0    // 0x258, 49.2, C1E3413
#define C1E3512_S                                                         0x088f8000800012d8    // 0x258, 49.3, C1E3512
#define C1E3511_S                                                         0x08900000800012e0    // 0x258, 49.4, C1E3511
#define C1E3513_S                                                         0x08908000800012e8    // 0x258, 49.5, C1E3513
#define C1E3612_S                                                         0x08910000800012f0    // 0x258, 49.6, C1E3612
#define C1E3611_S                                                         0x08918000800012f8    // 0x258, 49.7, C1E3611
#define C1E3613_S                                                         0x0892000080001300    // 0x258, 50.0, C1E3613
#define C1E3617_S                                                         0x0892800080001308    // 0x258, 50.1, C1E3617
#define C1E3616_S                                                         0x0893000080001310    // 0x258, 50.2, C1E3616
#define C1E3610_S                                                         0x0893800080001318    // 0x258, 50.3, C1E3610
#define C2DAE00_S                                                         0x0894000080001320    // 0x258, 50.4, C2DAE00
#define U10B787_S                                                         0x0894800080001328    // 0x258, 50.5, U10B787
#define C2DB012_S                                                         0x0895000080001330    // 0x258, 50.6, C2DB012
#define C2DB011_S                                                         0x0895800080001338    // 0x258, 50.7, C2DB011
#define C2DB029_S                                                         0x0896000080001340    // 0x258, 51.0, C2DB029
#define C2DB112_S                                                         0x0896800080001348    // 0x258, 51.1, C2DB112
#define C2DB111_S                                                         0x0897000080001350    // 0x258, 51.2, C2DB111
#define C2DB129_S                                                         0x0897800080001358    // 0x258, 51.3, C2DB129
#define C2DC707_S                                                         0x0898000080001360    // 0x258, 51.4, C2DC707
#define C2DB013_S                                                         0x0898800080001368    // 0x258, 51.5, C2DB013
#define C2DB113_S                                                         0x0899000080001370    // 0x258, 51.6, C2DB113
#define C2DAE13_S                                                         0x0899800080001378    // 0x258, 51.7, C2DAE13
#define C2DAF13_S                                                         0x089a000080001380    // 0x258, 52.0, C2DAF13
#define C2DB212_S                                                         0x089a800080001388    // 0x258, 52.1, C2DB212
#define C2DB211_S                                                         0x089b000080001390    // 0x258, 52.2, C2DB211
#define C2DB213_S                                                         0x089b800080001398    // 0x258, 52.3, C2DB213
#define C2DB312_S                                                         0x089c0000800013a0    // 0x258, 52.4, C2DB312
#define C2DB311_S                                                         0x089c8000800013a8    // 0x258, 52.5, C2DB311
#define C2DB313_S                                                         0x089d0000800013b0    // 0x258, 52.6, C2DB313
#define C2DB412_S                                                         0x089d8000800013b8    // 0x258, 52.7, C2DB412
#define C2DB411_S                                                         0x089e0000800013c0    // 0x258, 53.0, C2DB411
#define C2DB413_S                                                         0x089e8000800013c8    // 0x258, 53.1, C2DB413
#define C2DB512_S                                                         0x089f0000800013d0    // 0x258, 53.2, C2DB512
#define C2DB511_S                                                         0x089f8000800013d8    // 0x258, 53.3, C2DB511
#define C2DB513_S                                                         0x08a00000800013e0    // 0x258, 53.4, C2DB513
#define C2DB612_S                                                         0x08a08000800013e8    // 0x258, 53.5, C2DB612
#define C2DB611_S                                                         0x08a10000800013f0    // 0x258, 53.6, C2DB611
#define C2DB613_S                                                         0x08a18000800013f8    // 0x258, 53.7, C2DB613
#define C2DB712_S                                                         0x08a2000080001400    // 0x258, 54.0, C2DB712
#define C2DB711_S                                                         0x08a2800080001408    // 0x258, 54.1, C2DB711
#define C2DB713_S                                                         0x08a3000080001410    // 0x258, 54.2, C2DB713
#define C2DB912_S                                                         0x08a3800080001418    // 0x258, 54.3, C2DB912
#define C2DB911_S                                                         0x08a4000080001420    // 0x258, 54.4, C2DB911
#define C2DB913_S                                                         0x08a4800080001428    // 0x258, 54.5, C2DB913
#define C2DBB12_S                                                         0x08a5000080001430    // 0x258, 54.6, C2DBB12
#define C2DBB11_S                                                         0x08a5800080001438    // 0x258, 54.7, C2DBB11
#define C2DBB13_S                                                         0x08a6000080001440    // 0x258, 55.0, C2DBB13
#define C2DBD12_S                                                         0x08a6800080001448    // 0x258, 55.1, C2DBD12
#define C2DBD11_S                                                         0x08a7000080001450    // 0x258, 55.2, C2DBD11
#define C2DBD13_S                                                         0x08a7800080001458    // 0x258, 55.3, C2DBD13
#define C2DBE12_S                                                         0x08a8000080001460    // 0x258, 55.4, C2DBE12
#define C2DBE11_S                                                         0x08a8800080001468    // 0x258, 55.5, C2DBE11
#define C2DBE13_S                                                         0x08a9000080001470    // 0x258, 55.6, C2DBE13
#define C2DC012_S                                                         0x08a9800080001478    // 0x258, 55.7, C2DC012
#define C2DC011_S                                                         0x08aa000080001480    // 0x258, 56.0, C2DC011
#define C2DC013_S                                                         0x08aa800080001488    // 0x258, 56.1, C2DC013
#define C2DC112_S                                                         0x08ab000080001490    // 0x258, 56.2, C2DC112
#define C2DC111_S                                                         0x08ab800080001498    // 0x258, 56.3, C2DC111
#define C2DC113_S                                                         0x08ac0000800014a0    // 0x258, 56.4, C2DC113
#define C2DC212_S                                                         0x08ac8000800014a8    // 0x258, 56.5, C2DC212
#define C2DC211_S                                                         0x08ad0000800014b0    // 0x258, 56.6, C2DC211
#define C2DC213_S                                                         0x08ad8000800014b8    // 0x258, 56.7, C2DC213
#define C2DC312_S                                                         0x08ae0000800014c0    // 0x258, 57.0, C2DC312
#define C2DC311_S                                                         0x08ae8000800014c8    // 0x258, 57.1, C2DC311
#define C2DC313_S                                                         0x08af0000800014d0    // 0x258, 57.2, C2DC313
#define C2DBC00_S                                                         0x08af8000800014d8    // 0x258, 57.3, C2DBC00
#define C2DB300_S                                                         0x08b00000800014e0    // 0x258, 57.4, C2DB300
#define C2DB400_S                                                         0x08b08000800014e8    // 0x258, 57.5, C2DB400
#define C2DB500_S                                                         0x08b10000800014f0    // 0x258, 57.6, C2DB500
#define C2DB600_S                                                         0x08b18000800014f8    // 0x258, 57.7, C2DB600
#define C2DB700_S                                                         0x08b2000080001500    // 0x258, 58.0, C2DB700
#define C2DB800_S                                                         0x08b2800080001508    // 0x258, 58.1, C2DB800
#define C2DB900_S                                                         0x08b3000080001510    // 0x258, 58.2, C2DB900
#define C2DBA00_S                                                         0x08b3800080001518    // 0x258, 58.3, C2DBA00
#define C2DBB00_S                                                         0x08b4000080001520    // 0x258, 58.4, C2DBB00
#define C2DC400_S                                                         0x08b4800080001528    // 0x258, 58.5, C2DC400
#define C1E2C13_S                                                         0x08b5000080001530    // 0x258, 58.6, C1E2C13
#define C1E2D13_S                                                         0x08b5800080001538    // 0x258, 58.7, C1E2D13
#define C1E2E13_S                                                         0x08b6000080001540    // 0x258, 59.0, C1E2E13
#define C1E2F13_S                                                         0x08b6800080001548    // 0x258, 59.1, C1E2F13
#define C2DC500_S                                                         0x08b7000080001550    // 0x258, 59.2, C2DC500
#define C2DC700_S                                                         0x08b7800080001558    // 0x258, 59.3, C2DC700
#define C2DC701_S                                                         0x08b8000080001560    // 0x258, 59.4, C2DC701
#define C2DC702_S                                                         0x08b8800080001568    // 0x258, 59.5, C2DC702
#define C2DC703_S                                                         0x08b9000080001570    // 0x258, 59.6, C2DC703
#define C2DC704_S                                                         0x08b9800080001578    // 0x258, 59.7, C2DC704
#define C2DC705_S                                                         0x08ba000080001580    // 0x258, 60.0, C2DC705
#define C2DC708_S                                                         0x08ba800080001588    // 0x258, 60.1, C2DC708
#define C2DC706_S                                                         0x08bb000080001590    // 0x258, 60.2, C2DC706
#define C2DC800_S                                                         0x08bb800080001598    // 0x258, 60.3, C2DC800
#define U011187_S                                                         0x08bc0000800015a0    // 0x258, 60.4, U011187
#define C2DC900_S                                                         0x08bc8000800015a8    // 0x258, 60.5, C2DC900
#define C2DCB92_S                                                         0x08bd0000800015b0    // 0x258, 60.6, C2DCB92
#define C2DCA92_S                                                         0x08bd8000800015b8    // 0x258, 60.7, C2DCA92
#define C2DCA31_S                                                         0x08be0000800015c0    // 0x258, 61.0, C2DCA31
#define C2DB811_S                                                         0x08be8000800015c8    // 0x258, 61.1, C2DB811
#define C2DB812_S                                                         0x08bf0000800015d0    // 0x258, 61.2, C2DB812
#define C2DB813_S                                                         0x08bf8000800015d8    // 0x258, 61.3, C2DB813
#define C2DCA00_S                                                         0x08c00000800015e0    // 0x258, 61.4, C2DCA00
#define C2DCB00_S                                                         0x08c08000800015e8    // 0x258, 61.5, C2DCB00
#define C2DCC00_S                                                         0x08c10000800015f0    // 0x258, 61.6, C2DCC00
#define C2DCD00_S                                                         0x08c18000800015f8    // 0x258, 61.7, C2DCD00
#define C2DCE00_S                                                         0x08c2000080001600    // 0x258, 62.0, C2DCE00
#define C2DCF00_S                                                         0x08c2800080001608    // 0x258, 62.1, C2DCF00
#define C2DD000_S                                                         0x08c3000080001610    // 0x258, 62.2, C2DD000
#define C2E0000_S                                                         0x08c3800080001618    // 0x258, 62.3, C2E0000
#define C2E0001_S                                                         0x08c4000080001620    // 0x258, 62.4, C2E0001
#define C2E0002_S                                                         0x08c4800080001628    // 0x258, 62.5, C2E0002
#define C2E0003_S                                                         0x08c5000080001630    // 0x258, 62.6, C2E0003
#define C2E0004_S                                                         0x08c5800080001638    // 0x258, 62.7, C2E0004
#define C2E0005_S                                                         0x08c6000080001640    // 0x258, 63.0, C2E0005
#define U040887_S                                                         0x08c6800080001648    // 0x258, 63.1, U040887
#define U040987_S                                                         0x08c7000080001650    // 0x258, 63.2, U040987
#define C2E0006_S                                                         0x08c7800080001658    // 0x258, 63.3, C2E0006
#define C2DA500_S                                                         0x08c8000080001660    // 0x258, 63.4, C2DA500
#define U102A88_S                                                         0x08c8800080001668    // 0x258, 63.5, U102A88
#define U007388_S                                                         0x08c9000080001670    // 0x258, 63.6, U007388
#define U2CB111_S                                                         0x08c9800080001678    // 0x258, 63.7, U2CB111
#define C11710A_S                                                         0x08ca000080001680    // 0x258, 64.0, C11710A
#define C2DD42B_S                                                         0x08ca800080001688    // 0x258, 64.1, C2DD42B
#define PDC_DISTANCE_FL_267_S                                             0x0147d802b0001690    // 0x267, 1.0-1.4, 倒车雷达左前区距离显示
#define PDC_DISTANCE_FML_267_S                                            0x01485812b0001698    // 0x267, 1.5-2.1, 倒车雷达左中前区距离显示
#define PDC_DISTANCE_FMR_267_S                                            0x0148d822b00016a0    // 0x267, 2.2-2.6, 倒车雷达右中前区距离显示
#define PDC_DISTANCE_FR_267_S                                             0x01495832b00016a8    // 0x267, 2.7-3.3, 倒车雷达右前区距离显示
#define PDC_DISTANCE_RL_267_S                                             0x0149d842b00016b0    // 0x267, 3.4-4.0, 倒车雷达左后区距离显示
#define PDC_DISTANCE_RML_267_S                                            0x014a5852b00016b8    // 0x267, 4.1-4.5, 倒车雷达左中后区距离显示
#define PDC_DISTANCE_RMR_267_S                                            0x014ad862b00016c0    // 0x267, 4.6-5.2, 倒车雷达右中后区距离显示
#define PDC_DISTANCE_RR_267_S                                             0x014b5872b00016c8    // 0x267, 5.3-5.7, 倒车雷达右后区距离显示
#define PAS_Status_267_S                                                  0x014b8002b00016d0    // 0x267, 7.0, 驻车辅助系统状态
#define DiSus_C_UserDefMode_S                                             0x014c0002c00016d8    // 0x28A, 30.0-30.2, 阻尼自定义实际子模式
#define Left_Front_Door_Status_294_S                                      0x014c8002d00016e0    // 0x294, 1.0-1.1, 左前门状态
#define Right_Front_Door_Status_294_S                                     0x014d0002d00016e8    // 0x294, 1.2-1.3, 右前门状态
#define Left_Back_Door_Status_294_S                                       0x014d8002d00016f0    // 0x294, 1.4-1.5, 左后门状态
#define Right_Back_Door_Status_294_S                                      0x014e0002d00016f8    // 0x294, 1.6-1.7, 右后门状态
#define LeftF_Door_Stats_Valid_Bit_S                                      0x014e8002d0001700    // 0x294, 2.0, 左前门状态有效位
#define RightF_Door_Sts_Valid_Bit_294_S                                   0x014f0002d0001708    // 0x294, 2.1, 右前门状态有效位
#define LeftR_Door_Stats_Valid_Bit_S                                      0x014f8002d0001710    // 0x294, 2.2, 左后门状态有效位
#define RightR_Door_Sts_Valid_Bit_294_S                                   0x01500002d0001718    // 0x294, 2.3, 右后门状态有效位
#define driver_seat_belts_significant_bit                                 0x01508002d0001720    // 0x294, 2.4, 驾驶员安全带状态有效位
#define Driver_Belt_Status_S                                              0x01510002d0001728    // 0x294, 3.0-3.1, 驾驶员安全带状态
#define Back_Door_Status_294_S                                            0x01518002d0001730    // 0x294, 3.2-3.3, 后背门状态
#define Former_Hatch_Status_294_S                                         0x01520002d0001738    // 0x294, 3.4-3.5, 前舱盖状态
#define Rolling_Counter_294_S                                             0x01528002d0001740    // 0x294, 7.0-7.3, 循环丢包计数器
#define DiDyna_SMC_PAD                                                    0x08cb000090001748    // 0x2A1, 1.0-1.1, SMC回执给PAD的请求信号
#define Didyna_TBC_Currentworking_State_S                                 0x08cb800090001750    // 0x2A1, 1.4-1.5, 爆胎稳定控制开关信号
#define DiDyna_TBC_Switch_mode                                            0x08cc000090001758    // 0x2A1, 2.7, 爆胎稳定控制开关模式
#define DiDyna_SMC_Switch_mode                                            0x08cc800090001760    // 0x2A1, 3.0, PAD自学习信号
#define g_AFS_out_Com_u8Gear_Z_2A1_S                                      0x08cd000090001768    // 0x2A1, 3.4-3.5, 主驾侧翼功能等级反馈
#define g_AFS_out_Com_u8Gear_F_2A1_S                                      0x08cd800090001770    // 0x2A1, 3.6-3.7, 副驾侧翼功能等级反馈
#define Left_Side_Wing_Of_Main_Driver_S                                   0x08ce000090001778    // 0x2A1, 4.0-4.1, 主驾左气袋充放气指令
#define Right_Side_Wing_Of_Main_Driver_S                                  0x08ce800090001780    // 0x2A1, 4.2-4.3, 主驾右气袋充放气指令
#define Left_Side_Wing_Of_CoPilot_S                                       0x08cf000090001788    // 0x2A1, 4.4-4.5, 副驾左气袋充放气指令
#define Right_Side_Wing_Of_CoPilot_S                                      0x08cf800090001790    // 0x2A1, 4.6-4.7, 副驾右气袋充放气指令
#define Date_Information_Year_2B6_S                                       0x01530002e0001798    // 0x2B6, 1.0-1.7, 日期信息：年
#define Date_Information_Month_2B6_S                                      0x01538002e00017a0    // 0x2B6, 2.0-2.7, 日期信息：月
#define Date_Information_Day_2B6_S                                        0x01540002e00017a8    // 0x2B6, 3.0-3.7, 日期信息：日
#define Date_Information_Hour_2B6_S                                       0x01548002e00017b0    // 0x2B6, 4.0-4.7, 时间信息：时
#define Date_Information_Minute_2B6_S                                     0x01550002e00017b8    // 0x2B6, 5.0-5.7, 时间信息：分
#define Date_Information_Second_2B6_S                                     0x01558002e00017c0    // 0x2B6, 6.0-6.7, 时间信息：秒
#define Week_2B6_S                                                        0x01560002e00017c8    // 0x2B6, 7.0-7.2, 星期
#define Time_2B6_S                                                        0x01568002e00017d0    // 0x2B6, 7.3-7.4, 时制
#define Main_Seat_Left_Flank_Pres_Feedback_S                              0x01570002f81017d8    // 0x2E4, 2.0-2.7, 主驾坐垫左侧侧翼压力值反馈
#define Main_Seat_Right_Flank_Pres_Feedback_S                             0x01578002f81017e0    // 0x2E4, 3.0-3.7, 主驾坐垫右侧侧翼压力值反馈
#define Main_Driver_Back_L_Flank_Pres_Feedback_S                          0x01580002f81017e8    // 0x2E4, 4.0-4.7, 主驾靠背左侧侧翼压力值反馈
#define Main_Driver_Back_R_Flank_Pres_Feedback_S                          0x01588002f81017f0    // 0x2E4, 5.0-5.7, 主驾靠背右侧侧翼压力值反馈
#define Main_Driver_Flank_Cond_Sta_Feedback_S                             0x01590002f81017f8    // 0x2E4, 6.0-6.1, 主驾侧翼调节状态反馈
#define First_Officer_Left_Flank_Pres_Feedback_S                          0x01598002f8121800    // 0x2E4, 2.0-2.7, 副驾坐垫左侧侧翼压力值反馈
#define First_Officer_Right_Flank_Pres_Feedback_S                         0x015a0002f8121808    // 0x2E4, 3.0-3.7, 副驾坐垫右侧侧翼压力值反馈
#define First_Officer_Back_L_Flank_Pres_Feedback_S                        0x015a8002f8121810    // 0x2E4, 4.0-4.7, 副驾靠背左侧侧翼压力值反馈
#define First_Officer_Back_R_Flank_Pres_Feedback_S                        0x015b0002f8121818    // 0x2E4, 5.0-5.7, 副驾靠背右侧侧翼压力值反馈
#define First_Officer_Flank_Cond_Sta_Feedback_S                           0x015b8002f8121820    // 0x2E4, 6.0-6.1, 副驾侧翼调节状态反馈
#define LR_Left_Flank_Pres_Feedback_S                                     0x015c0002f8141828    // 0x2E4, 2.0-2.7, 左后坐垫左侧侧翼压力值反馈
#define LR_Right_Flank_Pres_Feedback_S                                    0x015c8002f8141830    // 0x2E4, 3.0-3.7, 左后坐垫右侧侧翼压力值反馈
#define LR_Backrest_L_Flank_Pres_Feedback_S                               0x015d0002f8141838    // 0x2E4, 4.0-4.7, 左后靠背左侧侧翼压力值反馈
#define LR_Backrest_R_Flank_Pres_Feedback_S                               0x015d8002f8141840    // 0x2E4, 5.0-5.7, 左后靠背右侧侧翼压力值反馈
#define FR_Flank_Conditioning_Sta_Feedback_S                              0x015e0002f8141848    // 0x2E4, 6.0-6.1, 左后侧翼调节状态反馈
#define RR_Left_Flank_Pres_Feedback_S                                     0x015e8002f8161850    // 0x2E4, 2.0-2.7, 右后坐垫左侧侧翼压力值反馈
#define RR_Right_Flank_Pres_Feedback_S                                    0x015f0002f8161858    // 0x2E4, 3.0-3.7, 右后坐垫右侧侧翼压力值反馈
#define RR_Backrest_L_Flank_Pres_Feedback_S                               0x015f8002f8161860    // 0x2E4, 4.0-4.7, 右后靠背左侧侧翼压力值反馈
#define RR_Backrest_R_Flank_Pres_Feedback_S                               0x01600002f8161868    // 0x2E4, 5.0-5.7, 右后靠背右侧侧翼压力值反馈
#define RR_Flank_Conditioning_Sta_Feedback_S                              0x01608002f8161870    // 0x2E4, 6.0-6.1, 右后侧翼调节状态反馈
#define Oil_Life_Reset_Button_2EA_S                                       0x2161000300001878    // 0x2EA, 1.2, 机油寿命复位按键
#define Transmi_Oil_Life_Reset_Button_S                                   0x2161800300001880    // 0x2EA, 6.7, 变速器油寿命复位按键
#define Blade_Bat_Cool_Life_Reset_But_S                                   0x2162000300001888    // 0x2EA, 7.0, 刀片电池冷却液寿命复位按键
#define Longterm_Cool_Life_Reset_Butt_S                                   0x2162800300001890    // 0x2EA, 7.1, 长效冷却液寿命复位按键
#define Brake_Fluid_Life_Reset_Button_S                                   0x2163000300001898    // 0x2EA, 7.2, 制动液寿命复位按键
#define Main_Seat_Left_Flank_Pres_Feedback_2FF_S                          0x01638003181818a0    // 0x2FF, 2.0-2.7, 主驾坐垫左侧侧翼压力值反馈
#define Main_Seat_Right_Flank_Pres_Feedback_2FF_S                         0x01640003181818a8    // 0x2FF, 3.0-3.7, 主驾坐垫右侧侧翼压力值反馈
#define Main_Driver_Back_L_Flank_Pres_Feedback_2FF_S                      0x01648003181818b0    // 0x2FF, 4.0-4.7, 主驾靠背左侧侧翼压力值反馈
#define Main_Driver_Back_R_Flank_Pres_Feedback_2FF_S                      0x01650003181818b8    // 0x2FF, 5.0-5.7, 主驾靠背右侧侧翼压力值反馈
#define Main_Driver_Flank_Cond_Status_Feedback_2FF_S                      0x01658003181818c0    // 0x2FF, 6.0-6.1, 主驾侧翼调节状态反馈
#define First_Officer_Left_Flank_Pres_Feedback_2FF_S                      0x01660003181a18c8    // 0x2FF, 2.0-2.7, 副驾坐垫左侧侧翼压力值反馈
#define First_Officer_Right_Flank_Pres_Feedback_2FF_S                     0x01668003181a18d0    // 0x2FF, 3.0-3.7, 副驾坐垫右侧侧翼压力值反馈
#define First_Officer_Back_L_Flank_Pres_Feedback_2FF_S                    0x01670003181a18d8    // 0x2FF, 4.0-4.7, 副驾靠背左侧侧翼压力值反馈
#define First_Officer_Back_R_Flank_Pres_Feedback_2FF_S                    0x01678003181a18e0    // 0x2FF, 5.0-5.7, 副驾靠背右侧侧翼压力值反馈
#define First_Officer_Flank_Cond_Status_Feedback_2FF_S                    0x01680003181a18e8    // 0x2FF, 6.0-6.1, 副驾侧翼调节状态反馈
#define LR_Left_Flank_Pressure_Feedback_2FF_S                             0x01688003181c18f0    // 0x2FF, 2.0-2.7, 左后坐垫左侧侧翼压力值反馈
#define LR_Right_Flank_Pressure_Feedback_2FF_S                            0x01690003181c18f8    // 0x2FF, 3.0-3.7, 左后坐垫右侧侧翼压力值反馈
#define LR_Backrest_L_Flank_Pressure_Feedback_2FF_S                       0x01698003181c1900    // 0x2FF, 4.0-4.7, 左后靠背左侧侧翼压力值反馈
#define LR_Backrest_R_Flank_Pressure_Feedback_2FF_S                       0x016a0003181c1908    // 0x2FF, 5.0-5.7, 左后靠背右侧侧翼压力值反馈
#define FR_Flank_Conditioning_Status_Feedback_2FF_S                       0x016a8003181c1910    // 0x2FF, 6.0-6.1, 左后侧翼调节状态反馈
#define RR_Left_Flank_Pressure_Feedback_2FF_S                             0x016b0003181e1918    // 0x2FF, 2.0-2.7, 右后坐垫左侧侧翼压力值反馈
#define RR_Right_Flank_Pressure_Feedback_2FF_S                            0x016b8003181e1920    // 0x2FF, 3.0-3.7, 右后坐垫右侧侧翼压力值反馈
#define RR_Backrest_L_Flank_Pressure_Feedback_2FF_S                       0x016c0003181e1928    // 0x2FF, 4.0-4.7, 右后靠背左侧侧翼压力值反馈
#define RR_Backrest_R_Flank_Pressure_Feedback_2FF_S                       0x016c8003181e1930    // 0x2FF, 5.0-5.7, 右后靠背右侧侧翼压力值反馈
#define RR_Flank_Conditioning_Status_Feedback_2FF_S                       0x016d0003181e1938    // 0x2FF, 6.0-6.1, 右后侧翼调节状态反馈
#define VCU_Pkg_FuncEcho_302_S                                            0x016d800328201940    // 0x302, 44.5-44.6, VCU泊车状态
#define Engine_Water_Temp_Thermostat_S                                    0x016e588330001948    // 0x30D, 1.0-1.7, 发动机节温器端水温
#define ECM_Instant_Fuel_Consumption_S                                    0x016ed89330001950    // 0x30D, 2.0-3.7, 瞬时油耗
#define CoPilot_Seat_Belt_Status_312_S                                    0x016f000340001958    // 0x312, 1.0-1.1, 副驾安全带状态312
#define Rear_Left_Second_Row_Seat_Belt_Status_312_S                       0x016f800340001960    // 0x312, 1.2-1.3, 左后二排安全带状态312
#define Rear_Right_Second_Row_Seat_Belt_Status_312_S                      0x0170000340001968    // 0x312, 1.4-1.5, 右后二排安全带状态312
#define Middle_Rear_Second_Row_Seat_Belt_Status_312_S                     0x0170800340001970    // 0x312, 1.6-1.7, 中后二排安全带状态312
#define ThreeRow_Left_Seat_Belt_Status_312_S                              0x0171000340001978    // 0x312, 2.0-2.1, 第三排左座安全带状态312
#define ThreeRow_Middle_Seat_Belt_Status_312_S                            0x0171800340001980    // 0x312, 2.2-2.3, 第三排中座安全带状态312
#define ThreeRow_Right_Seat_Belt_Status_312_S                             0x0172000340001988    // 0x312, 2.4-2.5, 第三排右座安全带状态312
#define CoDriver_Seat_312_S                                               0x0172800340001990    // 0x312, 2.6-2.7, 副驾座椅312
#define Driver_Seat_S                                                     0x0173000340001998    // 0x312, 8.0-8.1, 主驾座椅
#define StrngWhlTorqVD_S                                                  0x01738003500019a0    // 0x318, 3.7, 方向盘转矩有效位
#define StrngWhlTorq_S                                                    0x017458a3500019a8    // 0x318, 4.0-5.3, 方向盘转矩
#define Rolling_Counter_318_S                                             0x01748003500019b0    // 0x318, 7.4-7.7, 滚动循环计数器318
#define PAS_status_S                                                      0x01750003682219b8    // 0x31A, 2.4-2.6, 倒车雷达系统状态
#define Show_Car_Mode_31E_S                                               0x01758003700019c0    // 0x31E, 1.0-1.3, 展车模式
#define Camping_Mode_S                                                    0x01760003700019c8    // 0x31E, 2.5-2.7, 露营模式
#define IPB_Simulator_Pressure_S                                          0x0176d8b3800019d0    // 0x321, 1.0-2.3, IPB_SimulatorPressure Simulator压力
#define IPB_Simulator_Pressure_Status_S                                   0x01770003800019d8    // 0x321, 2.4, IPB_SimulatorPressure_Sts Simulator压力状态
#define IPB_PlungerPressure_Status_321_S                                  0x01778003800019e0    // 0x321, 2.5, IPB_PlungerPressure_status
#define IPB_Plunger_Pressure_321_S                                        0x01780003800019e8    // 0x321, 3.0-4.3, IPB_PlungerPressure Plunger压力
#define Rolling_Counter_321_S                                             0x01788003800019f0    // 0x321, 7.4-7.7, 滚动循环计数器321
#define No_More_Reminder_Of_Oil_Life_S                                    0x21790003982419f8    // 0x32B, 2.0, 机油寿命不再提醒
#define Transmission_Oil_Life_Is_No_Longer_Reminded_S                     0x2179800398241a00    // 0x32B, 2.1, 变速器油寿命不再提醒
#define Brake_Fluid_Life_Will_No_Longer_Be_Reminded_S                     0x217a000398241a08    // 0x32B, 2.2, 制动液寿命不再提醒
#define Long_Life_Coolant_Life_Is_No_Longer_Reminded_S                    0x217a800398241a10    // 0x32B, 2.3, 长效冷却液寿命不再提醒
#define Blade_Battery_Coolant_Life_Is_No_Longer_Reminded_S                0x217b000398241a18    // 0x32B, 2.4, 刀片电池冷却液寿命不再提醒
#define Media_DiDyna_SMC_Work_Switch                                      0x217b800398241a20    // 0x32B, 3.2-3.3, SMC功能软开关标志位
#define Media_DiDyna_TBC_Work_Switch_S                                    0x217c000398241a28    // 0x32B, 3.4-3.5, PAD端TBC功能软开关标志位
#define Req_EBA_Decel_Ctrl_S                                              0x017c8003a0001a30    // 0x32F, 3.0, Req_EBA_Decel_Ctrl_S
#define AWB_Request_S                                                     0x017d0003a0001a38    // 0x32F, 3.3, AWB_Request_S
#define AEB_DecCtrl_Request_S                                             0x017d8003a0001a40    // 0x32F, 3.7, AEB_DecCtrl_Request_S
#define Rolling_Counter_32F_S                                             0x017e0003a0001a48    // 0x32F, 7.0-7.3, AliveCounter32F
#define SuspInhibitReq32F_S                                               0x017e8003a0001a50    // 0x32F, 7.4-7.6, 抑制悬架请求
#define SuspInhibitReq32F_Valid_S                                         0x017f0003a0001a58    // 0x32F, 7.7, 抑制悬架请求有效位
#define High_Pree_Req_Log_On_Back_Plat_S                                  0x017f8003b0001a60    // 0x336, 7.2-7.5, 后台上高压申请记录
#define Background_High_Pressure_S                                        0x01800003b0001a68    // 0x336, 7.6-7.7, 后台高压
#define VCU_READY_Indicator_Light_S                                       0x01808003c0001a70    // 0x341, 2.0-2.1, READY指示灯
#define Actual_Throttle_Depth_S                                           0x018158c3d0001a78    // 0x342, 1.0-1.7, 实际油门深度
#define VCU_Brake_Depth_S                                                 0x01818003d0001a80    // 0x342, 2.0-2.7, 制动深度
#define Actual_Throttle_Depth_Effective_Sign_S                            0x01820003d0001a88    // 0x342, 3.0, 实际油门深度有效标志
#define VCU_Brake_Depth_Virtual_Value_S                                   0x01828003d0001a90    // 0x342, 3.1, 制动深度有效位
#define VCU_Drive_Mode_1_342_S                                            0x01830003d0001a98    // 0x342, 3.4-3.5, 整车运行模式
#define VCU_Drive_Mode_2_342_S                                            0x01838003d0001aa0    // 0x342, 3.6-3.7, 整车运行模式2
#define Tar_rnk                                                           0x01840003d0001aa8    // 0x342, 4.0-4.3, 目标挡位
#define Veh_drv_sty                                                       0x01848003d0001ab0    // 0x342, 5.0-5.3, 整车驱动方式
#define Rolling_Counter_342_S                                             0x01850003d0001ab8    // 0x342, 7.4-7.7, 循环丢包计数器
#define Ejection_mod                                                      0x01858003e0001ac0    // 0x343, 5.0-5.3, 弹射状态
#define BMC_Charge_Gun_Connect_Status_S                                   0x01860003f0001ac8    // 0x344, 6.2-6.4, 充电枪连接状态
#define Veh_max_drv_whl_tq                                                0x0186d8d400008a98    // 0x356, 1.0-2.7, 当前最大整车扭矩
#define Veh_min_drv_whl_tq                                                0x018758e400008aa0    // 0x356, 3.0-4.7, 当前最小整车扭矩
#define Veh_max_drv_whl_tq_sat                                            0x0187800400001ad0    // 0x356, 5.0, 当前最大整车扭矩状态位
#define Veh_min_drv_whl_tq_sat                                            0x0188000400001ad8    // 0x356, 5.1, 当前最小整车扭矩状态位
#define tar_disus_mode_S                                                  0x0188800400001ae0    // 0x356, 6.0-6.7, 目标悬架模式
#define Rolling_Counter_356_S                                             0x0189000400001ae8    // 0x356, 7.4-7.7, 循环丢包计数器
#define FL_Tire_Press_Value_S                                             0x0189800410001af0    // 0x357, 1.0-2.3, 左前轮胎压力
#define FR_Tire_Press_Value_S                                             0x018a000410001af8    // 0x357, 2.4-3.7, 右前轮胎压力
#define RL_Tire_Press_Value_S                                             0x018a800410001b00    // 0x357, 4.0-5.3, 左后轮胎压力
#define RR_Tire_Press_Value_S                                             0x018b000410001b08    // 0x357, 5.4-6.7, 左后轮胎压力
#define Gear_Recognize_Status_S                                           0x018b800420001b10    // 0x35C, 2.0-2.2, 档位识别状态
#define Park_Button_Status_S                                              0x018c000420001b18    // 0x35C, 4.4-4.6, P开关状态
#define Rolling_Counter_35C_S                                             0x018c800420001b20    // 0x35C, 7.4-7.7, 滚动循环计数器35C
#define DiSus_SH_Adjust_Gear_Set_S                                        0x218d000430001b28    // 0x385, 7.2-7.3, Disus_C悬架模式控制开关信号
#define Oil_Life_38D_38D_S                                                0x18d058f0a0001b30    // 0x38D, 1.0-1.7, 机油寿命
#define Transmission_Oil_Life_38D_38D_S                                   0x18d0d900a0001b38    // 0x38D, 2.0-2.7, 变速器油寿命
#define Brake_Fluid_Life_38D_S                                            0x18d15910a0001b40    // 0x38D, 3.0-3.7, 制动液寿命
#define Blade_Battery_Coolant_Life_38D_S                                  0x18d1d920a0001b48    // 0x38D, 4.0-4.7, 刀片电池冷却液寿命
#define Longterm_Coolant_Life_38D_S                                       0x18d25930a0001b50    // 0x38D, 5.0-5.7, 长效冷却液寿命
#define Engine_Oil_Life_Reset_Feedback_38D_S                              0x18d28000a0001b58    // 0x38D, 6.0-6.1, 机油寿命复位反馈
#define Transmission_Oil_Life_Reset_Feedback_38D_S                        0x18d30000a0001b60    // 0x38D, 6.2-6.3, 变速器油寿命复位反馈
#define Blade_Battery_Coolant_Life_Reset_Feedback_38D_S                   0x18d38000a0001b68    // 0x38D, 6.4-6.5, 刀片电池冷却液寿命复位反馈
#define LongTerm_Coolant_Life_Reset_Feedback_38D_S                        0x18d40000a0001b70    // 0x38D, 6.6-6.7, 长效冷却液寿命复位反馈
#define Brake_Fluid_Life_Reset_Feedback_38D_S                             0x18d48000a0001b78    // 0x38D, 7.0-7.1, 制动液寿命复位反馈
#define Oil_Maintenance_Reminder_38D_S                                    0x18d50000a0001b80    // 0x38D, 7.2-7.3, 机油保养提醒
#define Transmission_Oil_Maintenance_Reminder_38D_S                       0x18d58000a0001b88    // 0x38D, 7.4-7.5, 变速器油保养提醒
#define Brake_Fluid_Maintenance_Reminder_38D_S                            0x18d60000a0001b90    // 0x38D, 7.6-7.7, 制动液保养提醒
#define LongTerm_Coolant_Maintenance_Reminder_38D_S                       0x18d68000a0001b98    // 0x38D, 8.0-8.1, 长效冷却液保养提醒
#define Blade_Battery_Coolant_Maintenance_Reminder_38D_S                  0x18d70000a0001ba0    // 0x38D, 8.2-8.3, 刀片电池冷却液保养提醒
#define Oil_Life_Development_SelfLearning_Status_Bit_S                    0x18d78000a0001ba8    // 0x38D, 8.4-8.5, 机油寿命开发自学习状态位
#define Transmission_Oil_Development_SelfLearning_Status_Bit_S            0x18d80000a0001bb0    // 0x38D, 8.6-8.7, 变速器油开发自学习状态位
#define Brake_Fluid_Life_Development_SelfLearning_Status_Bit_S            0x18d88000a0001bb8    // 0x38D, 9.0-9.1, 制动液寿命开发自学习状态位
#define Long_Life_Coolant_Life_Development_SelfLearning_Status_Bit_S      0x18d90000a0001bc0    // 0x38D, 9.2-9.3, 长效冷却液寿命开发自学习状态位
#define Blade_Battery_Cooling_Life_Development_SelfLearning_Status_Bit_S  0x18d98000a0001bc8    // 0x38D, 9.4-9.5, 刀片电池冷却液寿命开发自学习状态位
#define Main_Driver_Active_Flank_Strength_3CE_S                           0x118d800440001bd0    // 0x3CE, 7.6-7.7, 主驾主动侧翼强度
#define Pass_Seat_Active_Flank_Strength_S                                 0x118e000450001bd8    // 0x3D3, 7.6-7.7, 副驾主动侧翼强度
#define Inside_Temperature_3D8_S                                          0x018ed94460001be0    // 0x3D8, 6.0-6.7, 车内温度
#define Total_Distance_3D9s5_S                                            0x018f595478270000    // 0x3D9, 2.0-5.2, 总里程
#define HEV_Distance_3D9_S                                                0x018f800478290008    // 0x3D9, 3.0-5.3, HEV里程
#define Veh_Set_Fact_Dfault_Flg_S                                         0x2190000480001be8    // 0x3E3, 3.6, 车辆设置的恢复出厂标志位
#define Driv_Set_Fact_Dfault_Flg_S                                        0x2190800480001bf0    // 0x3E3, 3.7, 行驶设置的恢复出厂标志位
#define ACHMC_Outside_Temperature_S                                       0x1191596490001bf8    // 0x404, 7.0-7.7, 车外温度
#define Vehicle_Door_Lock_Logic_Status_S                                  0x11918004a0001c00    // 0x407, 2.2-2.3, 整车门锁逻辑状态
#define L_BAT_Capacity_Index                                              0x01920004b0001c08    // 0x444, 6.0-6.7, 电池组当前容量指数(分辨率1%)
#define S_BAT_Power_Mode                                                  0x01928004c0001c10    // 0x449, 8.2-8.3, 功耗模式
#define DiSus_C_UserDefInvaild_S                                          0x01930004d82a1c18    // 0x48B, 5.5, 阻尼自定义模式配置标志
#define Towing_Driving_Mode_Status                                        0x01938004e0001c20    // 0x490, 3.0-3.1, 带拖车行驶模式状态
#define Compressor_Working_Hours_S                                        0x08da0000b8001c28    // 0x495, 2.0-4.1, 压缩机工作时长
#define Exhaust_Valve_Action_Times_S                                      0x08da8000b8001c30    // 0x495, 4.2-6.5, 排气阀开关次数
#define Booster_Valve_Action_Times_S                                      0x08db0000b8001c38    // 0x495, 6.6-9.1, 增压阀开关次数
#define FL_LV_Action_Times_S                                              0x08db8000b8001c40    // 0x495, 9.2-11.5, FL调平阀开关次数
#define FR_LV_Action_Times_S                                              0x08dc0000b8001c48    // 0x495, 11.6-14.1, FR调平阀开关次数
#define RL_LV_Action_Times_S                                              0x08dc8000b8001c50    // 0x495, 14.2-16.5, RL调平阀开关次数
#define RR_LV_Action_Times_S                                              0x08dd0000b8001c58    // 0x495, 16.6-19.1, RR调平阀开关次数
#define ACCV_Action_Times_S                                               0x08dd8000b8001c60    // 0x495, 19.2-21.5, 储气罐阀开关次数
#define FourWayValve_Action_Times_S                                       0x08de0000b8001c68    // 0x495, 21.6-24.1, 侧翼四通阀开关次数
#define Replenishment_Frequency_S                                         0x08de8000b8001c70    // 0x495, 24.2-24.7, 补气次数
#define FL_Replenishment_Height_S                                         0x08df0000b8001c78    // 0x495, 25.0-25.7, FL补气高度
#define FR_Replenishment_Height_S                                         0x08df8000b8001c80    // 0x495, 26.0-26.7, FR补气高度
#define RL_Replenishment_Height_S                                         0x08e00000b8001c88    // 0x495, 27.0-27.7, RL补气高度
#define RR_Replenishment_Height_S                                         0x08e08000b8001c90    // 0x495, 28.0-28.7, RR补气高度
#define Inspector_Error_ID_S                                              0x08e10000b8001c98    // 0x495, 29.0-31.1, ASW故障ID
#define Compressor_Action_Times_S                                         0x08e18000b8001ca0    // 0x495, 31.2-33.5, 泵电机开关次数
#define Up_Down_Times_S                                                   0x08e20000b8001ca8    // 0x495, 33.6-36.1, 手动举升下降次数
#define FL_KV_Action_Times_S                                              0x08e28000b8001cb0    // 0x495, 36.2-38.5, FL刚度阀开关次数
#define FR_KV_Action_Times_S                                              0x08e30000b8001cb8    // 0x495, 38.6-41.1, FR刚度阀开关次数
#define RL_KV_Action_Times_S                                              0x08e38000b8001cc0    // 0x495, 41.2-43.5, RL刚度阀开关次数
#define RR_KV_Action_Times_S                                              0x08e40000b8001cc8    // 0x495, 43.6-46.1, RR刚度阀开关次数
#define Soft_Reset_Type_S                                                 0x08e48000b8001cd0    // 0x495, 46.2-46.7, 软件异常复位类型
#define Soft_Reset_Trigger_Reason_S                                       0x08e50000b8001cd8    // 0x495, 47.0-47.7, 软件异常复位触发原因
#define Soft_Reset_Count_S                                                0x08e58000b8001ce0    // 0x495, 48.0-48.7, 软件异常复位次数
#define Inspector_SlowDown80Kph_S                                         0x08e60000b8001ce8    // 0x495, 49.0, 高速行驶悬架系统安全提示
#define CCT2_0_Function_Switch_PAD_495                                    0x08e68000b8001cf0    // 0x495, 49.1-49.2, 舒适控制技术
#define Inspector_SWActorOutSig_S                                         0x08e70000b8001cf8    // 0x495, 49.3-52.7, 开关阀输出信号
#define Inspector_g_u8_Sts_S                                              0x08e78000b8001d00    // 0x495, 53.0-53.7, 悬架系统工作状态
#define Inspector_g_u8ASW_accu_ctr_S                                      0x08e80000b8001d08    // 0x495, 54.0-54.7, 悬架系统工作状态
#define Inspector_PressureSig_S                                           0x08e8d970b8001d10    // 0x495, 55.0-56.7, 悬架压力信号 单位bar
#define Inspector_SBAD_ID_S                                               0x08e90000b8001d18    // 0x495, 57.0-57.3, 悬架高度抑制类型
#define Inspector_ECUMode_S                                               0x08e98000b8001d20    // 0x495, 57.4-57.7, ECU模式
#define Actual_DiSus_Height_Mode_495_S                                    0x08ea0000b8001d28    // 0x495, 58.0-58.2, 实际悬架高度模式
#define DiSus_Height_Mode_OFF_495_S                                       0x08ea8000b8001d30    // 0x495, 58.3-58.4, 悬架高度模式OFF开关
#define DiSus_Welcome_Sig_495_S                                           0x08eb0000b8001d38    // 0x495, 58.5-58.6, 迎宾功能信号
#define DiSus_Rear_Suitcase_495_S                                         0x08eb8000b8001d40    // 0x495, 58.7-59.0, 后行李箱取物开关
#define DiSus_Balance_Switch_Sig_495_S                                    0x08ec0000b8001d48    // 0x495, 59.1-59.2, 露营调平功能开关
#define DiSus_Maintance_Switch_Sig_495_S                                  0x08ec8000b8001d50    // 0x495, 59.3-59.4, 千斤顶/举升机功能
#define CVC_Work_Mode_495_S                                               0x08ed0000b8001d58    // 0x495, 59.5-59.6, 底盘矢量控制开关
#define Inspector_g_bAllActPwrOff_S                                       0x08ed8000b8001d60    // 0x495, 59.7, 悬架系统断电信号
#define Inspector_TempSig_S                                               0x08ee5980b8001d68    // 0x495, 60.0-60.7, 悬架温度信号 PH=INT-50摄氏度
#define Inspector_IMU_Ax_S                                                0x08eed990b8001d70    // 0x495, 61.0-62.7, Inspector_IMU_Ax_S
#define Inspector_IMU_Ay_S                                                0x08ef59a0b8001d78    // 0x495, 63.0-64.7, Inspector_IMU_Ay_S
#define DiSus_Actual_Height_FL_495_S                                      0x08efd9b0b8021d80    // 0x495, 2.0-3.3, 左前悬架实际高度_495
#define DiSus_Actual_Height_FR_495_S                                      0x08f059c0b8021d88    // 0x495, 3.4-4.7, 右前悬架实际高度_495
#define DiSus_Actual_Height_RL_495_S                                      0x08f0d9d0b8021d90    // 0x495, 5.0-6.3, 左后悬架实际高度_495
#define DiSus_Actual_Height_RR_495_S                                      0x08f159e0b8021d98    // 0x495, 6.4-7.7, 右后悬架实际高度_495
#define Wakeup_Type_S                                                     0x08f18000b8021da0    // 0x495, 8.0-8.2, 唤醒方式
#define NVM_Exception_S                                                   0x08f20000b8021da8    // 0x495, 8.3-8.7, NVM异常
#define DiSus_Soft_Ver_495_S                                              0x08f28000b8021db0    // 0x495, 9.0-9.5, 悬架软件版本
#define AFS_Scheme_Ver_495_S                                              0x08f30000b8021db8    // 0x495, 9.6-9.7, 侧翼方案版本
#define DiEye_SettingStates_495_S                                         0x08f38000b8021dc0    // 0x495, 10.0-10.1, 预瞄设置开关
#define DiSus_HighAdj_Reason_495_S                                        0x08f40000b8021dc8    // 0x495, 10.2-10.7, 悬架高度调节原因
#define AFS_Fault_Status_S                                                0x08f48000b8021dd0    // 0x495, 11.0-11.3, 侧翼故障状态
#define AFS_Work_Status_S                                                 0x08f50000b8021dd8    // 0x495, 11.4-11.7, 侧翼工作状态
#define DiSus_CofigWord_495_S                                             0x08f58000b8021de0    // 0x495, 12.0-12.7, 悬架配置字
#define AFS_Press_Sensor_495_S                                            0x08f659f0b8021de8    // 0x495, 13.0-13.7, 侧翼压力传感器
#define Inspector_TempSig02_S                                             0x08f6da00b8021df0    // 0x495, 14.0-15.0, 悬架温度信号02
#define Inspector_SBAD_ID02_S                                             0x08f70000b8021df8    // 0x495, 15.1-15.5, 悬架高度抑制类型02
#define DiSus_AX                                                          0x08f7da10b8021e00    // 0x495, 15.6-16.4, DiSus_AX
#define DiSus_AY                                                          0x08f85a20b8021e08    // 0x495, 16.5-17.3, DiSus_AY
#define AFS_Ctr_S                                                         0x08f88000b8021e10    // 0x495, 17.4, 侧翼补气请求
#define AFS_Flag_S                                                        0x08f90000b8021e18    // 0x495, 17.5, 允许侧翼补气标志位
#define AFS_State_S                                                       0x08f98000b8021e20    // 0x495, 17.6-17.7, 侧翼补气状态
#define Medium_Enter_OTA_Mode_Req_S                                       0x11940004f0001e28    // 0x49A, 1.4, 进入OTA模式需求
#define Rolling_Counter_0x49A                                             0x11948004f0001e30    // 0x49A, 7.4-7.7, 滚动计数校验
#define Temperature_Unit_4A5_S_S                                          0x01950005082c1e38    // 0x4A5, 7.7, 温度单位
#define Outside_Temperatu_Display_4A5_S                                   0x0195da35082c1e40    // 0x4A5, 8.0-8.7, 车外温度显示
#define Height_Control_Status_4A8_S                                       0x21960005182e1e48    // 0x4A8, 2.0-2.7, 悬架高度控制状态
#define Suspension_Adjustment_Button_Additional_Parameters_S              0x1196800520001e50    // 0x4A9, 7.1, 悬架调节按键附加参数
#define Suspension_Adjustment_Switch_Signal_S                             0x1197000520001e58    // 0x4A9, 7.2-7.3, 悬架调节开关信号
#define g_ETS_In_Com_u8PADSW_S                                            0x2197800538301e60    // 0x4C1, 6.4-6.5, 电动尾翼软开关开闭指令（PAD开关下发）
#define g_ETS_In_Com_u8VoiceSW_S                                          0x2198000538301e68    // 0x4C1, 6.6-6.7, 电动尾翼语音开闭指令（PAD语音开闭下发）
#define g_ETS_In_Com_u8Welcome_S                                          0x2198800538301e70    // 0x4C1, 7.0-7.1, 尾翼迎宾功能设置项开关(PAD下发迎宾开关)
#define g_ETS_In_Com_u8Repair_S                                           0x2199000538301e78    // 0x4C1, 7.2-7.3, 电动尾翼维修功能(PAD下发维修开关)
#define g_ETS_In_Com_u8PADAuto_S                                          0x2199800538301e80    // 0x4C1, 8.4-8.5, 电动尾翼自动模式开关（PAD下发模式开关）
#define g_ETS_In_Com_u8VoiceAuto_S                                        0x219a000538301e88    // 0x4C1, 8.6-8.7, 电动尾翼自动模式语音开关（PAD语音模式开关下发）
#define Working_Strength_Of_Main_Driver_Active_Flanks_S                   0x219a800538321e90    // 0x4C1, 3.0-3.1, 主驾主动侧翼工作强度
#define CoPilot_Active_Wing_Working_Strength_S                            0x219b000538321e98    // 0x4C1, 8.0-8.1, 副驾主动侧翼工作强度
#define DiSus_C_UserDefModeReset_S                                        0x219b800548341ea0    // 0x4C1, 6.6-6.7, 阻尼自定义模式重置命令
#define Media_EPB_Switch                                                  0x219c000558361ea8    // 0x4EF, 2.0-2.1, EPB软开关
#define DiEye_Switch_State_S                                              0x219c800558381eb0    // 0x4EF, 4.6-4.7, DiEye功能开关
#define DiSus_Customize_Height_Set_S                                      0x219d0005583a1eb8    // 0x4EF, 2.0-2.2, DiSus自定义高度调节
#define DiSus_Customize_Combine_Height_Set_Enable_S                       0x219d8005583a1ec0    // 0x4EF, 2.6-2.7, DiSus自定义组合高度调节使能信号
#define DiSus_Customize_Single_Height_Set_Enable_S                        0x219e0005583a1ec8    // 0x4EF, 3.0-3.1, DiSus自定义单轮高度调节使能信号
#define DiSus_Customize_Combine_Height_Set_S                              0x219e8005583a1ed0    // 0x4EF, 3.2-3.7, DiSus自定义组合高度调节信号
#define Height_Control_Off_S                                              0x219f0005583a1ed8    // 0x4EF, 4.0-4.1, 高度控制OFF开关
#define DiSus_Maintance_Switch_S                                          0x219f8005583a1ee0    // 0x4EF, 4.2-4.3, 千斤顶/举升机开关
#define DiSus_Welcome_Switch_S                                            0x21a00005583a1ee8    // 0x4EF, 4.4-4.5, 迎宾功能信号
#define DiSus_Balance_Switch_S                                            0x21a08005583a1ef0    // 0x4EF, 4.6-4.7, 露营调平功能开关
#define DiSus_Front_Suitcase_Switch_S                                     0x21a10005583a1ef8    // 0x4EF, 5.0-5.1, 前行李箱取物开关
#define DiSus_Rear_Suitcase_Switch_S                                      0x21a18005583a1f00    // 0x4EF, 5.2-5.3, 后行李箱取物开关
#define DiSus_Height_Adjust_Voice_S                                       0x21a20005583a1f08    // 0x4EF, 5.4-5.5, 语音高度调节
#define Media_DiDyna_CVC_Work_Switch                                      0x21a28005583a1f10    // 0x4EF, 5.6-5.7, PAD上的CVC开关
#define CCT_Fun_Switch_PAD_S                                              0x21a30005583a1f18    // 0x4EF, 6.4-6.5, 舒适控制技术PAD开关
#define Media_CVC_Work_Mode_S                                             0x21a38005583a1f20    // 0x4EF, 6.6-6.7, 底盘矢量控制开关
#define Extra_Hi_Active_Status                                            0x21a40005583a1f28    // 0x4EF, 7.4-7.5, 超高激活状态
#define Extra_Low_Active_Status                                           0x21a48005583a1f30    // 0x4EF, 7.6-7.7, 超低激活状态
#define Media_DiSteer_A_Work_Mode_S                                       0x21a50005583c1f38    // 0x4EF, 2.0-2.1, PAD后轮转向控制开关信号
#define Media_DiSteer_A_CrabWalk_Mode_S                                   0x21a58005583c1f40    // 0x4EF, 2.2-2.3, PAD蟹行模式控制开关信号
#define FR_Safty_Blts_Remind_4FA_S                                        0x01a6000560001f48    // 0x4FA, 2.6-2.7, 副驾安全带未系提醒状态
#define RR_Sec_Safty_Blts_Remind_4FA_S                                    0x01a6800560001f50    // 0x4FA, 3.4-3.5, 右后二排安全带未系提醒状态
#define MR_Sec_Safty_Blts_Remind_4FA_S                                    0x01a7000560001f58    // 0x4FA, 3.2-3.3, 中后二排安全带未系提醒状态
#define RL_Sec_Safty_Blts_Remind_4FA_S                                    0x01a7800560001f60    // 0x4FA, 3.0-3.1, 左后二排安全带未系提醒状态
#define Suspension_Motor_ActualSpeed_S                                    0x01a8000570001f68    // 0x666, 1.0-2.4, 五行泵电机实时转速
#define Suspension_Motor_ActualSpeedVal_S                                 0x01a8800570001f70    // 0x666, 2.5, 五行泵电机实时转速有效位
#define Suspension_Motor_OverCur_S                                        0x01a9000570001f78    // 0x666, 2.6, 五行泵故障
#define Suspension_Motor_OverVol_S                                        0x01a9800570001f80    // 0x666, 2.7, 五行泵故障
#define Suspension_Motor_UnderVol_S                                       0x01aa000570001f88    // 0x666, 3.0, 五行泵故障
#define Suspension_Motor_OverTem_S                                        0x01aa800570001f90    // 0x666, 3.1, 五行泵故障
#define Suspension_Motor_Locked_S                                         0x01ab000570001f98    // 0x666, 3.2, 五行泵故障
#define Suspension_Motor_Stalled_S                                        0x01ab800570001fa0    // 0x666, 3.3, 五行泵故障
#define Suspension_Motor_McuOverTem_S                                     0x01ac000570001fa8    // 0x666, 3.4, 五行泵故障
#define Suspension_Motor_McuPowerFail_S                                   0x01ac800570001fb0    // 0x666, 3.5, 五行泵故障
#define Suspension_Motor_DriveOpen_S                                      0x01ad000570001fb8    // 0x666, 3.6, 五行泵故障
#define Suspension_Motor_Drive2Power_S                                    0x01ad800570001fc0    // 0x666, 3.7, 五行泵故障
#define Suspension_Motor_Drive2GDN_S                                      0x01ae000570001fc8    // 0x666, 4.0, 五行泵故障
#define Suspension_Motor_PessSigVal_S                                     0x01ae800570001fd0    // 0x666, 4.1, 五行泵故障
#define Suspension_Motor_Pess2Power_S                                     0x01af000570001fd8    // 0x666, 4.2, 五行泵故障
#define Suspension_Motor_Pess2GNDVal_S                                    0x01af800570001fe0    // 0x666, 4.3, 五行泵故障
#define Suspension_Motor_TempSigVal_S                                     0x01b0000570001fe8    // 0x666, 4.4, 五行泵故障
#define Suspension_Motor_Temp2Power_S                                     0x01b0800570001ff0    // 0x666, 4.5, 五行泵故障
#define Suspension_Motor_Temp2GNDVal_S                                    0x01b1000570001ff8    // 0x666, 4.6, 五行泵故障
#define Suspension_Motor_ActualCur_S                                      0x01b1800570002000    // 0x666, 4.7-5.5, 五行泵电机实时电流
#define Suspension_Motor_ActualCurVal_S                                   0x01b2000570002008    // 0x666, 5.6, 五行泵电机实时电流有效位
#define Suspension_Motor_ActualPres_S                                     0x01b2da4570002010    // 0x666, 5.7-7.2, 五行泵电机压力
#define Suspension_Motor_ActualPresVal_S                                  0x01b3000570002018    // 0x666, 7.3, 五行泵电机压力有效位
#define Rolling_Counter_411_S                                             0x01b3800570002020    // 0x666, 7.4-7.7, 计数器
#define Suspension_Motor_ActualTem_S                                      0x01b45a5580002028    // 0x667, 1.0-1.7, 五行泵电机温度
#define Suspension_Motor_PowerVol_S                                       0x01b4800580002030    // 0x667, 2.0-2.7, 五行泵电机供电电压
#define Suspension_Motor_ActualTemVal_S                                   0x01b5000580002038    // 0x667, 3.0, 五行泵电机温度有效位
#define Suspension_Motor_OverCurHis_S                                     0x01b5800580002040    // 0x667, 3.1, 五行泵历史故障
#define Suspension_Motor_OverVolHis_S                                     0x01b6000580002048    // 0x667, 3.2, 五行泵历史故障
#define Suspension_Motor_UnderVolHis_S                                    0x01b6800580002050    // 0x667, 3.3, 五行泵历史故障
#define Suspension_Motor_OverTemHis_S                                     0x01b7000580002058    // 0x667, 3.4, 五行泵历史故障
#define Suspension_Motor_LockedHis_S                                      0x01b7800580002060    // 0x667, 3.5, 五行泵历史故障
#define Suspension_Motor_StalledHis_S                                     0x01b8000580002068    // 0x667, 3.6, 五行泵历史故障
#define Suspension_Motor_McuOverTemHis_S                                  0x01b8800580002070    // 0x667, 3.7, 五行泵历史故障
#define Suspension_Motor_McuPowerFailHis_S                                0x01b9000580002078    // 0x667, 4.0, 五行泵历史故障
#define Suspension_Motor_DriveOpenHis_S                                   0x01b9800580002080    // 0x667, 4.1, 五行泵历史故障
#define Suspension_Motor_Drive2PowerHis_S                                 0x01ba000580002088    // 0x667, 4.2, 五行泵历史故障
#define Suspension_Motor_Drive2GNDHis_S                                   0x01ba800580002090    // 0x667, 4.3, 五行泵历史故障
#define Suspension_Motor_PessSigValHis_S                                  0x01bb000580002098    // 0x667, 4.4, 五行泵历史故障
#define Suspension_Motor_Pess2PowerHis_S                                  0x01bb8005800020a0    // 0x667, 4.5, 五行泵历史故障
#define Suspension_Motor_Pess2GNDValHis_S                                 0x01bc0005800020a8    // 0x667, 4.6, 五行泵历史故障
#define Suspension_Motor_TempSigValHis_S                                  0x01bc8005800020b0    // 0x667, 4.7, 五行泵历史故障
#define Suspension_Motor_Temp2PowerHis_S                                  0x01bd0005800020b8    // 0x667, 5.0, 五行泵历史故障
#define Suspension_Motor_Temp2GNDValHis_S                                 0x01bd8005800020c0    // 0x667, 5.1, 五行泵历史故障
#define Rolling_Counter_412_S                                             0x01be0005800020c8    // 0x667, 7.4-7.7, 计数器

// IO信号内容
// 测试服务
#define T1_TEST                                                           0x3000000000000000    // Port: 182, T1测试信号

#endif
