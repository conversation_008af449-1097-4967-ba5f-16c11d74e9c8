﻿#include "VSE_CanMRx.h"


static VSE_CAN_SIGNAL_MAP_t CAN_SIGNAL_MAP_CYCLE[CAN_SIG_CYCLE_NUM] =
{

    // HY: 0x051
	{VSE_SRS, (uint64_T)ACU_LongitudeAcceleration_S, &VSE_RTE_SIG_u16IMUAx, VSE_TYPE_UINT16, VSE_INVALID_IMU_A, 0u},
	{VSE_SRS, (uint64_T)ACU_LateralAcceleration_S, &VSE_RTE_SIG_u16IMUAy, VSE_TYPE_UINT16, VSE_INVALID_IMU_A, 0u},
	{VSE_SRS, (uint64_T)ACU_VerticalAcceleration_S, &VSE_RTE_SIG_u16IMUAz, VSE_TYPE_UINT16, VSE_INVALID_IMU_A, 0u},
	{VSE_SRS, (uint64_T)ACU_Wx_RollRate_S, &VSE_RTE_SIG_u16IMUWx, VSE_TYPE_UINT16, VSE_INVALID_IMU_W, 0u},
	{VSE_SRS, (uint64_T)ACU_Wy_PitchRate_S, &VSE_RTE_SIG_u16IMUWy, VSE_TYPE_UINT16, VSE_INVALID_IMU_W, 0u},
	{VSE_SRS, (uint64_T)ACU_YawRate_S, &VSE_RTE_SIG_u16IMUWz, VSE_TYPE_UINT16, VSE_INVALID_IMU_W, 0u},
	{VSE_SRS, (uint64_T)ACU_LongitudeAccelerationSensor_St_S, &VSE_RTE_SIG_bIMUAxSt, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},
	{VSE_SRS, (uint64_T)ACU_LateralAccelerationSensor_St_S, &VSE_RTE_SIG_bIMUAySt, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},
	{VSE_SRS, (uint64_T)ACU_VerticalAccelerationSensor_St_S, &VSE_RTE_SIG_bIMUAzSt, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},
	{VSE_SRS, (uint64_T)ACU_Wx_RollRateSensor_St_S, &VSE_RTE_SIG_bIMUWxSt, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},
	{VSE_SRS, (uint64_T)ACU_Wy_PitchRateSensor_St_S, &VSE_RTE_SIG_bIMUWySt, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},
	{VSE_SRS, (uint64_T)ACU_YawRateSensor_St_S, &VSE_RTE_SIG_bIMUWzSt, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},

	// 0x0FC
	{VSE_IPB, (uint64_T)Vehicle_Torque_FL_0FC_S, &VSE_RTE_SIG_i16VehTqFL, VSE_TYPE_INT16, VSE_INVALID_ZERO, 0u},
	{VSE_IPB, (uint64_T)Vehicle_Torque_FR_0FC_S, &VSE_RTE_SIG_i16VehTqFR, VSE_TYPE_INT16, VSE_INVALID_ZERO, 0u},
	{VSE_IPB, (uint64_T)Vehicle_Torque_RL_0FC_S, &VSE_RTE_SIG_i16VehTqRL, VSE_TYPE_INT16, VSE_INVALID_ZERO, 0u},
	{VSE_IPB, (uint64_T)Vehicle_Torque_RR_0FC_S, &VSE_RTE_SIG_i16VehTqRR, VSE_TYPE_INT16, VSE_INVALID_ZERO, 0u},
	{VSE_IPB, (uint64_T)Torque_State_FL_0FC_S, &VSE_RTE_SIG_bTqStsFL, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},
	{VSE_IPB, (uint64_T)Torque_State_FR_0FC_S, &VSE_RTE_SIG_bTqStsFR, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},
	{VSE_IPB, (uint64_T)Torque_State_RL_0FC_S, &VSE_RTE_SIG_bTqStsRL, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},
	{VSE_IPB, (uint64_T)Torque_State_RR_0FC_S, &VSE_RTE_SIG_bTqStsRR, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},

    // 0x342
	{VSE_IPB, (uint64_T)Actual_Throttle_Depth_S, &VSE_RTE_SIG_u8AccrPedlRate, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},
    {VSE_VCU, (uint64_T)Actual_Throttle_Depth_Effective_Sign_S, &VSE_RTE_SIG_bAccrPedlRateFlg, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},
	{VSE_IPB, (uint64_T)Veh_drv_sty, &VSE_RTE_SIG_u8VehDrvMod, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},
    
    // 0x122
	{VSE_IPB, (uint64_T)WheelSpeed_FL_122_S, &VSE_RTE_SIG_u16FLWhlSpd, VSE_TYPE_UINT16, VSE_INVALID_ZERO, 0u},
	{VSE_IPB, (uint64_T)WheelSpeed_FR_122_S, &VSE_RTE_SIG_u16FRWhlSpd, VSE_TYPE_UINT16, VSE_INVALID_ZERO, 0u},
	{VSE_IPB, (uint64_T)WheelSpeed_RL_122_S, &VSE_RTE_SIG_u16RLWhlSpd, VSE_TYPE_UINT16, VSE_INVALID_ZERO, 0u},
	{VSE_IPB, (uint64_T)WheelSpeed_RR_122_S, &VSE_RTE_SIG_u16RRWhlSpd, VSE_TYPE_UINT16, VSE_INVALID_ZERO, 0u},
	{VSE_IPB, (uint64_T)WheelSpeed_FL_Status_122_S, &VSE_RTE_SIG_bWhlSpdFLSt, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},
    {VSE_IPB, (uint64_T)WheelSpeed_FR_Status_122_S, &VSE_RTE_SIG_bWhlSpdFRSt, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},
	{VSE_IPB, (uint64_T)WheelSpeed_RL_Status_122_S, &VSE_RTE_SIG_bWhlSpdRLSt, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},
    {VSE_IPB, (uint64_T)WheelSpeed_RR_Status_122_S, &VSE_RTE_SIG_bWhlSpdRRSt, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},
	
    // 0x321
	{VSE_IPB, (uint64_T)IPB_PlungerPressure_Status_321_S, &VSE_RTE_SIG_bIPBPluPreSts, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},
	{VSE_IPB, (uint64_T)IPB_Plunger_Pressure_321_S, &VSE_RTE_SIG_u16IPBPPrs, VSE_TYPE_UINT16, VSE_INVALID_ZERO, 0u},

    // 0x123 
	{VSE_IPB, (uint64_T)IPB_BRAKE_PEDAL_STATUS, &VSE_RTE_SIG_u8IPBBrkSts, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},
	
    //0x342
    {VSE_IPB, (uint64_T)VCU_Brake_Depth_S, &VSE_RTE_SIG_u8BrkDepth, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},
	{VSE_IPB, (uint64_T)VCU_Brake_Depth_Virtual_Value_S, &VSE_RTE_SIG_bBrkDepthSts, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},

    // 0x11F
	{VSE_IPB, (uint64_T)Steering_Wheel_Angle_S, &VSE_RTE_SIG_i16SteerAg, VSE_TYPE_INT16, VSE_INVALID_ZERO, 0u},
	{VSE_IPB, (uint64_T)Steering_Wheel_Rotation_Speed_S, &VSE_RTE_SIG_u16SteerAgSpd, VSE_TYPE_UINT16, VSE_INVALID_ZERO, 0u},
	{VSE_EPS, (uint64_T)Failure_Stats_OK_S, &VSE_RTE_SIG_bSteerAgSnsSt, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0U},
    {VSE_IPB, (uint64_T)Sensor_Calibration_Stats_S, &VSE_RTE_SIG_bSteerAgCASnsSt, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},

    // 0x12D
	{VSE_IPB, (uint64_T)BCMPower_Gear_12D_S, &VSE_RTE_SIG_u8PwrGear, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},

    // 0x218
	{VSE_IPB, (uint64_T)EPB_Status, &VSE_RTE_SIG_u8EPBSt, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},

    //0x122
    {VSE_IPB, (uint64_T)TCS_Active_122_S, &VSE_RTE_SIG_bTCSActS, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},

    //0x123
    {VSE_IPB, (uint64_T)TCS_Fault_123_S, &VSE_RTE_SIG_bTCSFlt, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},
	

    // 0x242  Gear_Position Gear_Status
    {VSE_VCU, (uint64_T)Rnk, &VSE_RTE_SIG_u8GearPosn, VSE_TYPE_UINT8, VSE_INVALID_NULL, 0U},
    {VSE_VCU, (uint64_T)Rnk_efc_flg, &VSE_RTE_SIG_bGearSts, VSE_TYPE_UINT8, VSE_INVALID_NULL, 0U},

    // 0x241
    {VSE_VCU, (uint64_T)Vehicle_Torque_FL_241_S, &VSE_RTE_SIG_u16FrntMotTq, VSE_TYPE_UINT16, VSE_INVALID_ZERO, 0U},
	{VSE_VCU, (uint64_T)Torque_State_FL_241_S, &VSE_RTE_SIG_bFrntMotTqSts, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},

    // 0x251
	{VSE_VCU, (uint64_T)Torque_State_RL_251_S, &VSE_RTE_SIG_bReMotTqSts, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},
    {VSE_VCU, (uint64_T)Vehicle_Torque_RL_251_S, &VSE_RTE_SIG_u16ReMotTq, VSE_TYPE_UINT16, VSE_INVALID_NULL, 0U},

    //0x122
    {VSE_IPB, (uint64_T)ABS_Active_122_S, &VSE_RTE_SIG_bABSActS, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},
	{VSE_IPB, (uint64_T)ABS_Fault_122_S, &VSE_RTE_SIG_bABSFlt, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},
	
    //0x123
    {VSE_IPB, (uint64_T)VDC_Fault_123_S, &VSE_RTE_SIG_bVDCFlt, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},

    // 0x222
	{VSE_IPB, (uint64_T)VDC_Active_222_S, &VSE_RTE_SIG_bVDCActS, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},

    // 0x112
	// {VSE_IB, (uint64_T)EPSA_Angle_S, &VSE_RTE_SIG_u16RWhlSteerAg, VSE_TYPE_UINT16, VSE_INVALID_ZERO, 0u},
	// {VSE_IPBP, (uint64_T)EPSA_Angle_VD_S, &VSE_RTE_SIG_bRWhlSteerAgSts, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},


	// 0x109
	{VSE_IPB, (uint64_T)DiSus_Actual_Height_FL_S, &VSE_RTE_SIG_u16DamprPosnFL, VSE_TYPE_UINT16, VSE_INVALID_ZERO, 0u},
	{VSE_IPB, (uint64_T)DiSus_Actual_Height_FR_S, &VSE_RTE_SIG_u16DamprPosnFR, VSE_TYPE_UINT16, VSE_INVALID_ZERO, 0u},
	{VSE_IPB, (uint64_T)DiSus_Actual_Height_RL_S, &VSE_RTE_SIG_u16DamprPosnRL, VSE_TYPE_UINT16, VSE_INVALID_ZERO, 0u},
	{VSE_IPB, (uint64_T)DiSus_Actual_Height_RR_S, &VSE_RTE_SIG_u16DamprPosnRR, VSE_TYPE_UINT16, VSE_INVALID_ZERO, 0u},
	{VSE_IPB, (uint64_T)DiSus_Actual_Height_Invalid_FL_S, &VSE_RTE_SIG_bDamprPosnFLSts, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},
    {VSE_IPB, (uint64_T)DiSus_Actual_Height_Invalid_FR_S, &VSE_RTE_SIG_bDamprPosnFRSts, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},
	{VSE_IPB, (uint64_T)DiSus_Actual_Height_Invalid_RL_S, &VSE_RTE_SIG_bDamprPosnRLSts, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},
	{VSE_IPB, (uint64_T)DiSus_Actual_Height_Invalid_RR_S, &VSE_RTE_SIG_bDamprPosnRRSts, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},
   
    // //以下信号HCEM没有，预留
    // {VSE_IPB, (uint64_T)DiSus_Mode_Execute_Status_S, &VSE_RTE_SIG_u8DiSusModExeSts, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},
    // #if (CAN_PROTOCOL_TPYE == CANFD_TYPE)
    // {VSE_IPB, (uint64_T)DiSus_Type_S, &VSE_RTE_SIG_u8DiSus_Type, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},
    // #else
    // {VSE_IPB, (uint64_T)DiSus_Type_1A7_S, &VSE_RTE_SIG_u8DiSus_Type, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},
    // #endif
  
    // 0x258
    // {VSE_SRS, (uint64_T)DiSus_Height_Adjust_Status_S, &VSE_RTE_SIG_bDiSusHeiAdjSts, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},
    // {VSE_SRS, (uint64_T)DiSus_Height_Adjust_Process_S, &VSE_RTE_SIG_u8DiSusHeiAdjProc, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},
    // {VSE_SRS, (uint64_T)DiSus_Height_Adjust_Fault_S, &VSE_RTE_SIG_bDiSusHeiAdjFltSts, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},

    
    /*
    {VSE_IPB, (uint64_T)FL_Wheel_Bra_Active_State_S, &VSE_RTE_SIG_u8FLWhlBraSts, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},
    {VSE_IPB, (uint64_T)FR_Wheel_Bra_Active_State_S, &VSE_RTE_SIG_u8FRWhlBraSts, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},
    {VSE_IPB, (uint64_T)RL_Wheel_Bra_Active_State_S, &VSE_RTE_SIG_u8RLWhlBraSts, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},
    {VSE_IPB, (uint64_T)RR_Wheel_Bra_Active_State_S, &VSE_RTE_SIG_u8RRWhlBraSts, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},
    */
    
    //0x0da HY:来自0x0da，非116
    // {VSE_IPB, (uint64_T)FL_Wheel_Bra_Active_State_S, &VSE_RTE_SIG_u8FLWhlBraSts, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},
    // {VSE_IPB, (uint64_T)FR_Wheel_Bra_Active_State_S, &VSE_RTE_SIG_u8FRWhlBraSts, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},
    // {VSE_IPB, (uint64_T)RL_Wheel_Bra_Active_State_S, &VSE_RTE_SIG_u8RLWhlBraSts, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},
    // {VSE_IPB, (uint64_T)RR_Wheel_Bra_Active_State_S, &VSE_RTE_SIG_u8RRWhlBraSts, VSE_TYPE_UINT8, VSE_INVALID_ZERO, 0u},
    
    //0x116
    {VSE_IPB, (uint64_T)FL_brake_tq_execution_S, &VSE_RTE_SIG_i16FLBrkTqExecu, VSE_TYPE_INT16, VSE_INVALID_ZERO, 0u},
    {VSE_IPB, (uint64_T)FR_brake_tq_execution_S, &VSE_RTE_SIG_i16FRBrkTqExecu, VSE_TYPE_INT16, VSE_INVALID_ZERO, 0u},
    {VSE_IPB, (uint64_T)RL_brake_tq_execution_S, &VSE_RTE_SIG_i16RLBrkTqExecu, VSE_TYPE_INT16, VSE_INVALID_ZERO, 0u},
    {VSE_IPB, (uint64_T)RR_brake_tq_execution_S, &VSE_RTE_SIG_i16RRBrkTqExecu, VSE_TYPE_INT16, VSE_INVALID_ZERO, 0u},
    
    

};

static VSE_SIG_SRC_MAP_t VSE_SigSrcMap[VSE_SIG_IDX_NUM];
/*********************************************************
 * Function Name: VSE_SigSrcMapInit
 * Description  : 建立VSE输入信号的索引至信号值的映射关系
 * Parameter    : null
 * return       : null
 *********************************************************/
void VSE_SigSrcMapInit(void)
{
    VSE_SigSrcMap[VSE_SIG_IDX_u16IMUAx].pCANSigValue=&VSE_RTE_SIG_u16IMUAx;         VSE_SigSrcMap[VSE_SIG_IDX_u16IMUAx].emSigSrcType=VSE_SIGNAL_SOURCE_u16IMUAx;       VSE_SigSrcMap[VSE_SIG_IDX_u16IMUAx].pSigFltInjVal=&VSE_Inj_Dft_u16IMUAx;     VSE_SigSrcMap[VSE_SIG_IDX_u16IMUAx].bSigFltInjFlg=VSE_Inj_Flg_u16IMUAx;     VSE_SigSrcMap[VSE_SIG_IDX_u16IMUAx].emSigDataType = VSE_TYPE_UINT16;
    VSE_SigSrcMap[VSE_SIG_IDX_u16IMUAy].pCANSigValue=&VSE_RTE_SIG_u16IMUAy;         VSE_SigSrcMap[VSE_SIG_IDX_u16IMUAy].emSigSrcType=VSE_SIGNAL_SOURCE_u16IMUAy;       VSE_SigSrcMap[VSE_SIG_IDX_u16IMUAy].pSigFltInjVal=&VSE_Inj_Dft_u16IMUAy;     VSE_SigSrcMap[VSE_SIG_IDX_u16IMUAy].bSigFltInjFlg=VSE_Inj_Flg_u16IMUAy;     VSE_SigSrcMap[VSE_SIG_IDX_u16IMUAy].emSigDataType = VSE_TYPE_UINT16;
    VSE_SigSrcMap[VSE_SIG_IDX_u16IMUAz].pCANSigValue=&VSE_RTE_SIG_u16IMUAz;         VSE_SigSrcMap[VSE_SIG_IDX_u16IMUAz].emSigSrcType=VSE_SIGNAL_SOURCE_u16IMUAz;        VSE_SigSrcMap[VSE_SIG_IDX_u16IMUAz].pSigFltInjVal=&VSE_Inj_Dft_u16IMUAz;     VSE_SigSrcMap[VSE_SIG_IDX_u16IMUAz].bSigFltInjFlg=VSE_Inj_Flg_u16IMUAz;     VSE_SigSrcMap[VSE_SIG_IDX_u16IMUAz].emSigDataType = VSE_TYPE_UINT16;
    VSE_SigSrcMap[VSE_SIG_IDX_u16IMUWx].pCANSigValue=&VSE_RTE_SIG_u16IMUWx;         VSE_SigSrcMap[VSE_SIG_IDX_u16IMUWx].emSigSrcType=VSE_SIGNAL_SOURCE_u16IMUWx;        VSE_SigSrcMap[VSE_SIG_IDX_u16IMUWx].pSigFltInjVal=&VSE_Inj_Dft_u16IMUWx;     VSE_SigSrcMap[VSE_SIG_IDX_u16IMUWx].bSigFltInjFlg=VSE_Inj_Flg_u16IMUWx;     VSE_SigSrcMap[VSE_SIG_IDX_u16IMUWx].emSigDataType = VSE_TYPE_UINT16;
    VSE_SigSrcMap[VSE_SIG_IDX_u16IMUWy].pCANSigValue=&VSE_RTE_SIG_u16IMUWy;         VSE_SigSrcMap[VSE_SIG_IDX_u16IMUWy].emSigSrcType=VSE_SIGNAL_SOURCE_u16IMUWy;        VSE_SigSrcMap[VSE_SIG_IDX_u16IMUWy].pSigFltInjVal=&VSE_Inj_Dft_u16IMUWy;     VSE_SigSrcMap[VSE_SIG_IDX_u16IMUWy].bSigFltInjFlg=VSE_Inj_Flg_u16IMUWy;     VSE_SigSrcMap[VSE_SIG_IDX_u16IMUWy].emSigDataType = VSE_TYPE_UINT16;
    VSE_SigSrcMap[VSE_SIG_IDX_u16IMUWz].pCANSigValue=&VSE_RTE_SIG_u16IMUWz;         VSE_SigSrcMap[VSE_SIG_IDX_u16IMUWz].emSigSrcType=VSE_SIGNAL_SOURCE_u16IMUWz;        VSE_SigSrcMap[VSE_SIG_IDX_u16IMUWz].pSigFltInjVal=&VSE_Inj_Dft_u16IMUWz;     VSE_SigSrcMap[VSE_SIG_IDX_u16IMUWz].bSigFltInjFlg=VSE_Inj_Flg_u16IMUWz;     VSE_SigSrcMap[VSE_SIG_IDX_u16IMUWz].emSigDataType = VSE_TYPE_UINT16;
    VSE_SigSrcMap[VSE_SIG_IDX_bIMUAxSt].pCANSigValue=&VSE_RTE_SIG_bIMUAxSt;         VSE_SigSrcMap[VSE_SIG_IDX_bIMUAxSt].emSigSrcType=VSE_SIGNAL_SOURCE_bIMUAxSt;        VSE_SigSrcMap[VSE_SIG_IDX_bIMUAxSt].pSigFltInjVal=&VSE_Inj_Dft_bIMUAxSt;     VSE_SigSrcMap[VSE_SIG_IDX_bIMUAxSt].bSigFltInjFlg=VSE_Inj_Flg_bIMUAxSt;     VSE_SigSrcMap[VSE_SIG_IDX_bIMUAxSt].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_bIMUAySt].pCANSigValue=&VSE_RTE_SIG_bIMUAySt;         VSE_SigSrcMap[VSE_SIG_IDX_bIMUAySt].emSigSrcType=VSE_SIGNAL_SOURCE_bIMUAySt;        VSE_SigSrcMap[VSE_SIG_IDX_bIMUAySt].pSigFltInjVal=&VSE_Inj_Dft_bIMUAySt;     VSE_SigSrcMap[VSE_SIG_IDX_bIMUAySt].bSigFltInjFlg=VSE_Inj_Flg_bIMUAySt;     VSE_SigSrcMap[VSE_SIG_IDX_bIMUAySt].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_bIMUAzSt].pCANSigValue=&VSE_RTE_SIG_bIMUAzSt;         VSE_SigSrcMap[VSE_SIG_IDX_bIMUAzSt].emSigSrcType=VSE_SIGNAL_SOURCE_bIMUAzSt;        VSE_SigSrcMap[VSE_SIG_IDX_bIMUAzSt].pSigFltInjVal=&VSE_Inj_Dft_bIMUAzSt;     VSE_SigSrcMap[VSE_SIG_IDX_bIMUAzSt].bSigFltInjFlg=VSE_Inj_Flg_bIMUAzSt;     VSE_SigSrcMap[VSE_SIG_IDX_bIMUAzSt].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_bIMUWxSt].pCANSigValue=&VSE_RTE_SIG_bIMUWxSt;         VSE_SigSrcMap[VSE_SIG_IDX_bIMUWxSt].emSigSrcType=VSE_SIGNAL_SOURCE_bIMUWxSt;        VSE_SigSrcMap[VSE_SIG_IDX_bIMUWxSt].pSigFltInjVal=&VSE_Inj_Dft_bIMUWxSt;     VSE_SigSrcMap[VSE_SIG_IDX_bIMUWxSt].bSigFltInjFlg=VSE_Inj_Flg_bIMUWxSt;     VSE_SigSrcMap[VSE_SIG_IDX_bIMUWxSt].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_bIMUWySt].pCANSigValue=&VSE_RTE_SIG_bIMUWySt;         VSE_SigSrcMap[VSE_SIG_IDX_bIMUWySt].emSigSrcType=VSE_SIGNAL_SOURCE_bIMUWySt;        VSE_SigSrcMap[VSE_SIG_IDX_bIMUWySt].pSigFltInjVal=&VSE_Inj_Dft_bIMUWySt;     VSE_SigSrcMap[VSE_SIG_IDX_bIMUWySt].bSigFltInjFlg=VSE_Inj_Flg_bIMUWySt;     VSE_SigSrcMap[VSE_SIG_IDX_bIMUWySt].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_bIMUWzSt].pCANSigValue=&VSE_RTE_SIG_bIMUWzSt;         VSE_SigSrcMap[VSE_SIG_IDX_bIMUWzSt].emSigSrcType=VSE_SIGNAL_SOURCE_bIMUWzSt;        VSE_SigSrcMap[VSE_SIG_IDX_bIMUWzSt].pSigFltInjVal=&VSE_Inj_Dft_bIMUWzSt;     VSE_SigSrcMap[VSE_SIG_IDX_bIMUWzSt].bSigFltInjFlg=VSE_Inj_Flg_bIMUWzSt;     VSE_SigSrcMap[VSE_SIG_IDX_bIMUWzSt].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_i16VehTqFL].pCANSigValue=&VSE_RTE_SIG_i16VehTqFL;         VSE_SigSrcMap[VSE_SIG_IDX_i16VehTqFL].emSigSrcType=VSE_SIGNAL_SOURCE_i16VehTqFL;        VSE_SigSrcMap[VSE_SIG_IDX_i16VehTqFL].pSigFltInjVal=&VSE_Inj_Dft_i16VehTqFL;       VSE_SigSrcMap[VSE_SIG_IDX_i16VehTqFL].bSigFltInjFlg=VSE_Inj_Flg_i16VehTqFL;       VSE_SigSrcMap[VSE_SIG_IDX_i16VehTqFL].emSigDataType = VSE_TYPE_INT16;
    VSE_SigSrcMap[VSE_SIG_IDX_i16VehTqFR].pCANSigValue=&VSE_RTE_SIG_i16VehTqFR;         VSE_SigSrcMap[VSE_SIG_IDX_i16VehTqFR].emSigSrcType=VSE_SIGNAL_SOURCE_i16VehTqFR;        VSE_SigSrcMap[VSE_SIG_IDX_i16VehTqFR].pSigFltInjVal=&VSE_Inj_Dft_i16VehTqFR;       VSE_SigSrcMap[VSE_SIG_IDX_i16VehTqFR].bSigFltInjFlg=VSE_Inj_Flg_i16VehTqFR;       VSE_SigSrcMap[VSE_SIG_IDX_i16VehTqFR].emSigDataType = VSE_TYPE_INT16;
    VSE_SigSrcMap[VSE_SIG_IDX_i16VehTqRL].pCANSigValue=&VSE_RTE_SIG_i16VehTqRL;         VSE_SigSrcMap[VSE_SIG_IDX_i16VehTqRL].emSigSrcType=VSE_SIGNAL_SOURCE_i16VehTqRL;        VSE_SigSrcMap[VSE_SIG_IDX_i16VehTqRL].pSigFltInjVal=&VSE_Inj_Dft_i16VehTqRL;       VSE_SigSrcMap[VSE_SIG_IDX_i16VehTqRL].bSigFltInjFlg=VSE_Inj_Flg_i16VehTqRL;       VSE_SigSrcMap[VSE_SIG_IDX_i16VehTqRL].emSigDataType = VSE_TYPE_INT16;
    VSE_SigSrcMap[VSE_SIG_IDX_i16VehTqRR].pCANSigValue=&VSE_RTE_SIG_i16VehTqRR;         VSE_SigSrcMap[VSE_SIG_IDX_i16VehTqRR].emSigSrcType=VSE_SIGNAL_SOURCE_i16VehTqRR;        VSE_SigSrcMap[VSE_SIG_IDX_i16VehTqRR].pSigFltInjVal=&VSE_Inj_Dft_i16VehTqRR;       VSE_SigSrcMap[VSE_SIG_IDX_i16VehTqRR].bSigFltInjFlg=VSE_Inj_Flg_i16VehTqRR;       VSE_SigSrcMap[VSE_SIG_IDX_i16VehTqRR].emSigDataType = VSE_TYPE_INT16;
    VSE_SigSrcMap[VSE_SIG_IDX_bTqStsFL].pCANSigValue=&VSE_RTE_SIG_bTqStsFL;         VSE_SigSrcMap[VSE_SIG_IDX_bTqStsFL].emSigSrcType=VSE_SIGNAL_SOURCE_bTqStsFL;        VSE_SigSrcMap[VSE_SIG_IDX_bTqStsFL].pSigFltInjVal=&VSE_Inj_Dft_bTqStsFL;     VSE_SigSrcMap[VSE_SIG_IDX_bTqStsFL].bSigFltInjFlg=VSE_Inj_Flg_bTqStsFL;     VSE_SigSrcMap[VSE_SIG_IDX_bTqStsFL].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_bTqStsFR].pCANSigValue=&VSE_RTE_SIG_bTqStsFR;         VSE_SigSrcMap[VSE_SIG_IDX_bTqStsFR].emSigSrcType=VSE_SIGNAL_SOURCE_bTqStsFR;        VSE_SigSrcMap[VSE_SIG_IDX_bTqStsFR].pSigFltInjVal=&VSE_Inj_Dft_bTqStsFR;     VSE_SigSrcMap[VSE_SIG_IDX_bTqStsFR].bSigFltInjFlg=VSE_Inj_Flg_bTqStsFR;     VSE_SigSrcMap[VSE_SIG_IDX_bTqStsFR].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_bTqStsRL].pCANSigValue=&VSE_RTE_SIG_bTqStsRL;         VSE_SigSrcMap[VSE_SIG_IDX_bTqStsRL].emSigSrcType=VSE_SIGNAL_SOURCE_bTqStsRL;        VSE_SigSrcMap[VSE_SIG_IDX_bTqStsRL].pSigFltInjVal=&VSE_Inj_Dft_bTqStsRL;     VSE_SigSrcMap[VSE_SIG_IDX_bTqStsRL].bSigFltInjFlg=VSE_Inj_Flg_bTqStsRL;     VSE_SigSrcMap[VSE_SIG_IDX_bTqStsRL].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_bTqStsRR].pCANSigValue=&VSE_RTE_SIG_bTqStsRR;         VSE_SigSrcMap[VSE_SIG_IDX_bTqStsRR].emSigSrcType=VSE_SIGNAL_SOURCE_bTqStsRR;        VSE_SigSrcMap[VSE_SIG_IDX_bTqStsRR].pSigFltInjVal=&VSE_Inj_Dft_bTqStsRR;     VSE_SigSrcMap[VSE_SIG_IDX_bTqStsRR].bSigFltInjFlg=VSE_Inj_Flg_bTqStsRR;     VSE_SigSrcMap[VSE_SIG_IDX_bTqStsRR].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_u16FrntMotTq].pCANSigValue=&VSE_RTE_SIG_u16FrntMotTq;         VSE_SigSrcMap[VSE_SIG_IDX_u16FrntMotTq].emSigSrcType=VSE_SIGNAL_SOURCE_u16FrntMotTq;        VSE_SigSrcMap[VSE_SIG_IDX_u16FrntMotTq].pSigFltInjVal=&VSE_Inj_Dft_u16FrntMotTq;     VSE_SigSrcMap[VSE_SIG_IDX_u16FrntMotTq].bSigFltInjFlg=VSE_Inj_Flg_u16FrntMotTq;     VSE_SigSrcMap[VSE_SIG_IDX_u16FrntMotTq].emSigDataType = VSE_TYPE_UINT16;
    VSE_SigSrcMap[VSE_SIG_IDX_u16ReMotTq].pCANSigValue=&VSE_RTE_SIG_u16ReMotTq;         VSE_SigSrcMap[VSE_SIG_IDX_u16ReMotTq].emSigSrcType=VSE_SIGNAL_SOURCE_u16ReMotTq;        VSE_SigSrcMap[VSE_SIG_IDX_u16ReMotTq].pSigFltInjVal=&VSE_Inj_Dft_u16ReMotTq;       VSE_SigSrcMap[VSE_SIG_IDX_u16ReMotTq].bSigFltInjFlg=VSE_Inj_Flg_u16ReMotTq;       VSE_SigSrcMap[VSE_SIG_IDX_u16ReMotTq].emSigDataType = VSE_TYPE_UINT16;
    VSE_SigSrcMap[VSE_SIG_IDX_bFrntMotTqSts].pCANSigValue=&VSE_RTE_SIG_bFrntMotTqSts;         VSE_SigSrcMap[VSE_SIG_IDX_bFrntMotTqSts].emSigSrcType=VSE_SIGNAL_SOURCE_bFrntMotTqSts;        VSE_SigSrcMap[VSE_SIG_IDX_bFrntMotTqSts].pSigFltInjVal=&VSE_Inj_Dft_bFrntMotTqSts;        VSE_SigSrcMap[VSE_SIG_IDX_bFrntMotTqSts].bSigFltInjFlg=VSE_Inj_Flg_bFrntMotTqSts;        VSE_SigSrcMap[VSE_SIG_IDX_bFrntMotTqSts].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_bReMotTqSts].pCANSigValue=&VSE_RTE_SIG_bReMotTqSts;         VSE_SigSrcMap[VSE_SIG_IDX_bReMotTqSts].emSigSrcType=VSE_SIGNAL_SOURCE_bReMotTqSts;        VSE_SigSrcMap[VSE_SIG_IDX_bReMotTqSts].pSigFltInjVal=&VSE_Inj_Dft_bReMotTqSts;      VSE_SigSrcMap[VSE_SIG_IDX_bReMotTqSts].bSigFltInjFlg=VSE_Inj_Flg_bReMotTqSts;      VSE_SigSrcMap[VSE_SIG_IDX_bReMotTqSts].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_u16FLWhlSpd].pCANSigValue=&VSE_RTE_SIG_u16FLWhlSpd;         VSE_SigSrcMap[VSE_SIG_IDX_u16FLWhlSpd].emSigSrcType=VSE_SIGNAL_SOURCE_u16FLWhlSpd;        VSE_SigSrcMap[VSE_SIG_IDX_u16FLWhlSpd].pSigFltInjVal=&VSE_Inj_Dft_u16FLWhlSpd;      VSE_SigSrcMap[VSE_SIG_IDX_u16FLWhlSpd].bSigFltInjFlg=VSE_Inj_Flg_u16FLWhlSpd;      VSE_SigSrcMap[VSE_SIG_IDX_u16FLWhlSpd].emSigDataType = VSE_TYPE_UINT16;
    VSE_SigSrcMap[VSE_SIG_IDX_u16FRWhlSpd].pCANSigValue=&VSE_RTE_SIG_u16FRWhlSpd;         VSE_SigSrcMap[VSE_SIG_IDX_u16FRWhlSpd].emSigSrcType=VSE_SIGNAL_SOURCE_u16FRWhlSpd;        VSE_SigSrcMap[VSE_SIG_IDX_u16FRWhlSpd].pSigFltInjVal=&VSE_Inj_Dft_u16FRWhlSpd;      VSE_SigSrcMap[VSE_SIG_IDX_u16FRWhlSpd].bSigFltInjFlg=VSE_Inj_Flg_u16FRWhlSpd;      VSE_SigSrcMap[VSE_SIG_IDX_u16FRWhlSpd].emSigDataType = VSE_TYPE_UINT16;
    VSE_SigSrcMap[VSE_SIG_IDX_u16RLWhlSpd].pCANSigValue=&VSE_RTE_SIG_u16RLWhlSpd;         VSE_SigSrcMap[VSE_SIG_IDX_u16RLWhlSpd].emSigSrcType=VSE_SIGNAL_SOURCE_u16RLWhlSpd;        VSE_SigSrcMap[VSE_SIG_IDX_u16RLWhlSpd].pSigFltInjVal=&VSE_Inj_Dft_u16RLWhlSpd;      VSE_SigSrcMap[VSE_SIG_IDX_u16RLWhlSpd].bSigFltInjFlg=VSE_Inj_Flg_u16RLWhlSpd;      VSE_SigSrcMap[VSE_SIG_IDX_u16RLWhlSpd].emSigDataType = VSE_TYPE_UINT16;
    VSE_SigSrcMap[VSE_SIG_IDX_u16RRWhlSpd].pCANSigValue=&VSE_RTE_SIG_u16RRWhlSpd;         VSE_SigSrcMap[VSE_SIG_IDX_u16RRWhlSpd].emSigSrcType=VSE_SIGNAL_SOURCE_u16RRWhlSpd;        VSE_SigSrcMap[VSE_SIG_IDX_u16RRWhlSpd].pSigFltInjVal=&VSE_Inj_Dft_u16RRWhlSpd;      VSE_SigSrcMap[VSE_SIG_IDX_u16RRWhlSpd].bSigFltInjFlg=VSE_Inj_Flg_u16RRWhlSpd;      VSE_SigSrcMap[VSE_SIG_IDX_u16RRWhlSpd].emSigDataType = VSE_TYPE_UINT16;
    VSE_SigSrcMap[VSE_SIG_IDX_bWhlSpdFLSt].pCANSigValue=&VSE_RTE_SIG_bWhlSpdFLSt;         VSE_SigSrcMap[VSE_SIG_IDX_bWhlSpdFLSt].emSigSrcType=VSE_SIGNAL_SOURCE_bWhlSpdFLSt;        VSE_SigSrcMap[VSE_SIG_IDX_bWhlSpdFLSt].pSigFltInjVal=&VSE_Inj_Dft_bWhlSpdFLSt;      VSE_SigSrcMap[VSE_SIG_IDX_bWhlSpdFLSt].bSigFltInjFlg=VSE_Inj_Flg_bWhlSpdFLSt;      VSE_SigSrcMap[VSE_SIG_IDX_bWhlSpdFLSt].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_bWhlSpdFRSt].pCANSigValue=&VSE_RTE_SIG_bWhlSpdFRSt;         VSE_SigSrcMap[VSE_SIG_IDX_bWhlSpdFRSt].emSigSrcType=VSE_SIGNAL_SOURCE_bWhlSpdFRSt;        VSE_SigSrcMap[VSE_SIG_IDX_bWhlSpdFRSt].pSigFltInjVal=&VSE_Inj_Dft_bWhlSpdFRSt;      VSE_SigSrcMap[VSE_SIG_IDX_bWhlSpdFRSt].bSigFltInjFlg=VSE_Inj_Flg_bWhlSpdFRSt;      VSE_SigSrcMap[VSE_SIG_IDX_bWhlSpdFRSt].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_bWhlSpdRLSt].pCANSigValue=&VSE_RTE_SIG_bWhlSpdRLSt;         VSE_SigSrcMap[VSE_SIG_IDX_bWhlSpdRLSt].emSigSrcType=VSE_SIGNAL_SOURCE_bWhlSpdRLSt;        VSE_SigSrcMap[VSE_SIG_IDX_bWhlSpdRLSt].pSigFltInjVal=&VSE_Inj_Dft_bWhlSpdRLSt;      VSE_SigSrcMap[VSE_SIG_IDX_bWhlSpdRLSt].bSigFltInjFlg=VSE_Inj_Flg_bWhlSpdRLSt;      VSE_SigSrcMap[VSE_SIG_IDX_bWhlSpdRLSt].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_bWhlSpdRRSt].pCANSigValue=&VSE_RTE_SIG_bWhlSpdRRSt;         VSE_SigSrcMap[VSE_SIG_IDX_bWhlSpdRRSt].emSigSrcType=VSE_SIGNAL_SOURCE_bWhlSpdRRSt;        VSE_SigSrcMap[VSE_SIG_IDX_bWhlSpdRRSt].pSigFltInjVal=&VSE_Inj_Dft_bWhlSpdRRSt;      VSE_SigSrcMap[VSE_SIG_IDX_bWhlSpdRRSt].bSigFltInjFlg=VSE_Inj_Flg_bWhlSpdRRSt;      VSE_SigSrcMap[VSE_SIG_IDX_bWhlSpdRRSt].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_u8AccrPedlRate].pCANSigValue=&VSE_RTE_SIG_u8AccrPedlRate;         VSE_SigSrcMap[VSE_SIG_IDX_u8AccrPedlRate].emSigSrcType=VSE_SIGNAL_SOURCE_u8AccrPedlRate;        VSE_SigSrcMap[VSE_SIG_IDX_u8AccrPedlRate].pSigFltInjVal=&VSE_Inj_Dft_u8AccrPedlRate;       VSE_SigSrcMap[VSE_SIG_IDX_u8AccrPedlRate].bSigFltInjFlg=VSE_Inj_Flg_u8AccrPedlRate;       VSE_SigSrcMap[VSE_SIG_IDX_u8AccrPedlRate].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_bAccrPedlRateFlg].pCANSigValue=&VSE_RTE_SIG_bAccrPedlRateFlg;         VSE_SigSrcMap[VSE_SIG_IDX_bAccrPedlRateFlg].emSigSrcType=VSE_SIGNAL_SOURCE_bAccrPedlRateFlg;        VSE_SigSrcMap[VSE_SIG_IDX_bAccrPedlRateFlg].pSigFltInjVal=&VSE_Inj_Dft_bAccrPedlRateFlg;     VSE_SigSrcMap[VSE_SIG_IDX_bAccrPedlRateFlg].bSigFltInjFlg=VSE_Inj_Flg_bAccrPedlRateFlg;     VSE_SigSrcMap[VSE_SIG_IDX_bAccrPedlRateFlg].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_u8IPBBrkSts].pCANSigValue=&VSE_RTE_SIG_u8IPBBrkSts;         VSE_SigSrcMap[VSE_SIG_IDX_u8IPBBrkSts].emSigSrcType=VSE_SIGNAL_SOURCE_u8IPBBrkSts;        VSE_SigSrcMap[VSE_SIG_IDX_u8IPBBrkSts].pSigFltInjVal=&VSE_Inj_Dft_u8IPBBrkSts;      VSE_SigSrcMap[VSE_SIG_IDX_u8IPBBrkSts].bSigFltInjFlg=VSE_Inj_Flg_u8IPBBrkSts;      VSE_SigSrcMap[VSE_SIG_IDX_u8IPBBrkSts].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_u8BrkDepth].pCANSigValue=&VSE_RTE_SIG_u8BrkDepth;         VSE_SigSrcMap[VSE_SIG_IDX_u8BrkDepth].emSigSrcType=VSE_SIGNAL_SOURCE_u8BrkDepth;        VSE_SigSrcMap[VSE_SIG_IDX_u8BrkDepth].pSigFltInjVal=&VSE_Inj_Dft_u8BrkDepth;       VSE_SigSrcMap[VSE_SIG_IDX_u8BrkDepth].bSigFltInjFlg=VSE_Inj_Flg_u8BrkDepth;       VSE_SigSrcMap[VSE_SIG_IDX_u8BrkDepth].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_bBrkDepthSts].pCANSigValue=&VSE_RTE_SIG_bBrkDepthSts;         VSE_SigSrcMap[VSE_SIG_IDX_bBrkDepthSts].emSigSrcType=VSE_SIGNAL_SOURCE_bBrkDepthSts;        VSE_SigSrcMap[VSE_SIG_IDX_bBrkDepthSts].pSigFltInjVal=&VSE_Inj_Dft_bBrkDepthSts;     VSE_SigSrcMap[VSE_SIG_IDX_bBrkDepthSts].bSigFltInjFlg=VSE_Inj_Flg_bBrkDepthSts;     VSE_SigSrcMap[VSE_SIG_IDX_bBrkDepthSts].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_u16IPBPPrs].pCANSigValue=&VSE_RTE_SIG_u16IPBPPrs;         VSE_SigSrcMap[VSE_SIG_IDX_u16IPBPPrs].emSigSrcType=VSE_SIGNAL_SOURCE_u16IPBPPrs;        VSE_SigSrcMap[VSE_SIG_IDX_u16IPBPPrs].pSigFltInjVal=&VSE_Inj_Dft_u16IPBPPrs;       VSE_SigSrcMap[VSE_SIG_IDX_u16IPBPPrs].bSigFltInjFlg=VSE_Inj_Flg_u16IPBPPrs;       VSE_SigSrcMap[VSE_SIG_IDX_u16IPBPPrs].emSigDataType = VSE_TYPE_UINT16;
    VSE_SigSrcMap[VSE_SIG_IDX_bIPBPluPreSts].pCANSigValue=&VSE_RTE_SIG_bIPBPluPreSts;         VSE_SigSrcMap[VSE_SIG_IDX_bIPBPluPreSts].emSigSrcType=VSE_SIGNAL_SOURCE_bIPBPluPreSts;        VSE_SigSrcMap[VSE_SIG_IDX_bIPBPluPreSts].pSigFltInjVal=&VSE_Inj_Dft_bIPBPluPreSts;        VSE_SigSrcMap[VSE_SIG_IDX_bIPBPluPreSts].bSigFltInjFlg=VSE_Inj_Flg_bIPBPluPreSts;        VSE_SigSrcMap[VSE_SIG_IDX_bIPBPluPreSts].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_u8GearPosn].pCANSigValue=&VSE_RTE_SIG_u8GearPosn;         VSE_SigSrcMap[VSE_SIG_IDX_u8GearPosn].emSigSrcType=VSE_SIGNAL_SOURCE_u8GearPosn;        VSE_SigSrcMap[VSE_SIG_IDX_u8GearPosn].pSigFltInjVal=&VSE_Inj_Dft_u8GearPosn;       VSE_SigSrcMap[VSE_SIG_IDX_u8GearPosn].bSigFltInjFlg=VSE_Inj_Flg_u8GearPosn;       VSE_SigSrcMap[VSE_SIG_IDX_u8GearPosn].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_bGearSts].pCANSigValue=&VSE_RTE_SIG_bGearSts;         VSE_SigSrcMap[VSE_SIG_IDX_bGearSts].emSigSrcType=VSE_SIGNAL_SOURCE_bGearSts;        VSE_SigSrcMap[VSE_SIG_IDX_bGearSts].pSigFltInjVal=&VSE_Inj_Dft_bGearSts;     VSE_SigSrcMap[VSE_SIG_IDX_bGearSts].bSigFltInjFlg=VSE_Inj_Flg_bGearSts;     VSE_SigSrcMap[VSE_SIG_IDX_bGearSts].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_i16SteerAg].pCANSigValue=&VSE_RTE_SIG_i16SteerAg;         VSE_SigSrcMap[VSE_SIG_IDX_i16SteerAg].emSigSrcType=VSE_SIGNAL_SOURCE_i16SteerAg;        VSE_SigSrcMap[VSE_SIG_IDX_i16SteerAg].pSigFltInjVal=&VSE_Inj_Dft_i16SteerAg;       VSE_SigSrcMap[VSE_SIG_IDX_i16SteerAg].bSigFltInjFlg=VSE_Inj_Flg_i16SteerAg;       VSE_SigSrcMap[VSE_SIG_IDX_i16SteerAg].emSigDataType = VSE_TYPE_INT16;
    VSE_SigSrcMap[VSE_SIG_IDX_u16SteerAgSpd].pCANSigValue=&VSE_RTE_SIG_u16SteerAgSpd;         VSE_SigSrcMap[VSE_SIG_IDX_u16SteerAgSpd].emSigSrcType=VSE_SIGNAL_SOURCE_u16SteerAgSpd;        VSE_SigSrcMap[VSE_SIG_IDX_u16SteerAgSpd].pSigFltInjVal=&VSE_Inj_Dft_u16SteerAgSpd;        VSE_SigSrcMap[VSE_SIG_IDX_u16SteerAgSpd].bSigFltInjFlg=VSE_Inj_Flg_u16SteerAgSpd;        VSE_SigSrcMap[VSE_SIG_IDX_u16SteerAgSpd].emSigDataType = VSE_TYPE_UINT16;
    VSE_SigSrcMap[VSE_SIG_IDX_bSteerAgSnsSt].pCANSigValue=&VSE_RTE_SIG_bSteerAgSnsSt;         VSE_SigSrcMap[VSE_SIG_IDX_bSteerAgSnsSt].emSigSrcType=VSE_SIGNAL_SOURCE_bSteerAgSnsSt;        VSE_SigSrcMap[VSE_SIG_IDX_bSteerAgSnsSt].pSigFltInjVal=&VSE_Inj_Dft_bSteerAgSnsSt;        VSE_SigSrcMap[VSE_SIG_IDX_bSteerAgSnsSt].bSigFltInjFlg=VSE_Inj_Flg_bSteerAgSnsSt;        VSE_SigSrcMap[VSE_SIG_IDX_bSteerAgSnsSt].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_bSteerAgCASnsSt].pCANSigValue=&VSE_RTE_SIG_bSteerAgCASnsSt;         VSE_SigSrcMap[VSE_SIG_IDX_bSteerAgCASnsSt].emSigSrcType=VSE_SIGNAL_SOURCE_bSteerAgCASnsSt;        VSE_SigSrcMap[VSE_SIG_IDX_bSteerAgCASnsSt].pSigFltInjVal=&VSE_Inj_Dft_bSteerAgCASnsSt;      VSE_SigSrcMap[VSE_SIG_IDX_bSteerAgCASnsSt].bSigFltInjFlg=VSE_Inj_Flg_bSteerAgCASnsSt;      VSE_SigSrcMap[VSE_SIG_IDX_bSteerAgCASnsSt].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_u16RWhlSteerAg].pCANSigValue=&VSE_RTE_SIG_u16RWhlSteerAg;         VSE_SigSrcMap[VSE_SIG_IDX_u16RWhlSteerAg].emSigSrcType=VSE_SIGNAL_SOURCE_u16RWhlSteerAg;        VSE_SigSrcMap[VSE_SIG_IDX_u16RWhlSteerAg].pSigFltInjVal=&VSE_Inj_Dft_u16RWhlSteerAg;       VSE_SigSrcMap[VSE_SIG_IDX_u16RWhlSteerAg].bSigFltInjFlg=VSE_Inj_Flg_u16RWhlSteerAg;       VSE_SigSrcMap[VSE_SIG_IDX_u16RWhlSteerAg].emSigDataType = VSE_TYPE_UINT16;
    VSE_SigSrcMap[VSE_SIG_IDX_bRWhlSteerAgSts].pCANSigValue=&VSE_RTE_SIG_bRWhlSteerAgSts;         VSE_SigSrcMap[VSE_SIG_IDX_bRWhlSteerAgSts].emSigSrcType=VSE_SIGNAL_SOURCE_bRWhlSteerAgSts;        VSE_SigSrcMap[VSE_SIG_IDX_bRWhlSteerAgSts].pSigFltInjVal=&VSE_Inj_Dft_bRWhlSteerAgSts;      VSE_SigSrcMap[VSE_SIG_IDX_bRWhlSteerAgSts].bSigFltInjFlg=VSE_Inj_Flg_bRWhlSteerAgSts;      VSE_SigSrcMap[VSE_SIG_IDX_bRWhlSteerAgSts].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_bTCSActS].pCANSigValue=&VSE_RTE_SIG_bTCSActS;         VSE_SigSrcMap[VSE_SIG_IDX_bTCSActS].emSigSrcType=VSE_SIGNAL_SOURCE_bTCSActS;        VSE_SigSrcMap[VSE_SIG_IDX_bTCSActS].pSigFltInjVal=&VSE_Inj_Dft_bTCSActS;     VSE_SigSrcMap[VSE_SIG_IDX_bTCSActS].bSigFltInjFlg=VSE_Inj_Flg_bTCSActS;     VSE_SigSrcMap[VSE_SIG_IDX_bTCSActS].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_bTCSFlt].pCANSigValue=&VSE_RTE_SIG_bTCSFlt;         VSE_SigSrcMap[VSE_SIG_IDX_bTCSFlt].emSigSrcType=VSE_SIGNAL_SOURCE_bTCSFlt;        VSE_SigSrcMap[VSE_SIG_IDX_bTCSFlt].pSigFltInjVal=&VSE_Inj_Dft_bTCSFlt;      VSE_SigSrcMap[VSE_SIG_IDX_bTCSFlt].bSigFltInjFlg=VSE_Inj_Flg_bTCSFlt;      VSE_SigSrcMap[VSE_SIG_IDX_bTCSFlt].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_bABSActS].pCANSigValue=&VSE_RTE_SIG_bABSActS;         VSE_SigSrcMap[VSE_SIG_IDX_bABSActS].emSigSrcType=VSE_SIGNAL_SOURCE_bABSActS;        VSE_SigSrcMap[VSE_SIG_IDX_bABSActS].pSigFltInjVal=&VSE_Inj_Dft_bABSActS;     VSE_SigSrcMap[VSE_SIG_IDX_bABSActS].bSigFltInjFlg=VSE_Inj_Flg_bABSActS;     VSE_SigSrcMap[VSE_SIG_IDX_bABSActS].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_bABSFlt].pCANSigValue=&VSE_RTE_SIG_bABSFlt;         VSE_SigSrcMap[VSE_SIG_IDX_bABSFlt].emSigSrcType=VSE_SIGNAL_SOURCE_bABSFlt;        VSE_SigSrcMap[VSE_SIG_IDX_bABSFlt].pSigFltInjVal=&VSE_Inj_Dft_bABSFlt;      VSE_SigSrcMap[VSE_SIG_IDX_bABSFlt].bSigFltInjFlg=VSE_Inj_Flg_bABSFlt;      VSE_SigSrcMap[VSE_SIG_IDX_bABSFlt].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_bVDCActS].pCANSigValue=&VSE_RTE_SIG_bVDCActS;         VSE_SigSrcMap[VSE_SIG_IDX_bVDCActS].emSigSrcType=VSE_SIGNAL_SOURCE_bVDCActS;        VSE_SigSrcMap[VSE_SIG_IDX_bVDCActS].pSigFltInjVal=&VSE_Inj_Dft_bVDCActS;     VSE_SigSrcMap[VSE_SIG_IDX_bVDCActS].bSigFltInjFlg=VSE_Inj_Flg_bVDCActS;     VSE_SigSrcMap[VSE_SIG_IDX_bVDCActS].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_bVDCFlt].pCANSigValue=&VSE_RTE_SIG_bVDCFlt;         VSE_SigSrcMap[VSE_SIG_IDX_bVDCFlt].emSigSrcType=VSE_SIGNAL_SOURCE_bVDCFlt;        VSE_SigSrcMap[VSE_SIG_IDX_bVDCFlt].pSigFltInjVal=&VSE_Inj_Dft_bVDCFlt;      VSE_SigSrcMap[VSE_SIG_IDX_bVDCFlt].bSigFltInjFlg=VSE_Inj_Flg_bVDCFlt;      VSE_SigSrcMap[VSE_SIG_IDX_bVDCFlt].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_u8VehDrvMod].pCANSigValue=&VSE_RTE_SIG_u8VehDrvMod;         VSE_SigSrcMap[VSE_SIG_IDX_u8VehDrvMod].emSigSrcType=VSE_SIGNAL_SOURCE_u8VehDrvMod;        VSE_SigSrcMap[VSE_SIG_IDX_u8VehDrvMod].pSigFltInjVal=&VSE_Inj_Dft_u8VehDrvMod;      VSE_SigSrcMap[VSE_SIG_IDX_u8VehDrvMod].bSigFltInjFlg=VSE_Inj_Flg_u8VehDrvMod;      VSE_SigSrcMap[VSE_SIG_IDX_u8VehDrvMod].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_u8PwrGear].pCANSigValue=&VSE_RTE_SIG_u8PwrGear;         VSE_SigSrcMap[VSE_SIG_IDX_u8PwrGear].emSigSrcType=VSE_SIGNAL_SOURCE_u8PwrGear;        VSE_SigSrcMap[VSE_SIG_IDX_u8PwrGear].pSigFltInjVal=&VSE_Inj_Dft_u8PwrGear;        VSE_SigSrcMap[VSE_SIG_IDX_u8PwrGear].bSigFltInjFlg=VSE_Inj_Flg_u8PwrGear;        VSE_SigSrcMap[VSE_SIG_IDX_u8PwrGear].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_u8EPBSt].pCANSigValue=&VSE_RTE_SIG_u8EPBSt;         VSE_SigSrcMap[VSE_SIG_IDX_u8EPBSt].emSigSrcType=VSE_SIGNAL_SOURCE_u8EPBSt;        VSE_SigSrcMap[VSE_SIG_IDX_u8EPBSt].pSigFltInjVal=&VSE_Inj_Dft_u8EPBSt;      VSE_SigSrcMap[VSE_SIG_IDX_u8EPBSt].bSigFltInjFlg=VSE_Inj_Flg_u8EPBSt;      VSE_SigSrcMap[VSE_SIG_IDX_u8EPBSt].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_u16DamprPosnFL].pCANSigValue=&VSE_RTE_SIG_u16DamprPosnFL;         VSE_SigSrcMap[VSE_SIG_IDX_u16DamprPosnFL].emSigSrcType=VSE_SIGNAL_SOURCE_u16DamprPosnFL;        VSE_SigSrcMap[VSE_SIG_IDX_u16DamprPosnFL].pSigFltInjVal=&VSE_Inj_Dft_u16DamprPosnFL;       VSE_SigSrcMap[VSE_SIG_IDX_u16DamprPosnFL].bSigFltInjFlg=VSE_Inj_Flg_u16DamprPosnFL;       VSE_SigSrcMap[VSE_SIG_IDX_u16DamprPosnFL].emSigDataType = VSE_TYPE_UINT16;
    VSE_SigSrcMap[VSE_SIG_IDX_u16DamprPosnFR].pCANSigValue=&VSE_RTE_SIG_u16DamprPosnFR;         VSE_SigSrcMap[VSE_SIG_IDX_u16DamprPosnFR].emSigSrcType=VSE_SIGNAL_SOURCE_u16DamprPosnFR;        VSE_SigSrcMap[VSE_SIG_IDX_u16DamprPosnFR].pSigFltInjVal=&VSE_Inj_Dft_u16DamprPosnFR;       VSE_SigSrcMap[VSE_SIG_IDX_u16DamprPosnFR].bSigFltInjFlg=VSE_Inj_Flg_u16DamprPosnFR;       VSE_SigSrcMap[VSE_SIG_IDX_u16DamprPosnFR].emSigDataType = VSE_TYPE_UINT16;
    VSE_SigSrcMap[VSE_SIG_IDX_u16DamprPosnRL].pCANSigValue=&VSE_RTE_SIG_u16DamprPosnRL;         VSE_SigSrcMap[VSE_SIG_IDX_u16DamprPosnRL].emSigSrcType=VSE_SIGNAL_SOURCE_u16DamprPosnRL;        VSE_SigSrcMap[VSE_SIG_IDX_u16DamprPosnRL].pSigFltInjVal=&VSE_Inj_Dft_u16DamprPosnRL;       VSE_SigSrcMap[VSE_SIG_IDX_u16DamprPosnRL].bSigFltInjFlg=VSE_Inj_Flg_u16DamprPosnRL;       VSE_SigSrcMap[VSE_SIG_IDX_u16DamprPosnRL].emSigDataType = VSE_TYPE_UINT16;
    VSE_SigSrcMap[VSE_SIG_IDX_u16DamprPosnRR].pCANSigValue=&VSE_RTE_SIG_u16DamprPosnRR;         VSE_SigSrcMap[VSE_SIG_IDX_u16DamprPosnRR].emSigSrcType=VSE_SIGNAL_SOURCE_u16DamprPosnRR;        VSE_SigSrcMap[VSE_SIG_IDX_u16DamprPosnRR].pSigFltInjVal=&VSE_Inj_Dft_u16DamprPosnRR;       VSE_SigSrcMap[VSE_SIG_IDX_u16DamprPosnRR].bSigFltInjFlg=VSE_Inj_Flg_u16DamprPosnRR;       VSE_SigSrcMap[VSE_SIG_IDX_u16DamprPosnRR].emSigDataType = VSE_TYPE_UINT16;
    VSE_SigSrcMap[VSE_SIG_IDX_bDamprPosnFLSts].pCANSigValue=&VSE_RTE_SIG_bDamprPosnFLSts;         VSE_SigSrcMap[VSE_SIG_IDX_bDamprPosnFLSts].emSigSrcType=VSE_SIGNAL_SOURCE_bDamprPosnFLSts;        VSE_SigSrcMap[VSE_SIG_IDX_bDamprPosnFLSts].pSigFltInjVal=&VSE_Inj_Dft_bDamprPosnFLSts;      VSE_SigSrcMap[VSE_SIG_IDX_bDamprPosnFLSts].bSigFltInjFlg=VSE_Inj_Flg_bDamprPosnFLSts;      VSE_SigSrcMap[VSE_SIG_IDX_bDamprPosnFLSts].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_bDamprPosnFRSts].pCANSigValue=&VSE_RTE_SIG_bDamprPosnFRSts;         VSE_SigSrcMap[VSE_SIG_IDX_bDamprPosnFRSts].emSigSrcType=VSE_SIGNAL_SOURCE_bDamprPosnFRSts;        VSE_SigSrcMap[VSE_SIG_IDX_bDamprPosnFRSts].pSigFltInjVal=&VSE_Inj_Dft_bDamprPosnFRSts;      VSE_SigSrcMap[VSE_SIG_IDX_bDamprPosnFRSts].bSigFltInjFlg=VSE_Inj_Flg_bDamprPosnFRSts;      VSE_SigSrcMap[VSE_SIG_IDX_bDamprPosnFRSts].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_bDamprPosnRLSts].pCANSigValue=&VSE_RTE_SIG_bDamprPosnRLSts;         VSE_SigSrcMap[VSE_SIG_IDX_bDamprPosnRLSts].emSigSrcType=VSE_SIGNAL_SOURCE_bDamprPosnRLSts;        VSE_SigSrcMap[VSE_SIG_IDX_bDamprPosnRLSts].pSigFltInjVal=&VSE_Inj_Dft_bDamprPosnRLSts;      VSE_SigSrcMap[VSE_SIG_IDX_bDamprPosnRLSts].bSigFltInjFlg=VSE_Inj_Flg_bDamprPosnRLSts;      VSE_SigSrcMap[VSE_SIG_IDX_bDamprPosnRLSts].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_bDamprPosnRRSts].pCANSigValue=&VSE_RTE_SIG_bDamprPosnRRSts;         VSE_SigSrcMap[VSE_SIG_IDX_bDamprPosnRRSts].emSigSrcType=VSE_SIGNAL_SOURCE_bDamprPosnRRSts;        VSE_SigSrcMap[VSE_SIG_IDX_bDamprPosnRRSts].pSigFltInjVal=&VSE_Inj_Dft_bDamprPosnRRSts;      VSE_SigSrcMap[VSE_SIG_IDX_bDamprPosnRRSts].bSigFltInjFlg=VSE_Inj_Flg_bDamprPosnRRSts;      VSE_SigSrcMap[VSE_SIG_IDX_bDamprPosnRRSts].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_u16FLActualCurrent].pCANSigValue=&VSE_RTE_SIG_u16FLActualCurrent;         VSE_SigSrcMap[VSE_SIG_IDX_u16FLActualCurrent].emSigSrcType=VSE_SIGNAL_SOURCE_u16FLActualCurrent;        VSE_SigSrcMap[VSE_SIG_IDX_u16FLActualCurrent].pSigFltInjVal=&VSE_Inj_Dft_u16FLActualCurrent;       VSE_SigSrcMap[VSE_SIG_IDX_u16FLActualCurrent].bSigFltInjFlg=VSE_Inj_Flg_u16FLActualCurrent;       VSE_SigSrcMap[VSE_SIG_IDX_u16FLActualCurrent].emSigDataType = VSE_TYPE_UINT16;
    VSE_SigSrcMap[VSE_SIG_IDX_u16FRActualCurrent].pCANSigValue=&VSE_RTE_SIG_u16FRActualCurrent;         VSE_SigSrcMap[VSE_SIG_IDX_u16FRActualCurrent].emSigSrcType=VSE_SIGNAL_SOURCE_u16FRActualCurrent;        VSE_SigSrcMap[VSE_SIG_IDX_u16FRActualCurrent].pSigFltInjVal=&VSE_Inj_Dft_u16FRActualCurrent;       VSE_SigSrcMap[VSE_SIG_IDX_u16FRActualCurrent].bSigFltInjFlg=VSE_Inj_Flg_u16FRActualCurrent;       VSE_SigSrcMap[VSE_SIG_IDX_u16FRActualCurrent].emSigDataType = VSE_TYPE_UINT16;
    VSE_SigSrcMap[VSE_SIG_IDX_u16RLActualCurrent].pCANSigValue=&VSE_RTE_SIG_u16RLActualCurrent;         VSE_SigSrcMap[VSE_SIG_IDX_u16RLActualCurrent].emSigSrcType=VSE_SIGNAL_SOURCE_u16RLActualCurrent;        VSE_SigSrcMap[VSE_SIG_IDX_u16RLActualCurrent].pSigFltInjVal=&VSE_Inj_Dft_u16RLActualCurrent;       VSE_SigSrcMap[VSE_SIG_IDX_u16RLActualCurrent].bSigFltInjFlg=VSE_Inj_Flg_u16RLActualCurrent;       VSE_SigSrcMap[VSE_SIG_IDX_u16RLActualCurrent].emSigDataType = VSE_TYPE_UINT16;
    VSE_SigSrcMap[VSE_SIG_IDX_u16RRActualCurrent].pCANSigValue=&VSE_RTE_SIG_u16RRActualCurrent;         VSE_SigSrcMap[VSE_SIG_IDX_u16RRActualCurrent].emSigSrcType=VSE_SIGNAL_SOURCE_u16RRActualCurrent;        VSE_SigSrcMap[VSE_SIG_IDX_u16RRActualCurrent].pSigFltInjVal=&VSE_Inj_Dft_u16RRActualCurrent;       VSE_SigSrcMap[VSE_SIG_IDX_u16RRActualCurrent].bSigFltInjFlg=VSE_Inj_Flg_u16RRActualCurrent;       VSE_SigSrcMap[VSE_SIG_IDX_u16RRActualCurrent].emSigDataType = VSE_TYPE_UINT16;
    VSE_SigSrcMap[VSE_SIG_IDX_u8DiSusModExeSts].pCANSigValue=&VSE_RTE_SIG_u8DiSusModExeSts;        VSE_SigSrcMap[VSE_SIG_IDX_u8DiSusModExeSts].emSigSrcType=VSE_SIGNAL_SOURCE_u8DiSusModExeSts;     VSE_SigSrcMap[VSE_SIG_IDX_u8DiSusModExeSts].pSigFltInjVal=&VSE_Inj_Dft_u8DiSusModExeSts;     VSE_SigSrcMap[VSE_SIG_IDX_u8DiSusModExeSts].bSigFltInjFlg=VSE_Inj_Flg_u8DiSusModExeSts;     VSE_SigSrcMap[VSE_SIG_IDX_u8DiSusModExeSts].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_u8DiSus_Type].pCANSigValue=&VSE_RTE_SIG_u8DiSus_Type;        VSE_SigSrcMap[VSE_SIG_IDX_u8DiSus_Type].emSigSrcType=VSE_SIGNAL_SOURCE_u8DiSus_Type;     VSE_SigSrcMap[VSE_SIG_IDX_u8DiSus_Type].pSigFltInjVal=&VSE_Inj_Dft_u8DiSus_Type;     VSE_SigSrcMap[VSE_SIG_IDX_u8DiSus_Type].bSigFltInjFlg=VSE_Inj_Flg_u8DiSus_Type;     VSE_SigSrcMap[VSE_SIG_IDX_u8DiSus_Type].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_bDiSusHeiAdjSts].pCANSigValue=&VSE_RTE_SIG_bDiSusHeiAdjSts;         VSE_SigSrcMap[VSE_SIG_IDX_bDiSusHeiAdjSts].emSigSrcType=VSE_SIGNAL_SOURCE_bDiSusHeiAdjSts;        VSE_SigSrcMap[VSE_SIG_IDX_bDiSusHeiAdjSts].pSigFltInjVal=&VSE_Inj_Dft_bDiSusHeiAdjSts;      VSE_SigSrcMap[VSE_SIG_IDX_bDiSusHeiAdjSts].bSigFltInjFlg=VSE_Inj_Flg_bDiSusHeiAdjSts;      VSE_SigSrcMap[VSE_SIG_IDX_bDiSusHeiAdjSts].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_u8DiSusHeiAdjProc].pCANSigValue=&VSE_RTE_SIG_u8DiSusHeiAdjProc;         VSE_SigSrcMap[VSE_SIG_IDX_u8DiSusHeiAdjProc].emSigSrcType=VSE_SIGNAL_SOURCE_u8DiSusHeiAdjProc;        VSE_SigSrcMap[VSE_SIG_IDX_u8DiSusHeiAdjProc].pSigFltInjVal=&VSE_Inj_Dft_u8DiSusHeiAdjProc;        VSE_SigSrcMap[VSE_SIG_IDX_u8DiSusHeiAdjProc].bSigFltInjFlg=VSE_Inj_Flg_u8DiSusHeiAdjProc;        VSE_SigSrcMap[VSE_SIG_IDX_u8DiSusHeiAdjProc].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_bDiSusHeiAdjFltSts].pCANSigValue=&VSE_RTE_SIG_bDiSusHeiAdjFltSts;         VSE_SigSrcMap[VSE_SIG_IDX_bDiSusHeiAdjFltSts].emSigSrcType=VSE_SIGNAL_SOURCE_bDiSusHeiAdjFltSts;        VSE_SigSrcMap[VSE_SIG_IDX_bDiSusHeiAdjFltSts].pSigFltInjVal=&VSE_Inj_Dft_bDiSusHeiAdjFltSts;       VSE_SigSrcMap[VSE_SIG_IDX_bDiSusHeiAdjFltSts].bSigFltInjFlg=VSE_Inj_Flg_bDiSusHeiAdjFltSts;       VSE_SigSrcMap[VSE_SIG_IDX_bDiSusHeiAdjFltSts].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_u16CmpActIFL].pCANSigValue=&VSE_RTE_SIG_u16CmpActIFL;         VSE_SigSrcMap[VSE_SIG_IDX_u16CmpActIFL].emSigSrcType=VSE_SIGNAL_SOURCE_u16CmpActIFL;        VSE_SigSrcMap[VSE_SIG_IDX_u16CmpActIFL].pSigFltInjVal=&VSE_Inj_Dft_u16CmpActIFL;     VSE_SigSrcMap[VSE_SIG_IDX_u16CmpActIFL].bSigFltInjFlg=VSE_Inj_Flg_u16CmpActIFL;     VSE_SigSrcMap[VSE_SIG_IDX_u16CmpActIFL].emSigDataType = VSE_TYPE_UINT16;
    VSE_SigSrcMap[VSE_SIG_IDX_u16StchActIFL].pCANSigValue=&VSE_RTE_SIG_u16StchActIFL;         VSE_SigSrcMap[VSE_SIG_IDX_u16StchActIFL].emSigSrcType=VSE_SIGNAL_SOURCE_u16StchActIFL;        VSE_SigSrcMap[VSE_SIG_IDX_u16StchActIFL].pSigFltInjVal=&VSE_Inj_Dft_u16StchActIFL;        VSE_SigSrcMap[VSE_SIG_IDX_u16StchActIFL].bSigFltInjFlg=VSE_Inj_Flg_u16StchActIFL;        VSE_SigSrcMap[VSE_SIG_IDX_u16StchActIFL].emSigDataType = VSE_TYPE_UINT16;
    VSE_SigSrcMap[VSE_SIG_IDX_u16CmpActIFR].pCANSigValue=&VSE_RTE_SIG_u16CmpActIFR;         VSE_SigSrcMap[VSE_SIG_IDX_u16CmpActIFR].emSigSrcType=VSE_SIGNAL_SOURCE_u16CmpActIFR;        VSE_SigSrcMap[VSE_SIG_IDX_u16CmpActIFR].pSigFltInjVal=&VSE_Inj_Dft_u16CmpActIFR;     VSE_SigSrcMap[VSE_SIG_IDX_u16CmpActIFR].bSigFltInjFlg=VSE_Inj_Flg_u16CmpActIFR;     VSE_SigSrcMap[VSE_SIG_IDX_u16CmpActIFR].emSigDataType = VSE_TYPE_UINT16;
    VSE_SigSrcMap[VSE_SIG_IDX_u16StchActIFR].pCANSigValue=&VSE_RTE_SIG_u16StchActIFR;         VSE_SigSrcMap[VSE_SIG_IDX_u16StchActIFR].emSigSrcType=VSE_SIGNAL_SOURCE_u16StchActIFR;        VSE_SigSrcMap[VSE_SIG_IDX_u16StchActIFR].pSigFltInjVal=&VSE_Inj_Dft_u16StchActIFR;        VSE_SigSrcMap[VSE_SIG_IDX_u16StchActIFR].bSigFltInjFlg=VSE_Inj_Flg_u16StchActIFR;        VSE_SigSrcMap[VSE_SIG_IDX_u16StchActIFR].emSigDataType = VSE_TYPE_UINT16;
    VSE_SigSrcMap[VSE_SIG_IDX_u16CmpActIRL].pCANSigValue=&VSE_RTE_SIG_u16CmpActIRL;         VSE_SigSrcMap[VSE_SIG_IDX_u16CmpActIRL].emSigSrcType=VSE_SIGNAL_SOURCE_u16CmpActIRL;        VSE_SigSrcMap[VSE_SIG_IDX_u16CmpActIRL].pSigFltInjVal=&VSE_Inj_Dft_u16CmpActIRL;     VSE_SigSrcMap[VSE_SIG_IDX_u16CmpActIRL].bSigFltInjFlg=VSE_Inj_Flg_u16CmpActIRL;     VSE_SigSrcMap[VSE_SIG_IDX_u16CmpActIRL].emSigDataType = VSE_TYPE_UINT16;
    VSE_SigSrcMap[VSE_SIG_IDX_u16StchActIRL].pCANSigValue=&VSE_RTE_SIG_u16StchActIRL;         VSE_SigSrcMap[VSE_SIG_IDX_u16StchActIRL].emSigSrcType=VSE_SIGNAL_SOURCE_u16StchActIRL;        VSE_SigSrcMap[VSE_SIG_IDX_u16StchActIRL].pSigFltInjVal=&VSE_Inj_Dft_u16StchActIRL;        VSE_SigSrcMap[VSE_SIG_IDX_u16StchActIRL].bSigFltInjFlg=VSE_Inj_Flg_u16StchActIRL;        VSE_SigSrcMap[VSE_SIG_IDX_u16StchActIRL].emSigDataType = VSE_TYPE_UINT16;
    VSE_SigSrcMap[VSE_SIG_IDX_u16CmpActIRR].pCANSigValue=&VSE_RTE_SIG_u16CmpActIRR;         VSE_SigSrcMap[VSE_SIG_IDX_u16CmpActIRR].emSigSrcType=VSE_SIGNAL_SOURCE_u16CmpActIRR;        VSE_SigSrcMap[VSE_SIG_IDX_u16CmpActIRR].pSigFltInjVal=&VSE_Inj_Dft_u16CmpActIRR;     VSE_SigSrcMap[VSE_SIG_IDX_u16CmpActIRR].bSigFltInjFlg=VSE_Inj_Flg_u16CmpActIRR;     VSE_SigSrcMap[VSE_SIG_IDX_u16CmpActIRR].emSigDataType = VSE_TYPE_UINT16;
    VSE_SigSrcMap[VSE_SIG_IDX_u16StchActIRR].pCANSigValue=&VSE_RTE_SIG_u16StchActIRR;         VSE_SigSrcMap[VSE_SIG_IDX_u16StchActIRR].emSigSrcType=VSE_SIGNAL_SOURCE_u16StchActIRR;        VSE_SigSrcMap[VSE_SIG_IDX_u16StchActIRR].pSigFltInjVal=&VSE_Inj_Dft_u16StchActIRR;        VSE_SigSrcMap[VSE_SIG_IDX_u16StchActIRR].bSigFltInjFlg=VSE_Inj_Flg_u16StchActIRR;        VSE_SigSrcMap[VSE_SIG_IDX_u16StchActIRR].emSigDataType = VSE_TYPE_UINT16;
    VSE_SigSrcMap[VSE_SIG_IDX_u16EstimdFFL].pCANSigValue=&VSE_RTE_SIG_u16EstimdFFL;         VSE_SigSrcMap[VSE_SIG_IDX_u16EstimdFFL].emSigSrcType=VSE_SIGNAL_SOURCE_u16EstimdFFL;        VSE_SigSrcMap[VSE_SIG_IDX_u16EstimdFFL].pSigFltInjVal=&VSE_Inj_Dft_u16EstimdFFL;     VSE_SigSrcMap[VSE_SIG_IDX_u16EstimdFFL].bSigFltInjFlg=VSE_Inj_Flg_u16EstimdFFL;     VSE_SigSrcMap[VSE_SIG_IDX_u16EstimdFFL].emSigDataType = VSE_TYPE_UINT16;
    VSE_SigSrcMap[VSE_SIG_IDX_u16EstimdFFR].pCANSigValue=&VSE_RTE_SIG_u16EstimdFFR;         VSE_SigSrcMap[VSE_SIG_IDX_u16EstimdFFR].emSigSrcType=VSE_SIGNAL_SOURCE_u16EstimdFFR;        VSE_SigSrcMap[VSE_SIG_IDX_u16EstimdFFR].pSigFltInjVal=&VSE_Inj_Dft_u16EstimdFFR;     VSE_SigSrcMap[VSE_SIG_IDX_u16EstimdFFR].bSigFltInjFlg=VSE_Inj_Flg_u16EstimdFFR;     VSE_SigSrcMap[VSE_SIG_IDX_u16EstimdFFR].emSigDataType = VSE_TYPE_UINT16;
    VSE_SigSrcMap[VSE_SIG_IDX_u16EstimdFRL].pCANSigValue=&VSE_RTE_SIG_u16EstimdFRL;         VSE_SigSrcMap[VSE_SIG_IDX_u16EstimdFRL].emSigSrcType=VSE_SIGNAL_SOURCE_u16EstimdFRL;        VSE_SigSrcMap[VSE_SIG_IDX_u16EstimdFRL].pSigFltInjVal=&VSE_Inj_Dft_u16EstimdFRL;     VSE_SigSrcMap[VSE_SIG_IDX_u16EstimdFRL].bSigFltInjFlg=VSE_Inj_Flg_u16EstimdFRL;     VSE_SigSrcMap[VSE_SIG_IDX_u16EstimdFRL].emSigDataType = VSE_TYPE_UINT16;
    VSE_SigSrcMap[VSE_SIG_IDX_u16EstimdFRR].pCANSigValue=&VSE_RTE_SIG_u16EstimdFRR;         VSE_SigSrcMap[VSE_SIG_IDX_u16EstimdFRR].emSigSrcType=VSE_SIGNAL_SOURCE_u16EstimdFRR;        VSE_SigSrcMap[VSE_SIG_IDX_u16EstimdFRR].pSigFltInjVal=&VSE_Inj_Dft_u16EstimdFRR;     VSE_SigSrcMap[VSE_SIG_IDX_u16EstimdFRR].bSigFltInjFlg=VSE_Inj_Flg_u16EstimdFRR;     VSE_SigSrcMap[VSE_SIG_IDX_u16EstimdFRR].emSigDataType = VSE_TYPE_UINT16;
    VSE_SigSrcMap[VSE_SIG_IDX_i16DamprWhlHFL].pCANSigValue=&VSE_RTE_SIG_i16DamprWhlHFL;         VSE_SigSrcMap[VSE_SIG_IDX_i16DamprWhlHFL].emSigSrcType=VSE_SIGNAL_SOURCE_i16DamprWhlHFL;        VSE_SigSrcMap[VSE_SIG_IDX_i16DamprWhlHFL].pSigFltInjVal=&VSE_Inj_Dft_i16DamprWhlHFL;       VSE_SigSrcMap[VSE_SIG_IDX_i16DamprWhlHFL].bSigFltInjFlg=VSE_Inj_Flg_i16DamprWhlHFL;       VSE_SigSrcMap[VSE_SIG_IDX_i16DamprWhlHFL].emSigDataType = VSE_TYPE_INT16;
    VSE_SigSrcMap[VSE_SIG_IDX_i16DamprWhlHFR].pCANSigValue=&VSE_RTE_SIG_i16DamprWhlHFR;         VSE_SigSrcMap[VSE_SIG_IDX_i16DamprWhlHFR].emSigSrcType=VSE_SIGNAL_SOURCE_i16DamprWhlHFR;        VSE_SigSrcMap[VSE_SIG_IDX_i16DamprWhlHFR].pSigFltInjVal=&VSE_Inj_Dft_i16DamprWhlHFR;       VSE_SigSrcMap[VSE_SIG_IDX_i16DamprWhlHFR].bSigFltInjFlg=VSE_Inj_Flg_i16DamprWhlHFR;       VSE_SigSrcMap[VSE_SIG_IDX_i16DamprWhlHFR].emSigDataType = VSE_TYPE_INT16;
    VSE_SigSrcMap[VSE_SIG_IDX_i16DamprWhlHRL].pCANSigValue=&VSE_RTE_SIG_i16DamprWhlHRL;         VSE_SigSrcMap[VSE_SIG_IDX_i16DamprWhlHRL].emSigSrcType=VSE_SIGNAL_SOURCE_i16DamprWhlHRL;        VSE_SigSrcMap[VSE_SIG_IDX_i16DamprWhlHRL].pSigFltInjVal=&VSE_Inj_Dft_i16DamprWhlHRL;       VSE_SigSrcMap[VSE_SIG_IDX_i16DamprWhlHRL].bSigFltInjFlg=VSE_Inj_Flg_i16DamprWhlHRL;       VSE_SigSrcMap[VSE_SIG_IDX_i16DamprWhlHRL].emSigDataType = VSE_TYPE_INT16;
    VSE_SigSrcMap[VSE_SIG_IDX_i16DamprWhlHRR].pCANSigValue=&VSE_RTE_SIG_i16DamprWhlHRR;         VSE_SigSrcMap[VSE_SIG_IDX_i16DamprWhlHRR].emSigSrcType=VSE_SIGNAL_SOURCE_i16DamprWhlHRR;        VSE_SigSrcMap[VSE_SIG_IDX_i16DamprWhlHRR].pSigFltInjVal=&VSE_Inj_Dft_i16DamprWhlHRR;       VSE_SigSrcMap[VSE_SIG_IDX_i16DamprWhlHRR].bSigFltInjFlg=VSE_Inj_Flg_i16DamprWhlHRR;       VSE_SigSrcMap[VSE_SIG_IDX_i16DamprWhlHRR].emSigDataType = VSE_TYPE_INT16;
    VSE_SigSrcMap[VSE_SIG_IDX_SNS_u16FLWhlSpd].pCANSigValue=&VSE_RTE_SIG_SNS_u16FLWhlSpd;         VSE_SigSrcMap[VSE_SIG_IDX_SNS_u16FLWhlSpd].emSigSrcType=VSE_SIGNAL_SOURCE_SNS_u16FLWhlSpd;        VSE_SigSrcMap[VSE_SIG_IDX_SNS_u16FLWhlSpd].pSigFltInjVal=&VSE_Inj_Dft_SNS_u16FLWhlSpd;      VSE_SigSrcMap[VSE_SIG_IDX_SNS_u16FLWhlSpd].bSigFltInjFlg=VSE_Inj_Flg_SNS_u16FLWhlSpd;      VSE_SigSrcMap[VSE_SIG_IDX_SNS_u16FLWhlSpd].emSigDataType = VSE_TYPE_UINT16;
    VSE_SigSrcMap[VSE_SIG_IDX_SNS_u16FRWhlSpd].pCANSigValue=&VSE_RTE_SIG_SNS_u16FRWhlSpd;         VSE_SigSrcMap[VSE_SIG_IDX_SNS_u16FRWhlSpd].emSigSrcType=VSE_SIGNAL_SOURCE_SNS_u16FRWhlSpd;        VSE_SigSrcMap[VSE_SIG_IDX_SNS_u16FRWhlSpd].pSigFltInjVal=&VSE_Inj_Dft_SNS_u16FRWhlSpd;      VSE_SigSrcMap[VSE_SIG_IDX_SNS_u16FRWhlSpd].bSigFltInjFlg=VSE_Inj_Flg_SNS_u16FRWhlSpd;      VSE_SigSrcMap[VSE_SIG_IDX_SNS_u16FRWhlSpd].emSigDataType = VSE_TYPE_UINT16;
    VSE_SigSrcMap[VSE_SIG_IDX_SNS_u16RLWhlSpd].pCANSigValue=&VSE_RTE_SIG_SNS_u16RLWhlSpd;         VSE_SigSrcMap[VSE_SIG_IDX_SNS_u16RLWhlSpd].emSigSrcType=VSE_SIGNAL_SOURCE_SNS_u16RLWhlSpd;        VSE_SigSrcMap[VSE_SIG_IDX_SNS_u16RLWhlSpd].pSigFltInjVal=&VSE_Inj_Dft_SNS_u16RLWhlSpd;      VSE_SigSrcMap[VSE_SIG_IDX_SNS_u16RLWhlSpd].bSigFltInjFlg=VSE_Inj_Flg_SNS_u16RLWhlSpd;      VSE_SigSrcMap[VSE_SIG_IDX_SNS_u16RLWhlSpd].emSigDataType = VSE_TYPE_UINT16;
    VSE_SigSrcMap[VSE_SIG_IDX_SNS_u16RRWhlSpd].pCANSigValue=&VSE_RTE_SIG_SNS_u16RRWhlSpd;         VSE_SigSrcMap[VSE_SIG_IDX_SNS_u16RRWhlSpd].emSigSrcType=VSE_SIGNAL_SOURCE_SNS_u16RRWhlSpd;        VSE_SigSrcMap[VSE_SIG_IDX_SNS_u16RRWhlSpd].pSigFltInjVal=&VSE_Inj_Dft_SNS_u16RRWhlSpd;      VSE_SigSrcMap[VSE_SIG_IDX_SNS_u16RRWhlSpd].bSigFltInjFlg=VSE_Inj_Flg_SNS_u16RRWhlSpd;      VSE_SigSrcMap[VSE_SIG_IDX_SNS_u16RRWhlSpd].emSigDataType = VSE_TYPE_UINT16;
    VSE_SigSrcMap[VSE_SIG_IDX_SNS_bWhlSpdFLSt].pCANSigValue=&VSE_RTE_SIG_SNS_bWhlSpdFLSt;         VSE_SigSrcMap[VSE_SIG_IDX_SNS_bWhlSpdFLSt].emSigSrcType=VSE_SIGNAL_SOURCE_SNS_bWhlSpdFLSt;        VSE_SigSrcMap[VSE_SIG_IDX_SNS_bWhlSpdFLSt].pSigFltInjVal=&VSE_Inj_Dft_SNS_bWhlSpdFLSt;      VSE_SigSrcMap[VSE_SIG_IDX_SNS_bWhlSpdFLSt].bSigFltInjFlg=VSE_Inj_Flg_SNS_bWhlSpdFLSt;      VSE_SigSrcMap[VSE_SIG_IDX_SNS_bWhlSpdFLSt].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_SNS_bWhlSpdFRSt].pCANSigValue=&VSE_RTE_SIG_SNS_bWhlSpdFRSt;         VSE_SigSrcMap[VSE_SIG_IDX_SNS_bWhlSpdFRSt].emSigSrcType=VSE_SIGNAL_SOURCE_SNS_bWhlSpdFRSt;        VSE_SigSrcMap[VSE_SIG_IDX_SNS_bWhlSpdFRSt].pSigFltInjVal=&VSE_Inj_Dft_SNS_bWhlSpdFRSt;      VSE_SigSrcMap[VSE_SIG_IDX_SNS_bWhlSpdFRSt].bSigFltInjFlg=VSE_Inj_Flg_SNS_bWhlSpdFRSt;      VSE_SigSrcMap[VSE_SIG_IDX_SNS_bWhlSpdFRSt].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_SNS_bWhlSpdRLSt].pCANSigValue=&VSE_RTE_SIG_SNS_bWhlSpdRLSt;         VSE_SigSrcMap[VSE_SIG_IDX_SNS_bWhlSpdRLSt].emSigSrcType=VSE_SIGNAL_SOURCE_SNS_bWhlSpdRLSt;        VSE_SigSrcMap[VSE_SIG_IDX_SNS_bWhlSpdRLSt].pSigFltInjVal=&VSE_Inj_Dft_SNS_bWhlSpdRLSt;      VSE_SigSrcMap[VSE_SIG_IDX_SNS_bWhlSpdRLSt].bSigFltInjFlg=VSE_Inj_Flg_SNS_bWhlSpdRLSt;      VSE_SigSrcMap[VSE_SIG_IDX_SNS_bWhlSpdRLSt].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_SNS_bWhlSpdRRSt].pCANSigValue=&VSE_RTE_SIG_SNS_bWhlSpdRRSt;         VSE_SigSrcMap[VSE_SIG_IDX_SNS_bWhlSpdRRSt].emSigSrcType=VSE_SIGNAL_SOURCE_SNS_bWhlSpdRRSt;        VSE_SigSrcMap[VSE_SIG_IDX_SNS_bWhlSpdRRSt].pSigFltInjVal=&VSE_Inj_Dft_SNS_bWhlSpdRRSt;      VSE_SigSrcMap[VSE_SIG_IDX_SNS_bWhlSpdRRSt].bSigFltInjFlg=VSE_Inj_Flg_SNS_bWhlSpdRRSt;      VSE_SigSrcMap[VSE_SIG_IDX_SNS_bWhlSpdRRSt].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_bIMU_051Flt].pCANSigValue=&VSE_RTE_SIG_bIMU_051Flt;         VSE_SigSrcMap[VSE_SIG_IDX_bIMU_051Flt].emSigSrcType=VSE_SIGNAL_SOURCE_bIMU_051Flt;        VSE_SigSrcMap[VSE_SIG_IDX_bIMU_051Flt].pSigFltInjVal=&VSE_Inj_Dft_bIMU_051Flt;      VSE_SigSrcMap[VSE_SIG_IDX_bIMU_051Flt].bSigFltInjFlg=VSE_Inj_Flg_bIMU_051Flt;      VSE_SigSrcMap[VSE_SIG_IDX_bIMU_051Flt].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_bVCU_0FCFlt].pCANSigValue=&VSE_RTE_SIG_bVCU_0FCFlt;         VSE_SigSrcMap[VSE_SIG_IDX_bVCU_0FCFlt].emSigSrcType=VSE_SIGNAL_SOURCE_bVCU_0FCFlt;        VSE_SigSrcMap[VSE_SIG_IDX_bVCU_0FCFlt].pSigFltInjVal=&VSE_Inj_Dft_bVCU_0FCFlt;      VSE_SigSrcMap[VSE_SIG_IDX_bVCU_0FCFlt].bSigFltInjFlg=VSE_Inj_Flg_bVCU_0FCFlt;      VSE_SigSrcMap[VSE_SIG_IDX_bVCU_0FCFlt].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_bVCU_342Flt].pCANSigValue=&VSE_RTE_SIG_bVCU_342Flt;         VSE_SigSrcMap[VSE_SIG_IDX_bVCU_342Flt].emSigSrcType=VSE_SIGNAL_SOURCE_bVCU_342Flt;        VSE_SigSrcMap[VSE_SIG_IDX_bVCU_342Flt].pSigFltInjVal=&VSE_Inj_Dft_bVCU_342Flt;      VSE_SigSrcMap[VSE_SIG_IDX_bVCU_342Flt].bSigFltInjFlg=VSE_Inj_Flg_bVCU_342Flt;      VSE_SigSrcMap[VSE_SIG_IDX_bVCU_342Flt].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_bVCU_12DFlt].pCANSigValue=&VSE_RTE_SIG_bVCU_12DFlt;         VSE_SigSrcMap[VSE_SIG_IDX_bVCU_12DFlt].emSigSrcType=VSE_SIGNAL_SOURCE_bVCU_12DFlt;        VSE_SigSrcMap[VSE_SIG_IDX_bVCU_12DFlt].pSigFltInjVal=&VSE_Inj_Dft_bVCU_12DFlt;      VSE_SigSrcMap[VSE_SIG_IDX_bVCU_12DFlt].bSigFltInjFlg=VSE_Inj_Flg_bVCU_12DFlt;      VSE_SigSrcMap[VSE_SIG_IDX_bVCU_12DFlt].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_bVCU_242Flt].pCANSigValue=&VSE_RTE_SIG_bVCU_242Flt;         VSE_SigSrcMap[VSE_SIG_IDX_bVCU_242Flt].emSigSrcType=VSE_SIGNAL_SOURCE_bVCU_242Flt;        VSE_SigSrcMap[VSE_SIG_IDX_bVCU_242Flt].pSigFltInjVal=&VSE_Inj_Dft_bVCU_242Flt;      VSE_SigSrcMap[VSE_SIG_IDX_bVCU_242Flt].bSigFltInjFlg=VSE_Inj_Flg_bVCU_242Flt;      VSE_SigSrcMap[VSE_SIG_IDX_bVCU_242Flt].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_bVCU_241Flt].pCANSigValue=&VSE_RTE_SIG_bVCU_241Flt;         VSE_SigSrcMap[VSE_SIG_IDX_bVCU_241Flt].emSigSrcType=VSE_SIGNAL_SOURCE_bVCU_241Flt;        VSE_SigSrcMap[VSE_SIG_IDX_bVCU_241Flt].pSigFltInjVal=&VSE_Inj_Dft_bVCU_241Flt;      VSE_SigSrcMap[VSE_SIG_IDX_bVCU_241Flt].bSigFltInjFlg=VSE_Inj_Flg_bVCU_241Flt;      VSE_SigSrcMap[VSE_SIG_IDX_bVCU_241Flt].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_bVCU_251Flt].pCANSigValue=&VSE_RTE_SIG_bVCU_251Flt;         VSE_SigSrcMap[VSE_SIG_IDX_bVCU_251Flt].emSigSrcType=VSE_SIGNAL_SOURCE_bVCU_251Flt;        VSE_SigSrcMap[VSE_SIG_IDX_bVCU_251Flt].pSigFltInjVal=&VSE_Inj_Dft_bVCU_251Flt;      VSE_SigSrcMap[VSE_SIG_IDX_bVCU_251Flt].bSigFltInjFlg=VSE_Inj_Flg_bVCU_251Flt;      VSE_SigSrcMap[VSE_SIG_IDX_bVCU_251Flt].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_bIPB_122Flt].pCANSigValue=&VSE_RTE_SIG_bIPB_122Flt;         VSE_SigSrcMap[VSE_SIG_IDX_bIPB_122Flt].emSigSrcType=VSE_SIGNAL_SOURCE_bIPB_122Flt;        VSE_SigSrcMap[VSE_SIG_IDX_bIPB_122Flt].pSigFltInjVal=&VSE_Inj_Dft_bIPB_122Flt;      VSE_SigSrcMap[VSE_SIG_IDX_bIPB_122Flt].bSigFltInjFlg=VSE_Inj_Flg_bIPB_122Flt;      VSE_SigSrcMap[VSE_SIG_IDX_bIPB_122Flt].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_bIPB_321Flt].pCANSigValue=&VSE_RTE_SIG_bIPB_321Flt;         VSE_SigSrcMap[VSE_SIG_IDX_bIPB_321Flt].emSigSrcType=VSE_SIGNAL_SOURCE_bIPB_321Flt;        VSE_SigSrcMap[VSE_SIG_IDX_bIPB_321Flt].pSigFltInjVal=&VSE_Inj_Dft_bIPB_321Flt;      VSE_SigSrcMap[VSE_SIG_IDX_bIPB_321Flt].bSigFltInjFlg=VSE_Inj_Flg_bIPB_321Flt;      VSE_SigSrcMap[VSE_SIG_IDX_bIPB_321Flt].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_bIPB_123Flt].pCANSigValue=&VSE_RTE_SIG_bIPB_123Flt;         VSE_SigSrcMap[VSE_SIG_IDX_bIPB_123Flt].emSigSrcType=VSE_SIGNAL_SOURCE_bIPB_123Flt;        VSE_SigSrcMap[VSE_SIG_IDX_bIPB_123Flt].pSigFltInjVal=&VSE_Inj_Dft_bIPB_123Flt;      VSE_SigSrcMap[VSE_SIG_IDX_bIPB_123Flt].bSigFltInjFlg=VSE_Inj_Flg_bIPB_123Flt;      VSE_SigSrcMap[VSE_SIG_IDX_bIPB_123Flt].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_bIPB_222Flt].pCANSigValue=&VSE_RTE_SIG_bIPB_222Flt;         VSE_SigSrcMap[VSE_SIG_IDX_bIPB_222Flt].emSigSrcType=VSE_SIGNAL_SOURCE_bIPB_222Flt;        VSE_SigSrcMap[VSE_SIG_IDX_bIPB_222Flt].pSigFltInjVal=&VSE_Inj_Dft_bIPB_222Flt;      VSE_SigSrcMap[VSE_SIG_IDX_bIPB_222Flt].bSigFltInjFlg=VSE_Inj_Flg_bIPB_222Flt;      VSE_SigSrcMap[VSE_SIG_IDX_bIPB_222Flt].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_bEPS_11FFlt].pCANSigValue=&VSE_RTE_SIG_bEPS_11FFlt;         VSE_SigSrcMap[VSE_SIG_IDX_bEPS_11FFlt].emSigSrcType=VSE_SIGNAL_SOURCE_bEPS_11FFlt;        VSE_SigSrcMap[VSE_SIG_IDX_bEPS_11FFlt].pSigFltInjVal=&VSE_Inj_Dft_bEPS_11FFlt;      VSE_SigSrcMap[VSE_SIG_IDX_bEPS_11FFlt].bSigFltInjFlg=VSE_Inj_Flg_bEPS_11FFlt;      VSE_SigSrcMap[VSE_SIG_IDX_bEPS_11FFlt].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_bEPB_218Flt].pCANSigValue=&VSE_RTE_SIG_bEPB_218Flt;         VSE_SigSrcMap[VSE_SIG_IDX_bEPB_218Flt].emSigSrcType=VSE_SIGNAL_SOURCE_bEPB_218Flt;        VSE_SigSrcMap[VSE_SIG_IDX_bEPB_218Flt].pSigFltInjVal=&VSE_Inj_Dft_bEPB_218Flt;      VSE_SigSrcMap[VSE_SIG_IDX_bEPB_218Flt].bSigFltInjFlg=VSE_Inj_Flg_bEPB_218Flt;      VSE_SigSrcMap[VSE_SIG_IDX_bEPB_218Flt].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_bCCU_0F4Flt].pCANSigValue=&VSE_RTE_SIG_bCCU_0F4Flt;         VSE_SigSrcMap[VSE_SIG_IDX_bCCU_0F4Flt].emSigSrcType=VSE_SIGNAL_SOURCE_bCCU_0F4Flt;        VSE_SigSrcMap[VSE_SIG_IDX_bCCU_0F4Flt].pSigFltInjVal=&VSE_Inj_Dft_bCCU_0F4Flt;      VSE_SigSrcMap[VSE_SIG_IDX_bCCU_0F4Flt].bSigFltInjFlg=VSE_Inj_Flg_bCCU_0F4Flt;      VSE_SigSrcMap[VSE_SIG_IDX_bCCU_0F4Flt].emSigDataType = VSE_TYPE_UINT8;
    //0x116预留
    /*
    VSE_SigSrcMap[VSE_SIG_IDX_u8FLWhlBraSts].pCANSigValue=&VSE_RTE_SIG_u8FLWhlBraSts;         VSE_SigSrcMap[VSE_SIG_IDX_u8FLWhlBraSts].emSigSrcType=VSE_SIGNAL_SOURCE_u8FLWhlBraSts;        VSE_SigSrcMap[VSE_SIG_IDX_u8FLWhlBraSts].pSigFltInjVal=&VSE_Inj_Dft_u8FLWhlBraSts;      VSE_SigSrcMap[VSE_SIG_IDX_u8FLWhlBraSts].bSigFltInjFlg=VSE_Inj_Flg_u8FLWhlBraSts;      VSE_SigSrcMap[VSE_SIG_IDX_u8FLWhlBraSts].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_u8FRWhlBraSts].pCANSigValue=&VSE_RTE_SIG_u8FRWhlBraSts;         VSE_SigSrcMap[VSE_SIG_IDX_u8FRWhlBraSts].emSigSrcType=VSE_SIGNAL_SOURCE_u8FRWhlBraSts;        VSE_SigSrcMap[VSE_SIG_IDX_u8FRWhlBraSts].pSigFltInjVal=&VSE_Inj_Dft_u8FRWhlBraSts;      VSE_SigSrcMap[VSE_SIG_IDX_u8FRWhlBraSts].bSigFltInjFlg=VSE_Inj_Flg_u8FRWhlBraSts;      VSE_SigSrcMap[VSE_SIG_IDX_u8FRWhlBraSts].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_u8RLWhlBraSts].pCANSigValue=&VSE_RTE_SIG_u8RLWhlBraSts;         VSE_SigSrcMap[VSE_SIG_IDX_u8RLWhlBraSts].emSigSrcType=VSE_SIGNAL_SOURCE_u8RLWhlBraSts;        VSE_SigSrcMap[VSE_SIG_IDX_u8RLWhlBraSts].pSigFltInjVal=&VSE_Inj_Dft_u8RLWhlBraSts;      VSE_SigSrcMap[VSE_SIG_IDX_u8RLWhlBraSts].bSigFltInjFlg=VSE_Inj_Flg_u8RLWhlBraSts;      VSE_SigSrcMap[VSE_SIG_IDX_u8RLWhlBraSts].emSigDataType = VSE_TYPE_UINT8;
    VSE_SigSrcMap[VSE_SIG_IDX_u8RRWhlBraSts].pCANSigValue=&VSE_RTE_SIG_u8RRWhlBraSts;         VSE_SigSrcMap[VSE_SIG_IDX_u8RRWhlBraSts].emSigSrcType=VSE_SIGNAL_SOURCE_u8RRWhlBraSts;        VSE_SigSrcMap[VSE_SIG_IDX_u8RRWhlBraSts].pSigFltInjVal=&VSE_Inj_Dft_u8RRWhlBraSts;      VSE_SigSrcMap[VSE_SIG_IDX_u8RRWhlBraSts].bSigFltInjFlg=VSE_Inj_Flg_u8RRWhlBraSts;      VSE_SigSrcMap[VSE_SIG_IDX_u8RRWhlBraSts].emSigDataType = VSE_TYPE_UINT8;
    */
    VSE_SigSrcMap[VSE_SIG_IDX_i16FLBrkTqExecu].pCANSigValue=&VSE_RTE_SIG_i16FLBrkTqExecu;         VSE_SigSrcMap[VSE_SIG_IDX_i16FLBrkTqExecu].emSigSrcType=VSE_SIGNAL_SOURCE_i16FLBrkTqExecu;        VSE_SigSrcMap[VSE_SIG_IDX_i16FLBrkTqExecu].pSigFltInjVal=&VSE_Inj_Dft_i16FLBrkTqExecu;      VSE_SigSrcMap[VSE_SIG_IDX_i16FLBrkTqExecu].bSigFltInjFlg=VSE_Inj_Flg_i16FLBrkTqExecu;      VSE_SigSrcMap[VSE_SIG_IDX_i16FLBrkTqExecu].emSigDataType = VSE_TYPE_INT16;
    VSE_SigSrcMap[VSE_SIG_IDX_i16FRBrkTqExecu].pCANSigValue=&VSE_RTE_SIG_i16FRBrkTqExecu;         VSE_SigSrcMap[VSE_SIG_IDX_i16FRBrkTqExecu].emSigSrcType=VSE_SIGNAL_SOURCE_i16FRBrkTqExecu;        VSE_SigSrcMap[VSE_SIG_IDX_i16FRBrkTqExecu].pSigFltInjVal=&VSE_Inj_Dft_i16FRBrkTqExecu;      VSE_SigSrcMap[VSE_SIG_IDX_i16FRBrkTqExecu].bSigFltInjFlg=VSE_Inj_Flg_i16FRBrkTqExecu;      VSE_SigSrcMap[VSE_SIG_IDX_i16FRBrkTqExecu].emSigDataType = VSE_TYPE_INT16;
    VSE_SigSrcMap[VSE_SIG_IDX_i16RLBrkTqExecu].pCANSigValue=&VSE_RTE_SIG_i16RLBrkTqExecu;         VSE_SigSrcMap[VSE_SIG_IDX_i16RLBrkTqExecu].emSigSrcType=VSE_SIGNAL_SOURCE_i16RLBrkTqExecu;        VSE_SigSrcMap[VSE_SIG_IDX_i16RLBrkTqExecu].pSigFltInjVal=&VSE_Inj_Dft_i16RLBrkTqExecu;      VSE_SigSrcMap[VSE_SIG_IDX_i16RLBrkTqExecu].bSigFltInjFlg=VSE_Inj_Flg_i16RLBrkTqExecu;      VSE_SigSrcMap[VSE_SIG_IDX_i16RLBrkTqExecu].emSigDataType = VSE_TYPE_INT16;
    VSE_SigSrcMap[VSE_SIG_IDX_i16RRBrkTqExecu].pCANSigValue=&VSE_RTE_SIG_i16RRBrkTqExecu;         VSE_SigSrcMap[VSE_SIG_IDX_i16RRBrkTqExecu].emSigSrcType=VSE_SIGNAL_SOURCE_i16RRBrkTqExecu;        VSE_SigSrcMap[VSE_SIG_IDX_i16RRBrkTqExecu].pSigFltInjVal=&VSE_Inj_Dft_i16RRBrkTqExecu;      VSE_SigSrcMap[VSE_SIG_IDX_i16RRBrkTqExecu].bSigFltInjFlg=VSE_Inj_Flg_i16RRBrkTqExecu;      VSE_SigSrcMap[VSE_SIG_IDX_i16RRBrkTqExecu].emSigDataType = VSE_TYPE_INT16;


}

/*********************************************************
 * Function Name: VSE_CAN_Sig_Value_Type_Convert
 * Description  : CAN信号数据值类型转换接口，空指针强制转换成对应的数据类型
 * Parameter    
 *   SingalState: 信号状态
 *   pstMapTable：信号MAP表 
 *   u8Index    : 信号索引
 *   u64data    ：获取到的信号数据值
 * return       : null
 *********************************************************/
static inline void VSE_CAN_Sig_Value_Type_Convert(ErrCode SingalState, VSE_CAN_SIGNAL_MAP_t *pstMapTable, uint8_T u8Index, uint64_T u64data)
{
    if (NULL != pstMapTable[u8Index].pCANSigValue)
    {
        switch (pstMapTable[u8Index].emSigDataType)
        {
        case VSE_TYPE_UINT8:
            if(SingalState == (int32_T)ERR_SUCCESS){
                *(uint8_T *)(pstMapTable[u8Index].pCANSigValue) = (uint8_T)u64data;
            }else{
                *(uint8_T *)(pstMapTable[u8Index].pCANSigValue) = (uint8_T)(pstMapTable[u8Index].emInvalidData);
            }
            break;

        case VSE_TYPE_UINT16:
            if(SingalState == (int32_T)ERR_SUCCESS){
                *(uint16_T *)(pstMapTable[u8Index].pCANSigValue) = (uint16_T)u64data;
            }else{
                *(uint16_T *)(pstMapTable[u8Index].pCANSigValue) = (uint16_T)(pstMapTable[u8Index].emInvalidData);
            }
            break;

        case VSE_TYPE_INT16:
            if(SingalState == (int32_T)ERR_SUCCESS){
                *(int16_T *)(pstMapTable[u8Index].pCANSigValue) = (int16_T)u64data;
            }else{
                *(int16_T *)(pstMapTable[u8Index].pCANSigValue) = (int16_T)(pstMapTable[u8Index].emInvalidData);
            }
            break;

        case VSE_TYPE_UINT32:
            if(SingalState == (int32_T)ERR_SUCCESS){
                *(uint32_T *)(pstMapTable[u8Index].pCANSigValue) = (uint32_T)u64data;
            }else{
                *(uint32_T *)(pstMapTable[u8Index].pCANSigValue) = (uint32_T)(pstMapTable[u8Index].emInvalidData);
            }
            break;

        default:
            /*default*/
            break;
        }
    }
}

/*********************************************************
 * Function Name: IMUAxStjudge
 * Description  : IMU信号状态反转
 * Parameter    : void
 * return       : null
 *********************************************************/
static void IMUAxStjudge(void)
{
    if ((VSE_RTE_SIG_bIMUAxSt == 1U))
    {
        VSE_RTE_SIG_bIMUAxSt = 0;
    }
    else
    {
        VSE_RTE_SIG_bIMUAxSt = 1;
    }

    if ((VSE_RTE_SIG_bIMUAySt == 1U))
    {
        VSE_RTE_SIG_bIMUAySt = 0;
    }
    else
    {
        VSE_RTE_SIG_bIMUAySt = 1;
    }

    if ((VSE_RTE_SIG_bIMUAzSt == 1U))
    {
        VSE_RTE_SIG_bIMUAzSt = 0;
    }
    else
    {
        VSE_RTE_SIG_bIMUAzSt = 1;
    }

    if ((VSE_RTE_SIG_bIMUWxSt == 1U))
    {
        VSE_RTE_SIG_bIMUWxSt = 0;
    }
    else
    {
        VSE_RTE_SIG_bIMUWxSt = 1;
    }

    if ((VSE_RTE_SIG_bIMUWySt == 1U))
    {
        VSE_RTE_SIG_bIMUWySt = 0;
    }
    else
    {
        VSE_RTE_SIG_bIMUWySt = 1;
    }

    if ((VSE_RTE_SIG_bIMUWzSt == 1U))
    {
        VSE_RTE_SIG_bIMUWzSt = 0;
    }
    else
    {
        VSE_RTE_SIG_bIMUWzSt = 1;
    }
}

/*********************************************************
 * Function Name: VSE_SigMgrGetSig_Comm
 * Description  : 调用底软接口解析CAN 信号
 * Parameter    : 
 *   *Map_table ：信号MAP表 
 *   u8Index    : 信号索引
 * return       : 信号解析结果
 *********************************************************/
static VSE_SignalErrCode_t VSE_SigMgrGetSig_Comm(const VSE_CAN_SIGNAL_MAP_t *Map_table, uint8_T index)
{
    ErrCode SingalState;
    uint64_T data = 0u;
    uint32_T unStamp = 0u;

    if (NULL == Map_table[index].pCANSigValue)
    {
        return VSE_SIGNAL_POINTER_NULL;
    }
    SingalState = (ErrCode)SigMgrGetSig(Map_table[index].u64CANSigName, &data, NULL, &unStamp);

    (void)VSE_CAN_Sig_Value_Type_Convert(SingalState, Map_table, index, data);

    return VSE_SIGNAL_OK;
}

/*********************************************************
 * Function Name: VSE_CAN_CYCLE_Signal_GET_Handle
 * Description  : 状态观测CAN信号接收句柄，信号通信故障判定
 * Parameter    : void
 * return       : null
 *********************************************************/
void VSE_CAN_CYCLE_Signal_GET_Handle(void)
{
    // 通讯保护信号
    #if(VSE_CANMRX_MONITER_EN_051 == VSE_RTE_ON)
        if(VSE_CanRxMonitorFrameSta(VSE_IDX_0x051)==0u){
            VSE_RTE_SIG_bIMU_051Flt = 0u;
        }else{
            VSE_RTE_SIG_bIMU_051Flt = 1u;
        }
    #endif
    #if(VSE_CANMRX_MONITER_EN_0FC == VSE_RTE_ON)
        if(VSE_CanRxMonitorFrameSta(VSE_IDX_0x0FC)==0u){
            VSE_RTE_SIG_bVCU_0FCFlt = 0u;
        }else{
            VSE_RTE_SIG_bVCU_0FCFlt = 1u;
        }
    #endif
    #if(VSE_CANMRX_MONITER_EN_342 == VSE_RTE_ON)
        if(VSE_CanRxMonitorFrameSta(VSE_IDX_0x342)==0u){
            VSE_RTE_SIG_bVCU_342Flt = 0u;
        }else{
            VSE_RTE_SIG_bVCU_342Flt = 1u;
        }
    #endif
    #if(VSE_CANMRX_MONITER_EN_12D == VSE_RTE_ON)
        if(VSE_CanRxMonitorFrameSta(VSE_IDX_0x12D)==0u){
            VSE_RTE_SIG_bVCU_12DFlt = 0u;
        }else{
            VSE_RTE_SIG_bVCU_12DFlt = 1u;
        }
    #endif
    #if(VSE_CANMRX_MONITER_EN_242 == VSE_RTE_ON)
        if(VSE_CanRxMonitorFrameSta(VSE_IDX_0x242)==0u){
            VSE_RTE_SIG_bVCU_242Flt = 0u;
        }else{
            VSE_RTE_SIG_bVCU_242Flt = 1u;
        }
    #endif
    #if(VSE_CANMRX_MONITER_EN_241 == VSE_RTE_ON)
        if(VSE_CanRxMonitorFrameSta(VSE_IDX_0x241)==0u){
            VSE_RTE_SIG_bVCU_241Flt = 0u;
        }else{
            VSE_RTE_SIG_bVCU_241Flt = 1u;
        }
    #endif
    #if(VSE_CANMRX_MONITER_EN_251 == VSE_RTE_ON)
        if(VSE_CanRxMonitorFrameSta(VSE_IDX_0x251)==0u){
            VSE_RTE_SIG_bVCU_251Flt = 0u;
        }else{
            VSE_RTE_SIG_bVCU_251Flt = 1u;
        }
    #endif
    #if(VSE_CANMRX_MONITER_EN_122 == VSE_RTE_ON)
        if(VSE_CanRxMonitorFrameSta(VSE_IDX_0x122)==0u){
            VSE_RTE_SIG_bIPB_122Flt = 0u;
        }else{
            VSE_RTE_SIG_bIPB_122Flt = 1u;
        }
    #endif
    #if(VSE_CANMRX_MONITER_EN_321 == VSE_RTE_ON)
        if(VSE_CanRxMonitorFrameSta(VSE_IDX_0x321)==0u){
            VSE_RTE_SIG_bIPB_321Flt = 0u;
        }else{
            VSE_RTE_SIG_bIPB_321Flt = 1u;
        }
    #endif
    #if(VSE_CANMRX_MONITER_EN_123 == VSE_RTE_ON)
        if(VSE_CanRxMonitorFrameSta(VSE_IDX_0x123)==0u){
            VSE_RTE_SIG_bIPB_123Flt = 0u;
        }else{
            VSE_RTE_SIG_bIPB_123Flt = 1u;
        }
    #endif
    #if(VSE_CANMRX_MONITER_EN_222 == VSE_RTE_ON)
        if(VSE_CanRxMonitorFrameSta(VSE_IDX_0x222)==0u){
            VSE_RTE_SIG_bIPB_222Flt = 0u;
        }else{
            VSE_RTE_SIG_bIPB_222Flt = 1u;
        }
    #endif
    #if(VSE_CANMRX_MONITER_EN_11F == VSE_RTE_ON)
        if(VSE_CanRxMonitorFrameSta(VSE_IDX_0x11F)==0u){
            VSE_RTE_SIG_bEPS_11FFlt = 0u;
        }else{
            VSE_RTE_SIG_bEPS_11FFlt = 1u;
        }
    #endif
    #if(VSE_CANMRX_MONITER_EN_218 == VSE_RTE_ON)
        if(VSE_CanRxMonitorFrameSta(VSE_IDX_0x218)==0u){
            VSE_RTE_SIG_bEPB_218Flt = 0u;
        }else{
            VSE_RTE_SIG_bEPB_218Flt = 1u;
        }
    #endif
    #if(VSE_CANMRX_MONITER_EN_0F4 == VSE_RTE_ON)
        if(VSE_CanRxMonitorFrameSta(VSE_IDX_0x0F4)==0u){
            VSE_RTE_SIG_bCCU_0F4Flt = 0u;
        }else{
            VSE_RTE_SIG_bCCU_0F4Flt = 1u;
        }
    #endif

    // 信号解析
    VSE_CAN_SIGNAL_MAP_t *p_VSE_CAN_SIGNAL_MAP_CYCLE = CAN_SIGNAL_MAP_CYCLE;
    for (uint8_T index = 0u; index < CAN_SIG_CYCLE_NUM; index++)
    {
        (void)VSE_SigMgrGetSig_Comm(p_VSE_CAN_SIGNAL_MAP_CYCLE, index);
    }

    // 有效位信号取反(与模型定义不同)
    IMUAxStjudge();
}

/*********************************************************
 * Function Name: VSE_RTE_READ_CAN
 * Description  : VSE输入信号获取
 * Parameter    : 
 *       idx    : 需要获取的信号对应的索引  
 *   *g_VSE_In_Sig_Addr:获取到信号的地址
 * return       : null
 *********************************************************/
void VSE_RTE_READ_CAN(VSE_SIG_IDXS_t idx, void *g_VSE_In_Sig_Addr){
    // 根据故障注入标志位，返回故障注入设定值或正常解析值
   if ((NULL != VSE_SigSrcMap[idx].pSigFltInjVal)&&(NULL != VSE_SigSrcMap[idx].pCANSigValue))
   {
    switch (VSE_SigSrcMap[idx].emSigDataType)
    {
    case VSE_TYPE_UINT8:
        if(VSE_SigSrcMap[idx].bSigFltInjFlg == true){
            *(uint8_T *)(VSE_SigSrcMap[idx].pCANSigValue) = *(uint8_T *)(VSE_SigSrcMap[idx].pSigFltInjVal);
        }
        *(uint8_T *)(g_VSE_In_Sig_Addr) = *(uint8_T *)(VSE_SigSrcMap[idx].pCANSigValue);
        break;

    case VSE_TYPE_UINT16:
        if(VSE_SigSrcMap[idx].bSigFltInjFlg == true){
            *(uint16_T *)(VSE_SigSrcMap[idx].pCANSigValue) = *(uint16_T *)(VSE_SigSrcMap[idx].pSigFltInjVal);
        }
        *(uint16_T *)(g_VSE_In_Sig_Addr) = *(uint16_T *)(VSE_SigSrcMap[idx].pCANSigValue);
        break;

    case VSE_TYPE_INT16:
        if(VSE_SigSrcMap[idx].bSigFltInjFlg == true){
            *(int16_T *)(VSE_SigSrcMap[idx].pCANSigValue) = *(int16_T *)(VSE_SigSrcMap[idx].pSigFltInjVal);
        }
        *(int16_T *)(g_VSE_In_Sig_Addr) = *(int16_T *)(VSE_SigSrcMap[idx].pCANSigValue);
        break;

    case VSE_TYPE_UINT32:
        if(VSE_SigSrcMap[idx].bSigFltInjFlg == true){
            *(uint32_T *)(VSE_SigSrcMap[idx].pCANSigValue) = *(uint32_T *)(VSE_SigSrcMap[idx].pSigFltInjVal);
        }
        *(uint32_T *)(g_VSE_In_Sig_Addr) = *(uint32_T *)(VSE_SigSrcMap[idx].pCANSigValue);
        break;

    default:
        /*default*/
        break;
    }
   }
}

