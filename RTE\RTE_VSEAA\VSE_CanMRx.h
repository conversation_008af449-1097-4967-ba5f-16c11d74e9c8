﻿#ifndef VSE_CANMRX_H_
#define VSE_CANMRX_H_

#include "stdbool.h"
#include "can_server_cfg.h"
#include "SignalMgr.h"
#include "comtype.h"
#include "VSE_CanMRx_Moniter.h"
#include "SigMgr.h"
#include "VSE_SignalInRTE.h"
#include "VSE_CanMRx.h"
#include "VSE_APPInterface.h"




#if (CAN_PROTOCOL_TPYE == CANFD_TYPE)
#define  CAN_SIG_CYCLE_NUM  69u
#define  CAN_SIG_EVENT_NUM  2u
#else
#define  CAN_SIG_CYCLE_NUM  58u
#define  CAN_SIG_EVENT_NUM  12u
#endif

typedef enum
{
    VSE_SIGNAL_OK    = 0U,
    VSE_SIGNAL_ERROR,
    VSE_SIGNAL_POINTER_NOTNULL,
    VSE_SIGNAL_POINTER_NULL
}VSE_SignalErrCode_t;

/*node state value*/
 typedef enum OnOffLine
 {
    VSE_ON_LINE  = 0U,
     VSE_OFF_LINE = 1U,
     VSE_RESERVE  = 2U,
 }VSE_OnOffLine_t;

/*node name*/
typedef enum
{
  VSE_IPB_ESP = 0U,
  VSE_Left_BCM,
  VSE_MEDIA,
  VSE_Right_BCM,
  VSE_VCU,
  VSE_EPS,
  VSE_SCU,
  VSE_ADAS,
  VSE_IPB_SPEED,
  VSE_SRS,
  VSE_CYCLE_NEED_ONOFF_CHECK,
  VSE_CCU,
  VSE_LTE4G,
  VSE_SCPA,
  VSE_ECM,
  VSE_MPC,
  VSE_DMS,
  VSE_BMS,
  VSE_IPB,
  VSE_EPB,
  VSE_EVENT_NODE, 
  VSE_MEDIA_EVENT, // changed by wsq
  VSE_CAN_NODES_MAX
} VSE_CAN_NODES_t;

/*Signal Idx*/
typedef enum
{
  VSE_SIG_IDX_u16IMUAx, 
  VSE_SIG_IDX_u16IMUAy, 
  VSE_SIG_IDX_u16IMUAz, 
  VSE_SIG_IDX_u16IMUWx, 
  VSE_SIG_IDX_u16IMUWy, 
  VSE_SIG_IDX_u16IMUWz, 
  VSE_SIG_IDX_bIMUAxSt,
  VSE_SIG_IDX_bIMUAySt,
  VSE_SIG_IDX_bIMUAzSt,
  VSE_SIG_IDX_bIMUWxSt,
  VSE_SIG_IDX_bIMUWySt,
  VSE_SIG_IDX_bIMUWzSt,
  VSE_SIG_IDX_i16VehTqFL,
  VSE_SIG_IDX_i16VehTqFR,
  VSE_SIG_IDX_i16VehTqRL,
  VSE_SIG_IDX_i16VehTqRR,
  VSE_SIG_IDX_bTqStsFL,
  VSE_SIG_IDX_bTqStsFR,
  VSE_SIG_IDX_bTqStsRL,
  VSE_SIG_IDX_bTqStsRR,
  VSE_SIG_IDX_u16FrntMotTq,
  VSE_SIG_IDX_u16ReMotTq,
  VSE_SIG_IDX_bFrntMotTqSts,
  VSE_SIG_IDX_bReMotTqSts,
  VSE_SIG_IDX_u16FLWhlSpd,
  VSE_SIG_IDX_u16FRWhlSpd,
  VSE_SIG_IDX_u16RLWhlSpd,
  VSE_SIG_IDX_u16RRWhlSpd,
  VSE_SIG_IDX_bWhlSpdFLSt,
  VSE_SIG_IDX_bWhlSpdFRSt,
  VSE_SIG_IDX_bWhlSpdRLSt,
  VSE_SIG_IDX_bWhlSpdRRSt,
  VSE_SIG_IDX_u8AccrPedlRate,
  VSE_SIG_IDX_bAccrPedlRateFlg,
  VSE_SIG_IDX_u8IPBBrkSts,
  VSE_SIG_IDX_u8BrkDepth,
  VSE_SIG_IDX_bBrkDepthSts,
  VSE_SIG_IDX_u16IPBPPrs,
  VSE_SIG_IDX_bIPBPluPreSts,
  VSE_SIG_IDX_u8GearPosn,
  VSE_SIG_IDX_bGearSts,
  VSE_SIG_IDX_i16SteerAg,
  VSE_SIG_IDX_u16SteerAgSpd,
  VSE_SIG_IDX_bSteerAgSnsSt,
  VSE_SIG_IDX_bSteerAgCASnsSt,
  VSE_SIG_IDX_u16RWhlSteerAg,
  VSE_SIG_IDX_bRWhlSteerAgSts,
  VSE_SIG_IDX_bTCSActS,
  VSE_SIG_IDX_bTCSFlt, 
  VSE_SIG_IDX_bABSActS,
  VSE_SIG_IDX_bABSFlt, 
  VSE_SIG_IDX_bVDCActS,
  VSE_SIG_IDX_bVDCFlt, 
  VSE_SIG_IDX_u8VehDrvMod,
  VSE_SIG_IDX_u8PwrGear, 
  VSE_SIG_IDX_u8EPBSt,   
  VSE_SIG_IDX_u16DamprPosnFL,
  VSE_SIG_IDX_u16DamprPosnFR,
  VSE_SIG_IDX_u16DamprPosnRL,
  VSE_SIG_IDX_u16DamprPosnRR,
  VSE_SIG_IDX_bDamprPosnFLSts,
  VSE_SIG_IDX_bDamprPosnFRSts,
  VSE_SIG_IDX_bDamprPosnRLSts,
  VSE_SIG_IDX_bDamprPosnRRSts,
  VSE_SIG_IDX_u16FLActualCurrent,
  VSE_SIG_IDX_u16FRActualCurrent,
  VSE_SIG_IDX_u16RLActualCurrent,
  VSE_SIG_IDX_u16RRActualCurrent,
  VSE_SIG_IDX_u8DiSusModExeSts,
  VSE_SIG_IDX_u8DiSus_Type,
  VSE_SIG_IDX_bDiSusHeiAdjSts,
  VSE_SIG_IDX_u8DiSusHeiAdjProc,
  VSE_SIG_IDX_bDiSusHeiAdjFltSts,
  VSE_SIG_IDX_u16CmpActIFL,
  VSE_SIG_IDX_u16StchActIFL,
  VSE_SIG_IDX_u16CmpActIFR,
  VSE_SIG_IDX_u16StchActIFR,
  VSE_SIG_IDX_u16CmpActIRL,
  VSE_SIG_IDX_u16StchActIRL,
  VSE_SIG_IDX_u16CmpActIRR,
  VSE_SIG_IDX_u16StchActIRR,
  VSE_SIG_IDX_u16EstimdFFL,
  VSE_SIG_IDX_u16EstimdFFR,
  VSE_SIG_IDX_u16EstimdFRL,
  VSE_SIG_IDX_u16EstimdFRR,
  VSE_SIG_IDX_i16DamprWhlHFL,
  VSE_SIG_IDX_i16DamprWhlHFR,
  VSE_SIG_IDX_i16DamprWhlHRL,
  VSE_SIG_IDX_i16DamprWhlHRR,
  VSE_SIG_IDX_SNS_u16FLWhlSpd,
  VSE_SIG_IDX_SNS_u16FRWhlSpd,
  VSE_SIG_IDX_SNS_u16RLWhlSpd,
  VSE_SIG_IDX_SNS_u16RRWhlSpd,
  VSE_SIG_IDX_SNS_bWhlSpdFLSt,
  VSE_SIG_IDX_SNS_bWhlSpdFRSt,
  VSE_SIG_IDX_SNS_bWhlSpdRLSt,
  VSE_SIG_IDX_SNS_bWhlSpdRRSt,
  VSE_SIG_IDX_u16CarType,
  VSE_SIG_IDX_u8CarConfig,
  VSE_SIG_IDX_bIMU_051Flt,
  VSE_SIG_IDX_bVCU_0FCFlt,
  VSE_SIG_IDX_bVCU_342Flt,
  VSE_SIG_IDX_bVCU_12DFlt,
  VSE_SIG_IDX_bVCU_242Flt,
  VSE_SIG_IDX_bVCU_241Flt,
  VSE_SIG_IDX_bVCU_251Flt,
  VSE_SIG_IDX_bIPB_122Flt,
  VSE_SIG_IDX_bIPB_321Flt,
  VSE_SIG_IDX_bIPB_123Flt,
  VSE_SIG_IDX_bIPB_222Flt,
  VSE_SIG_IDX_bEPS_11FFlt,
  VSE_SIG_IDX_bEPB_218Flt,
  VSE_SIG_IDX_bCCU_0F4Flt,
  VSE_SIG_IDX_u8FLWhlBraSts,
  VSE_SIG_IDX_u8FRWhlBraSts,
  VSE_SIG_IDX_u8RLWhlBraSts,
  VSE_SIG_IDX_u8RRWhlBraSts,
  VSE_SIG_IDX_i16FLBrkTqExecu,
  VSE_SIG_IDX_i16FRBrkTqExecu,
  VSE_SIG_IDX_i16RLBrkTqExecu,
  VSE_SIG_IDX_i16RRBrkTqExecu,
  VSE_SIG_IDX_NUM,
} VSE_SIG_IDXS_t;

/*CAN 信号数据类型定义*/
typedef enum
{
  VSE_TYPE_UINT8 = 1u,//uint8 or boolean both unsigned char
  VSE_TYPE_UINT16,
  VSE_TYPE_INT16,
  VSE_TYPE_UINT32,
  VSE_TYPE_FLOAT,
} VSE_DATA_TYPE_t;

/*CAN 节点掉线无效值类型定义*/
typedef enum
{
  VSE_INVALID_ZERO = 0x0,
  VSE_INVALID_FAIL = 0x1,
  VSE_INVALID_NULL = 0x2, //不处理 维持原来的值
  VSE_INVALID_ERR  = 0x3,
  VSE_INVALID_00FE = 0xFE,
  VSE_INVALID_00FF = 0xFF,
  VSE_INVALID_0FFF = 0xFFF,
  VSE_INVALID_0064= 0x64,
  VSE_INVALID_IMU_A = 0x61A8,
  VSE_INVALID_IMU_W = 0x7530, 
  VSE_INVALID_7FFF = 0x7FFF,
  VSE_INVALID_FFFF = 0xFFFF,
  VSE_INVALID_00C8 = 0x00C8,
} VSE_INVALID_DATA_TYPE_t;

/*CAN 信号匹配索引表*/
typedef struct
{
  // uint16_t u16CAN_ID;
	VSE_CAN_NODES_t emNodesName;
  uint64_t u64CANSigName;
  void *pCANSigValue;
  VSE_DATA_TYPE_t emSigDataType;
  VSE_INVALID_DATA_TYPE_t emInvalidData;
  boolean bNeed_NodesOnOff;
} VSE_CAN_SIGNAL_MAP_t;

/*VSE 输入信号索引*/
typedef struct
{
	// VSE_SIG_IDXS_t emSigIdx;
  void *pCANSigValue;
  void *pSigFltInjVal;
  boolean bSigFltInjFlg;
  VSE_DATA_TYPE_t emSigDataType;
  VSE_SIG_SRC_TYPE_t emSigSrcType;
} VSE_SIG_SRC_MAP_t;

extern void VSE_SigSrcMapInit(void);
extern void VSE_CAN_CYCLE_Signal_GET_Handle(void);
extern void VSE_RTE_READ_CAN(VSE_SIG_IDXS_t idx, void *g_VSE_In_Sig_Addr);

#endif
