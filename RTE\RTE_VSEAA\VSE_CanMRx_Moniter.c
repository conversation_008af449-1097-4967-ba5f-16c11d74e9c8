#include"VSE_CanMRx_Moniter.h"

/*********************************************************
 * Function Name: ObvCanIdCrc
 * Description  : Monitor can id crc/checksum status
 * Parameter
 *   @Map       : Can id map
 *   @i32SigSt  : VSE_RTE_ERRCode status
 * return       : null
 *********************************************************/
static void VSE_ObvCanIdCrc(VSE_CanRxMonitorFrame_t*Map,int32_T i32SigSt)
{
    /* checksum/crc enable */
    if(Map->CanIdCrc.En == VSE_RTE_ON)
    {
        if(i32SigSt == (int32_T)ERR_SUCCESS)
        {
            Map->CanIdCrc.OKCnt++;
            Map->CanIdCrc.ErrCnt = 0u;
            if(Map->CanIdCrc.OKCnt >= Map->CanIdCrc.OKSet)
            {
                Map->CanIdCrc.Sta  = VSE_RTE_OK;
            }
        }
        else if(i32SigSt == (int32_T)ERR_SIGMGR_CRCERR)
        {
            Map->CanIdCrc.ErrCnt++;
           
            Map->CanIdCrc.OKCnt = 0u;
            if(Map->CanIdCrc.ErrCnt >= Map->CanIdCrc.ErrSet)
            {
                Map->CanIdCrc.Sta  = VSE_RTE_ERR;
            }
        }else{
            /* do nothing */
        }
    }
}
/*********************************************************
 * Function Name: ObvCanIdLine
 * Description  : Monitor can id on/off line status
 * Parameter
 *   @Map       : Can id map
 *   @i32SigSt  : VSE_RTE_ERRCode status
 *   @u32Time   : Can sig timestamp
 * return       : null
 *********************************************************/
static void VSE_ObvCanIdLine(VSE_CanRxMonitorFrame_t*Map, int32_T i32SigSt, uint32_T u32Time)
{
    /* Enable */
    if(Map->CanIdLine.En == VSE_RTE_ON)
    {
        uint32_T CycleTime = Map->CanIdLine.CycleRecv * Map->CanIdLine.SubIdCnt;
        /* can line status */
        switch(Map->CanIdLine.Sta)
        {
            /* if on line */
            case VSE_RTE_OK:
                /* do off line test */
                if((i32SigSt == (int32_T)ERR_SUCCESS) || (i32SigSt == (int32_T)ERR_SIGMGR_OFFLINE))
                {
                    /* timestamp > total time */
                    if(u32Time >= (uint32_T)(CycleTime * Map->CanIdLine.SetCycleOffline))
                    {
                        Map->CanIdLine.Sta = VSE_RTE_ERR;
                    }
                }
                break;
            /* is off line */
            case VSE_RTE_ERR:
                /* do on line test */
                switch(i32SigSt)
                {
                    case (int32_T)ERR_SUCCESS:
                        /* 时间戳小于周期 */
                        if(u32Time <= CycleTime){
                            Map->CanIdLine.CycleRecoCnt ++;   
                        /* 时间戳 > 接收周期+任务周期*/ 
                        }else if(u32Time > (CycleTime * 2u)){
                            Map->CanIdLine.CycleRecoCnt = 0u;
                        }
                        else{
                            /* do nothing */                            
                        }
                        if(Map->CanIdLine.CycleRecoCnt > Map->CanIdLine.SetCycleReco){
                            Map->CanIdLine.CycleRecoCnt = 0u;
                            Map->CanIdLine.Sta = VSE_RTE_OK;
                        }
                        break;
                    default:
                        /* do nothing */
                        break;
                }
                break;
            default:
                /* do nothing */
                break;
        }
    }
}

static void VSE_ObvCanIdTimeOut(VSE_CanRxMonitorFrame_t*Map,int32_T i32SigSt, uint32_T u32Time)
{
    /* Enable */
    if(Map->CanIdTimeOut.En == VSE_RTE_ON)
    {
        uint32_T CycleTime = Map->CanIdTimeOut.CycleRecv * Map->CanIdTimeOut.SubIdCnt;
        /* can line status */
        switch(Map->CanIdTimeOut.Sta)
        {
            /* if on line */
            case VSE_RTE_OK:
                /* do off line test */
                if((i32SigSt == (int32_T)ERR_SUCCESS) || (i32SigSt == (int32_T)ERR_SIGMGR_OFFLINE))
                {
                    /* timestamp > total time */
                    if(u32Time >= (uint32_T)(CycleTime * Map->CanIdTimeOut.SetCycleOffline))
                    {
                        Map->CanIdTimeOut.Sta = VSE_RTE_ERR;
                    }
                }
                break;
            /* is off line */
            case VSE_RTE_ERR:
                /* do on line test */
                switch(i32SigSt)
                {
                    case (int32_T)ERR_SUCCESS:
                        /* 时间戳小于周期 */
                        if(u32Time <= CycleTime){
                            Map->CanIdTimeOut.CycleRecoCnt ++;   
                        /* 时间戳 > 接收周期+任务周期*/ 
                        }else if(u32Time > (CycleTime * 2u)){
                            Map->CanIdTimeOut.CycleRecoCnt = 0u;
                        }
                        else{
                            /* do nothing */                            
                        }
                        if(Map->CanIdTimeOut.CycleRecoCnt > Map->CanIdTimeOut.SetCycleReco){
                            Map->CanIdTimeOut.CycleRecoCnt = 0u;
                            Map->CanIdTimeOut.Sta = VSE_RTE_OK;
                        }
                        break;
                    default:
                        /* do nothing */
                        break;
                }
                break;
            default:
                /* do nothing */
                break;
        }
    }
}

/*********************************************************
 * Function Name: CanIdMapAnalyze
 * Description  : Analyze can id map
 * Parameter
 *   @Map       : Can id map
 * return       : null
 *********************************************************/
static void VSE_CanIdMapAnalyze(VSE_CanRxMonitorFrame_t*Map, uint32_T Stamp)
{
    uint32_T  u32NodeSigTimeStamp = 0u;
    uint64_T  data = 0u;

    if (Map != VSE_RTE_NULL)
    {
        if(Map->CanIdLine.CycleRecv != 0u){
            if((Stamp % Map->CanIdLine.CycleRecv) == 0u)
            {
                /* Get sig value */
                Map->SigRet = (int32_T)SigMgrGetSig(Map->Sig, &data, (PrecisionInfo *)VSE_RTE_NULL, &u32NodeSigTimeStamp);    
				VSE_CanMRx_SetCanLineStatus(VSE_RTE_ON);
                /* check on/off line */
                VSE_ObvCanIdLine(Map, Map->SigRet, u32NodeSigTimeStamp);
                /* check crc */
                VSE_ObvCanIdCrc(Map, Map->SigRet);
                /* time out */
                VSE_ObvCanIdTimeOut(Map, Map->SigRet, u32NodeSigTimeStamp);
            }else{
                VSE_CanMRx_SetCanLineStatus(VSE_RTE_OFF);
            }
        }            
    }
}

uint8_T VSE_RTE_CAN_LINE_STATUS = VSE_RTE_OFF;

void VSE_CanMRx_SetCanLineStatus(uint8_T Status)
{
   VSE_RTE_CAN_LINE_STATUS = Status;
}

uint32_T VSE_CanRxMonitorCycle(uint32_T Index)
{
    return VSE_CanIdMap[Index].CanIdLine.CycleRecv;
}

/*********************************************************
 * Function Name: CanRxMonitorFrameSta
 * Description  : get can id parse result
 * Parameter
 *   @Gloup     : Can id map -> Index
 * return       : 
 *             0：success
 *             1：failed
 *********************************************************/
uint8_T VSE_CanRxMonitorFrameSta(uint32_T Index)
{
    uint8_T ErrRet = 0u;

    if(VSE_CanIdMap[Index].CanSigRoc.En == VSE_RTE_ON) {
        ErrRet |= (uint8_T)VSE_CanIdMap[Index].CanSigRoc.Sta; 
    }

    if(VSE_CanIdMap[Index].CanIdCrc.En == VSE_RTE_ON ) {
        ErrRet |= (uint8_T)VSE_CanIdMap[Index].CanIdCrc.Sta; 
    }

    if(VSE_CanIdMap[Index].CanIdLine.En == VSE_RTE_ON) {
       ErrRet |= (uint8_T)VSE_CanIdMap[Index].CanIdLine.Sta;
    }

    if(VSE_CanIdMap[Index].CanIdTimeOut.En == VSE_RTE_ON){
        ErrRet |= (uint8_T)VSE_CanIdMap[Index].CanIdTimeOut.Sta; 
    }
    return ErrRet;
}
/*********************************************************
 * Function Name: CanRxMonitorFrameDeal
 * Description  : Periodic traversal of the canid table.
 *                cyclic call.
 * Parameter    TaskCycle
 * return       : null
 *********************************************************/
void VSE_CanRxMonitorFrameDeal(uint32_T TaskCycle)
{
    uint8_T index;
    static uint32_T CanMonitorStamp=0u;

    for(index = 0u; index < (uint8_T)VSE_CAN_ID_MAP_NUM; index++)
    {
		VSE_CanIdMapAnalyze(&VSE_CanIdMap[index], CanMonitorStamp);
    }
    CanMonitorStamp += TaskCycle;
}

/*********************************************************
 * Function Name: CanRxMonitorGetCrc
 * Description  : Get crc/checksum status of can id
 * Parameter
 *   @id        : Can index
 * return       : Can id crc/checksum status
 *********************************************************/
VSE_RTE_STATUS_t VSE_CanRxMonitorGetCrc(VSE_CANMRX_IDX_t idx)
{
	VSE_RTE_STATUS_t  ret   =VSE_RTE_ERR;
    ret = VSE_CanIdMap[idx].CanIdCrc.Sta;
    return ret;
}

/*********************************************************
 * Function Name: CanRxMonitorGetTimeOut
 * Description  : Get timeout status of can id
 * Parameter
 *   @id        : Can index
 * return       : Can id timeout status
 *********************************************************/
VSE_RTE_STATUS_t VSE_CanRxMonitorGetTimeOut(VSE_CANMRX_IDX_t idx)
{
	VSE_RTE_STATUS_t  ret   = VSE_RTE_ERR;
    ret = VSE_CanIdMap[idx].CanIdTimeOut.Sta;
    return ret;
}

/*********************************************************
 * Function Name: CanRxMonitorGetRoc
 * Description  : Get rolling counter status of can id
 * Parameter
 *   @id        : Can index
 * return       : Can id rolling counter status
 *********************************************************/
VSE_RTE_STATUS_t VSE_CanRxMonitorGetRoc(VSE_CANMRX_IDX_t idx)
{
	VSE_RTE_STATUS_t  ret   = VSE_RTE_ERR;
    ret = VSE_CanIdMap[idx].CanSigRoc.Sta;  
    return ret;
}

/*********************************************************
 * Function Name: CanRxMonitorGetLine
 * Description  : Get on/off line status of can id
 * Parameter
 *   @id        : Can index
 * return       : Can id on/off line status
 *********************************************************/
VSE_RTE_STATUS_t VSE_CanRxMonitorGetLine(VSE_CANMRX_IDX_t idx)
{
	VSE_RTE_STATUS_t  ret   = VSE_RTE_ERR;
    ret = VSE_CanIdMap[idx].CanIdLine.Sta;
    return ret;
}
