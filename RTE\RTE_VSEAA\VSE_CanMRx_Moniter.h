#ifndef VSE_CANRX_MONITOR_H_
#define VSE_CANRX_MONITOR_H_
#include "VSE_CanMRx_MonitorFrameConfMap.h"

#include "VSE_RteCfg.h"

void VSE_CanMRx_SetCanLineStatus(uint8_T Status);


uint32_T VSE_CanRxMonitorCycle(uint32_T Index);

/*********************************************************
 * Description  : get can id parse result
 * Parameter
 *   @Gloup     : Can id map -> Gloup
 * return       : 
 *             0：success
 *             1：failed
 *********************************************************/
uint8_T VSE_CanRxMonitorFrameSta(uint32_T Index);

/*********************************************************
 * Function Name: CanRxMonitorFrameDeal
 * Description  : Periodic traversal of the canid table.
 *                cyclic call.
 * Parameter    TaskCycle
 * return       : null
 *********************************************************/
void VSE_CanRxMonitorFrameDeal(uint32_T TaskCycle);

/*********************************************************
 * Function Name: CanRxMonitorGetCrc
 * Description  : Get crc/checksum status of can id
 * Parameter
 *   @id        : Can index
 * return       : Can id crc/checksum status
 *********************************************************/
VSE_RTE_STATUS_t VSE_CanRxMonitorGetCrc(VSE_CANMRX_IDX_t idx);

/*********************************************************
 * Function Name: CanRxMonitorGetTimeOut
 * Description  : Get timeout status of can id
 * Parameter
 *   @id        : Can index
 * return       : Can id timeout status
 *********************************************************/
VSE_RTE_STATUS_t VSE_CanRxMonitorGetTimeOut(VSE_CANMRX_IDX_t idx);

/*********************************************************
 * Function Name: CanRxMonitorGetRoc
 * Description  : Get rolling counter status of can id
 * Parameter
 *   @id        : Can index
 * return       : Can id rolling counter status
 *********************************************************/
VSE_RTE_STATUS_t VSE_CanRxMonitorGetRoc(VSE_CANMRX_IDX_t idx);

/*********************************************************
 * Function Name: CanRxMonitorGetLine
 * Description  : Get on/off line status of can id
 * Parameter
 *   @id        : Can index
 * return       : Can id on/off line status
 *********************************************************/
VSE_RTE_STATUS_t VSE_CanRxMonitorGetLine(VSE_CANMRX_IDX_t idx);



#endif
