#include"VSE_CanMRx_MonitorFrameConfMap.h"

VSE_CanRxMonitorFrame_t  VSE_CanIdMap[VSE_CAN_ID_MAP_NUM]={
    #if(VSE_CANMRX_MONITER_EN_051 == VSE_RTE_ON)
        {.Index=(uint32_T)VSE_IDX_0x051, .Id=(uint32_T)0x051, .Sig=(uint64_T)Rolling_Counter_051_S, .SigRet=(int32_T)0u, .CanSigRoc={.En=0u,.CanType=ROC_BIT_4 ,.ErrSet=7u,.OKSet=1u}, .CanIdCrc={.En=0u,.ErrSet=7u,.OKSet=1u},.CanIdLine={.En=1u,.SubIdCnt = 1u,.CycleRecv=20u  ,.SetCycleOffline=50u ,.SetCycleReco=1u}, .CanIdTimeOut={.En=0u}},
    #endif
    #if(VSE_CANMRX_MONITER_EN_0FC == VSE_RTE_ON)
        {.Index=(uint32_T)VSE_IDX_0x0FC, .Id=(uint32_T)0x0FC, .Sig=(uint64_T)Rolling_Counter_0FC_S, .SigRet=(int32_T)0u, .CanSigRoc={.En=0u,.CanType=ROC_BIT_4 ,.ErrSet=7u,.OKSet=1u}, .CanIdCrc={.En=0u,.ErrSet=7u,.OKSet=1u},.CanIdLine={.En=1u,.SubIdCnt = 1u,.CycleRecv=20u  ,.SetCycleOffline=50u ,.SetCycleReco=1u}, .CanIdTimeOut={.En=0u}},
    #endif
    #if(VSE_CANMRX_MONITER_EN_342 == VSE_RTE_ON)
        {.Index=(uint32_T)VSE_IDX_0x342, .Id=(uint32_T)0x342, .Sig=(uint64_T)Rolling_Counter_342_S, .SigRet=(int32_T)0u, .CanSigRoc={.En=0u,.CanType=ROC_BIT_4 ,.ErrSet=7u,.OKSet=1u}, .CanIdCrc={.En=0u,.ErrSet=7u,.OKSet=1u},.CanIdLine={.En=1u,.SubIdCnt = 1u,.CycleRecv=20u  ,.SetCycleOffline=50u ,.SetCycleReco=1u}, .CanIdTimeOut={.En=0u}},
    #endif
    #if(VSE_CANMRX_MONITER_EN_12D == VSE_RTE_ON)
        {.Index=(uint32_T)VSE_IDX_0x12D, .Id=(uint32_T)0x12D, .Sig=(uint64_T)Rolling_Counter_12D_S, .SigRet=(int32_T)0u, .CanSigRoc={.En=0u,.CanType=ROC_BIT_4 ,.ErrSet=7u,.OKSet=1u}, .CanIdCrc={.En=0u,.ErrSet=7u,.OKSet=1u},.CanIdLine={.En=1u,.SubIdCnt = 1u,.CycleRecv=50u  ,.SetCycleOffline=125u ,.SetCycleReco=1u}, .CanIdTimeOut={.En=0u}},
    #endif
    #if(VSE_CANMRX_MONITER_EN_242 == VSE_RTE_ON)
        {.Index=(uint32_T)VSE_IDX_0x242, .Id=(uint32_T)0x242, .Sig=(uint64_T)Msg_Cnt_242_S, .SigRet=(int32_T)0u, .CanSigRoc={.En=0u,.CanType=ROC_BIT_4 ,.ErrSet=7u,.OKSet=1u}, .CanIdCrc={.En=0u,.ErrSet=7u,.OKSet=1u},.CanIdLine={.En=1u,.SubIdCnt = 1u,.CycleRecv=20u  ,.SetCycleOffline=50u ,.SetCycleReco=1u}, .CanIdTimeOut={.En=0u}},
    #endif
    #if(VSE_CANMRX_MONITER_EN_241 == VSE_RTE_ON)
        {.Index=(uint32_T)VSE_IDX_0x241, .Id=(uint32_T)0x241, .Sig=(uint64_T)Rolling_Counter_241_S, .SigRet=(int32_T)0u, .CanSigRoc={.En=0u,.CanType=ROC_BIT_4 ,.ErrSet=7u,.OKSet=1u}, .CanIdCrc={.En=0u,.ErrSet=7u,.OKSet=1u},.CanIdLine={.En=1u,.SubIdCnt = 1u,.CycleRecv=20u  ,.SetCycleOffline=50u ,.SetCycleReco=1u}, .CanIdTimeOut={.En=0u}},
    #endif
    #if(VSE_CANMRX_MONITER_EN_251 == VSE_RTE_ON)
        {.Index=(uint32_T)VSE_IDX_0x251, .Id=(uint32_T)0x251, .Sig=(uint64_T)Rolling_Counter_251_S, .SigRet=(int32_T)0u, .CanSigRoc={.En=0u,.CanType=ROC_BIT_4 ,.ErrSet=7u,.OKSet=1u}, .CanIdCrc={.En=0u,.ErrSet=7u,.OKSet=1u},.CanIdLine={.En=1u,.SubIdCnt = 1u,.CycleRecv=20u  ,.SetCycleOffline=50u ,.SetCycleReco=1u}, .CanIdTimeOut={.En=0u}},
    #endif
    #if(VSE_CANMRX_MONITER_EN_122 == VSE_RTE_ON)
        {.Index=(uint32_T)VSE_IDX_0x122, .Id=(uint32_T)0x122, .Sig=(uint64_T)Rolling_Counter_122_S, .SigRet=(int32_T)0u, .CanSigRoc={.En=0u,.CanType=ROC_BIT_4 ,.ErrSet=7u,.OKSet=1u}, .CanIdCrc={.En=0u,.ErrSet=7u,.OKSet=1u},.CanIdLine={.En=1u,.SubIdCnt = 1u,.CycleRecv=20u  ,.SetCycleOffline=50u ,.SetCycleReco=1u}, .CanIdTimeOut={.En=0u}},
    #endif
    #if(VSE_CANMRX_MONITER_EN_321 == VSE_RTE_ON)
        {.Index=(uint32_T)VSE_IDX_0x321, .Id=(uint32_T)0x321, .Sig=(uint64_T)Rolling_Counter_321_S, .SigRet=(int32_T)0u, .CanSigRoc={.En=0u,.CanType=ROC_BIT_4 ,.ErrSet=7u,.OKSet=1u}, .CanIdCrc={.En=0u,.ErrSet=7u,.OKSet=1u},.CanIdLine={.En=1u,.SubIdCnt = 1u,.CycleRecv=20u  ,.SetCycleOffline=50u ,.SetCycleReco=1u}, .CanIdTimeOut={.En=0u}},
    #endif
    #if(VSE_CANMRX_MONITER_EN_123 == VSE_RTE_ON)
        {.Index=(uint32_T)VSE_IDX_0x123, .Id=(uint32_T)0x123, .Sig=(uint64_T)Rolling_Counter_0x123, .SigRet=(int32_T)0u, .CanSigRoc={.En=0u,.CanType=ROC_BIT_4 ,.ErrSet=7u,.OKSet=1u}, .CanIdCrc={.En=0u,.ErrSet=7u,.OKSet=1u},.CanIdLine={.En=1u,.SubIdCnt = 1u,.CycleRecv=20u  ,.SetCycleOffline=50u ,.SetCycleReco=1u}, .CanIdTimeOut={.En=0u}},
    #endif
    #if(VSE_CANMRX_MONITER_EN_222 == VSE_RTE_ON)
        {.Index=(uint32_T)VSE_IDX_0x222, .Id=(uint32_T)0x222, .Sig=(uint64_T)Rolling_Counter_0x222, .SigRet=(int32_T)0u, .CanSigRoc={.En=0u,.CanType=ROC_BIT_4 ,.ErrSet=7u,.OKSet=1u}, .CanIdCrc={.En=0u,.ErrSet=7u,.OKSet=1u},.CanIdLine={.En=1u,.SubIdCnt = 1u,.CycleRecv=20u  ,.SetCycleOffline=50u ,.SetCycleReco=1u}, .CanIdTimeOut={.En=0u}},
    #endif
    #if(VSE_CANMRX_MONITER_EN_11F == VSE_RTE_ON)
        {.Index=(uint32_T)VSE_IDX_0x11F, .Id=(uint32_T)0x11F, .Sig=(uint64_T)Rolling_Counter_11F_S, .SigRet=(int32_T)0u, .CanSigRoc={.En=0u,.CanType=ROC_BIT_4 ,.ErrSet=7u,.OKSet=1u}, .CanIdCrc={.En=0u,.ErrSet=7u,.OKSet=1u},.CanIdLine={.En=1u,.SubIdCnt = 1u,.CycleRecv=10u  ,.SetCycleOffline=25u ,.SetCycleReco=1u}, .CanIdTimeOut={.En=0u}},
    #endif
    #if(VSE_CANMRX_MONITER_EN_218 == VSE_RTE_ON)
        {.Index=(uint32_T)VSE_IDX_0x218, .Id=(uint32_T)0x218, .Sig=(uint64_T)Msg_Cnt_218_S, .SigRet=(int32_T)0u, .CanSigRoc={.En=0u,.CanType=ROC_BIT_4 ,.ErrSet=7u,.OKSet=1u}, .CanIdCrc={.En=0u,.ErrSet=7u,.OKSet=1u},.CanIdLine={.En=1u,.SubIdCnt = 1u,.CycleRecv=20u  ,.SetCycleOffline=50u ,.SetCycleReco=1u}, .CanIdTimeOut={.En=0u}},
    #endif
    #if(VSE_CANMRX_MONITER_EN_0F4 == VSE_RTE_ON)
        {.Index=(uint32_T)VSE_IDX_0x0F4, .Id=(uint32_T)0x0F4, .Sig=(uint64_T)Rolling_Counter_0F4_S, .SigRet=(int32_T)0u, .CanSigRoc={.En=0u,.CanType=ROC_BIT_4 ,.ErrSet=7u,.OKSet=1u}, .CanIdCrc={.En=0u,.ErrSet=7u,.OKSet=1u},.CanIdLine={.En=1u,.SubIdCnt = 1u,.CycleRecv=20u  ,.SetCycleOffline=50u ,.SetCycleReco=1u}, .CanIdTimeOut={.En=0u}},
    #endif
};

#if(VSE_CANMRX_MONITER_EN_051 == VSE_RTE_ON)
    VSE_CAN_SIG_ROC_CONF(VSE_IDX_0x051,0x051);
#endif
#if(VSE_CANMRX_MONITER_EN_0FC == VSE_RTE_ON)
    VSE_CAN_SIG_ROC_CONF(VSE_IDX_0x0FC,0x0FC);
#endif
#if(VSE_CANMRX_MONITER_EN_342 == VSE_RTE_ON)
    VSE_CAN_SIG_ROC_CONF(VSE_IDX_0x342,0x342);
#endif
#if(VSE_CANMRX_MONITER_EN_12D == VSE_RTE_ON)
    VSE_CAN_SIG_ROC_CONF(VSE_IDX_0x12D,0x12D);
#endif
#if(VSE_CANMRX_MONITER_EN_242 == VSE_RTE_ON)
    VSE_CAN_SIG_ROC_CONF(VSE_IDX_0x242,0x242);
#endif
#if(VSE_CANMRX_MONITER_EN_241 == VSE_RTE_ON)
    VSE_CAN_SIG_ROC_CONF(VSE_IDX_0x241,0x241);
#endif
#if(VSE_CANMRX_MONITER_EN_251 == VSE_RTE_ON)
    VSE_CAN_SIG_ROC_CONF(VSE_IDX_0x251,0x251);
#endif
#if(VSE_CANMRX_MONITER_EN_122 == VSE_RTE_ON)
    VSE_CAN_SIG_ROC_CONF(VSE_IDX_0x122,0x122);
#endif
#if(VSE_CANMRX_MONITER_EN_321 == VSE_RTE_ON)
    VSE_CAN_SIG_ROC_CONF(VSE_IDX_0x321,0x321);
#endif
#if(VSE_CANMRX_MONITER_EN_123 == VSE_RTE_ON)
    VSE_CAN_SIG_ROC_CONF(VSE_IDX_0x123,0x123);
#endif
#if(VSE_CANMRX_MONITER_EN_222 == VSE_RTE_ON)
    VSE_CAN_SIG_ROC_CONF(VSE_IDX_0x222,0x222);
#endif
#if(VSE_CANMRX_MONITER_EN_11F == VSE_RTE_ON)
    VSE_CAN_SIG_ROC_CONF(VSE_IDX_0x11F,0x11F);
#endif
#if(VSE_CANMRX_MONITER_EN_218 == VSE_RTE_ON)
    VSE_CAN_SIG_ROC_CONF(VSE_IDX_0x218,0x218);
#endif
#if(VSE_CANMRX_MONITER_EN_0F4 == VSE_RTE_ON)
    VSE_CAN_SIG_ROC_CONF(VSE_IDX_0x0F4,0x0F4);
#endif

void VSE_CanRxMonitorFrameInit(void)
{
    #if(VSE_CANMRX_MONITER_EN_051 == VSE_RTE_ON)
        VSE_CAN_SIG_ROC_INIT(VSE_IDX_0x051,0x051);
    #endif
    #if(VSE_CANMRX_MONITER_EN_0FC == VSE_RTE_ON)
        VSE_CAN_SIG_ROC_INIT(VSE_IDX_0x0FC,0x0FC);
    #endif
    #if(VSE_CANMRX_MONITER_EN_342 == VSE_RTE_ON)
        VSE_CAN_SIG_ROC_INIT(VSE_IDX_0x342,0x342);
    #endif
    #if(VSE_CANMRX_MONITER_EN_12D == VSE_RTE_ON)
        VSE_CAN_SIG_ROC_INIT(VSE_IDX_0x12D,0x12D);
    #endif
    #if(VSE_CANMRX_MONITER_EN_242 == VSE_RTE_ON)
        VSE_CAN_SIG_ROC_INIT(VSE_IDX_0x242,0x242);
    #endif
    #if(VSE_CANMRX_MONITER_EN_241 == VSE_RTE_ON)
        VSE_CAN_SIG_ROC_INIT(VSE_IDX_0x241,0x241);
    #endif
    #if(VSE_CANMRX_MONITER_EN_251 == VSE_RTE_ON)
        VSE_CAN_SIG_ROC_INIT(VSE_IDX_0x251,0x251);
    #endif
    #if(VSE_CANMRX_MONITER_EN_122 == VSE_RTE_ON)
        VSE_CAN_SIG_ROC_INIT(VSE_IDX_0x122,0x122);
    #endif
    #if(VSE_CANMRX_MONITER_EN_321 == VSE_RTE_ON)
        VSE_CAN_SIG_ROC_INIT(VSE_IDX_0x321,0x321);
    #endif
    #if(VSE_CANMRX_MONITER_EN_123 == VSE_RTE_ON)
        VSE_CAN_SIG_ROC_INIT(VSE_IDX_0x123,0x123);
    #endif
    #if(VSE_CANMRX_MONITER_EN_222 == VSE_RTE_ON)
        VSE_CAN_SIG_ROC_INIT(VSE_IDX_0x222,0x222);
    #endif
    #if(VSE_CANMRX_MONITER_EN_11F == VSE_RTE_ON)
        VSE_CAN_SIG_ROC_INIT(VSE_IDX_0x11F,0x11F);
    #endif
    #if(VSE_CANMRX_MONITER_EN_218 == VSE_RTE_ON)
        VSE_CAN_SIG_ROC_INIT(VSE_IDX_0x218,0x218);
    #endif
    #if(VSE_CANMRX_MONITER_EN_0F4 == VSE_RTE_ON)
        VSE_CAN_SIG_ROC_INIT(VSE_IDX_0x0F4,0x0F4);
    #endif
}
