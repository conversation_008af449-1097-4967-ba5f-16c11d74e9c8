#ifndef VSE_CANRX_MONITORFRAMECONF_H_
#define VSE_CANRX_MONITORFRAMECONF_H_
#include"SigMap_Hev.h"
#include"SignalMgr.h"
//#include "rtwtypes.h"
#include "VSE_RteCfg.h"


#define ROC_BIT_4           0u
#define ROC_BIT_16          1u
#define OBV_GET_INDEX_ERR   0xFFFFu

/*RTE状态*/
typedef enum{
    VSE_RTE_OK  = 0,
    VSE_RTE_ERR = 1
}VSE_RTE_STATUS_t;

typedef enum{
	VSE_RTE_CHANGE_TRIGGER = 1,
	VSE_RTE_RECEIVE_TRIGGER
}VSE_RTE_ListenerCbType_t;

/* can id map number */
#define VSE_CAN_ID_MAP_NUM   VSE_IDX_SIZE

typedef PrecisionInfo   VSE_RTE_PrecisionInfo;

/* rolling counter pake*/
typedef struct{
    /* enable */
    uint8_T  En;
    /* status rolling counter*/
	VSE_RTE_STATUS_t  Sta;
    /* can type can or canfd */
    uint8_T  CanType;
    /* set roc err counter number */
    uint32_T ErrSet;
    /* set roc ok counter number */
    uint32_T OKSet;
    /* roc err counter index */
    uint32_T ErrCnt;
    /* roc ok counter index */
    uint32_T OKCnt;
    /* Current value */
    uint32_T Now;
    /* Last value */
    uint32_T Last;
}VSE_MonitorRoc_t;

/* crc pake*/
typedef struct{
    /* enable */
    uint8_T  En;
    /* status crc*/
	VSE_RTE_STATUS_t  Sta;
    /* set crc err counter number */
    uint32_T ErrSet;
    /* set crc ok counter number */
    uint32_T OKSet;
    /* crc err counter index */
    uint32_T ErrCnt;
    /* crc ok counter index */
    uint32_T OKCnt;
}VSE_MonitorCrc_t;

/* offline pake*/
typedef struct{
    /* enable */
    uint8_T  En;
    /* sub id counter ,if this id has no sub id ,the SubIdCnt = 1 */
    uint16_T SubIdCnt;
    /* status：on/off line*/
	VSE_RTE_STATUS_t  Sta;
    /* can id receive cycle */
    uint32_T CycleRecv;
    /* 
        can id offline cycle 
        Description 
        (1) CycleRecv <  500ms, CycleOffline = 10.
        (2) CycleRecv >= 500ms, CycleOffline * CycleRecv = 5s.
        (3) if this id is functionally safe , CycleOffline = 2.
     */
    uint32_T SetCycleOffline;
    /* set Cycle count */
    uint32_T SetCycleReco;
    /* Recovery Cycle count */
    uint32_T CycleRecoCnt;
}VSE_MonitorLine_t;

/* Config map pake*/
typedef struct{
    /* can id Index. */
    uint32_T    Index;
    /* can id. */
    uint32_T    Id;
    /* can signal. */
    uint64_T    Sig;
    /* can sig ret. */
    int32_T     SigRet;
    /* can id signal rolling counter pake. */
	VSE_MonitorRoc_t CanSigRoc;
    /* can id checksum/crc pake. */
	VSE_MonitorCrc_t  CanIdCrc;
    /* can id on/off line pake. */
	VSE_MonitorLine_t CanIdLine;
    /* continuous invalid signal */
	VSE_MonitorLine_t CanIdTimeOut;
}VSE_CanRxMonitorFrame_t;


/*********************************************************
 * Description  : configure can signal rolling counter 
 * Parameter
 *   @_index    : Can id map index
 *   @_Id       : Can id
 *********************************************************/
extern VSE_CanRxMonitorFrame_t  VSE_CanIdMap[VSE_CAN_ID_MAP_NUM];


#define VSE_CAN_SIG_ROC_CONF(_index,_Id) \
void  VSE_FunRoc_ ## _Id(uint64_T unSigVal, VSE_RTE_PrecisionInfo stPrecision, uint32_T unStamp); \
void  VSE_FunRoc_ ## _Id(uint64_T unSigVal, VSE_RTE_PrecisionInfo stPrecision, uint32_T unStamp) { \
    uint32_T loop = VSE_CanIdMap[(_index)].CanSigRoc.CanType == ROC_BIT_4 ? 16u: 65536u; \
    VSE_CanIdMap[(_index)].CanSigRoc.Now = (uint32_T)unSigVal; \
    switch(VSE_CanIdMap[(_index)].CanSigRoc.Sta){ \
    case VSE_RTE_OK: \
        if(VSE_CanIdMap[(_index)].CanSigRoc.Now != ((VSE_CanIdMap[(_index)].CanSigRoc.Last + 1u)%loop)){  \
            VSE_CanIdMap[(_index)].CanSigRoc.ErrCnt ++; \
            if(VSE_CanIdMap[(_index)].CanSigRoc.ErrCnt >= VSE_CanIdMap[(_index)].CanSigRoc.ErrSet){ \
                VSE_CanIdMap[(_index)].CanSigRoc.OKCnt = 0u; \
                VSE_CanIdMap[(_index)].CanSigRoc.Sta = VSE_RTE_ERR; }  \
        }else{ VSE_CanIdMap[(_index)].CanSigRoc.ErrCnt = 0u; } break;  \
    case VSE_RTE_ERR: \
        if(VSE_CanIdMap[(_index)].CanSigRoc.Now == ((VSE_CanIdMap[(_index)].CanSigRoc.Last + 1u)%loop)) {  \
            VSE_CanIdMap[(_index)].CanSigRoc.OKCnt ++; \
            if(VSE_CanIdMap[(_index)].CanSigRoc.OKCnt >= VSE_CanIdMap[(_index)].CanSigRoc.OKSet) {  \
                VSE_CanIdMap[(_index)].CanSigRoc.ErrCnt = 0u; \
                VSE_CanIdMap[(_index)].CanSigRoc.Sta = VSE_RTE_OK; }  \
        }else{ VSE_CanIdMap[(_index)].CanSigRoc.OKCnt = 0u; } break;  \
    default: \
        /* do nothing */ \
        (void)(1==1);\
        break;\
    } \
    VSE_CanIdMap[(_index)].CanSigRoc.Last = VSE_CanIdMap[(_index)].CanSigRoc.Now; \
} \

/*********************************************************
 * Description  : init can signal rolling counter 
 * Parameter
 *   @_index    : Can id map index
 *   @_Id       : Can id
 *********************************************************/
#define VSE_CAN_SIG_ROC_INIT(_index,_Id) \
    if(VSE_CanIdMap[(_index)].CanSigRoc.En == VSE_RTE_ON){ \
        (void)SigMgrRegistSigListener(VSE_CanIdMap[(_index)].Sig, VSE_RTE_RECEIVE_TRIGGER, VSE_FunRoc_ ## _Id); \
    }else{/* do nothing */};\


void VSE_CanRxMonitorFrameInit(void);


#endif
