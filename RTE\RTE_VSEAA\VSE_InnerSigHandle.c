﻿#include "VSE_InnerSigHandle.h"


//VSE输出信号
static VSE_SIG_OUT_MAP VSE_SigOutMap[VSE_SIG_OUT_IDX_NUM];

/*********************************************************
 * Function Name: VSE_SigOutMapInit
 * Description  : 建立VSE输出信号的索引至信号值的映射关系
 * Parameter    : null
 * return       : null
 *********************************************************/
void VSE_SigOutMapInit(void)
{
    VSE_SigOutMap[VSE_SIG_OUT_IDX_fYawRate].pCANSigValue=&g_VSE_CSE_Out_Com_fYawRate;VSE_SigOutMap[VSE_SIG_OUT_IDX_fYawRate].emSigDataType= VSE_TYPE_FLOAT;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_bYawRateVld].pCANSigValue=&g_VSE_CSE_Out_Com_bYawRateVld;VSE_SigOutMap[VSE_SIG_OUT_IDX_bYawRateVld].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_fYawRateA].pCANSigValue=&g_VSE_CSE_Out_Com_fYawRateA;VSE_SigOutMap[VSE_SIG_OUT_IDX_fYawRateA].emSigDataType= VSE_TYPE_FLOAT;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_bYawRateAVld].pCANSigValue=&g_VSE_CSE_Out_Com_bYawRateAVld;VSE_SigOutMap[VSE_SIG_OUT_IDX_bYawRateAVld].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_fAy].pCANSigValue=&g_VSE_CSE_Out_Com_fAy;VSE_SigOutMap[VSE_SIG_OUT_IDX_fAy].emSigDataType= VSE_TYPE_FLOAT;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_bAyVld].pCANSigValue=&g_VSE_CSE_Out_Com_bAyVld;VSE_SigOutMap[VSE_SIG_OUT_IDX_bAyVld].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_fAx].pCANSigValue=&g_VSE_CSE_Out_Com_fAx;VSE_SigOutMap[VSE_SIG_OUT_IDX_fAx].emSigDataType= VSE_TYPE_FLOAT;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_bAxVld].pCANSigValue=&g_VSE_CSE_Out_Com_bAxVld;VSE_SigOutMap[VSE_SIG_OUT_IDX_bAxVld].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_fVxEst].pCANSigValue=&g_VSE_VSRE_Out_Com_fVxEst;VSE_SigOutMap[VSE_SIG_OUT_IDX_fVxEst].emSigDataType= VSE_TYPE_FLOAT;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_fFLSlipRate].pCANSigValue=&g_VSE_VSRE_Out_Com_fFLSlipRate;VSE_SigOutMap[VSE_SIG_OUT_IDX_fFLSlipRate].emSigDataType= VSE_TYPE_FLOAT;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_fFRSlipRate].pCANSigValue=&g_VSE_VSRE_Out_Com_fFRSlipRate;VSE_SigOutMap[VSE_SIG_OUT_IDX_fFRSlipRate].emSigDataType= VSE_TYPE_FLOAT;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_fRLSlipRate].pCANSigValue=&g_VSE_VSRE_Out_Com_fRLSlipRate;VSE_SigOutMap[VSE_SIG_OUT_IDX_fRLSlipRate].emSigDataType= VSE_TYPE_FLOAT;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_fRRSlipRate].pCANSigValue=&g_VSE_VSRE_Out_Com_fRRSlipRate;VSE_SigOutMap[VSE_SIG_OUT_IDX_fRRSlipRate].emSigDataType= VSE_TYPE_FLOAT;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_bVxVld].pCANSigValue=&g_VSE_VSRE_Out_Com_bVxVld;VSE_SigOutMap[VSE_SIG_OUT_IDX_bVxVld].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_bFLSRVld].pCANSigValue=&g_VSE_VSRE_Out_Com_bFLSRVld;VSE_SigOutMap[VSE_SIG_OUT_IDX_bFLSRVld].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_bFRSRVld].pCANSigValue=&g_VSE_VSRE_Out_Com_bFRSRVld;VSE_SigOutMap[VSE_SIG_OUT_IDX_bFRSRVld].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_bRLSRVld].pCANSigValue=&g_VSE_VSRE_Out_Com_bRLSRVld;VSE_SigOutMap[VSE_SIG_OUT_IDX_bRLSRVld].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_bRRSRVld].pCANSigValue=&g_VSE_VSRE_Out_Com_bRRSRVld;VSE_SigOutMap[VSE_SIG_OUT_IDX_bRRSRVld].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_u8VxDemSts].pCANSigValue=&g_VSE_VSRE_Out_Com_u8VxDemSts;VSE_SigOutMap[VSE_SIG_OUT_IDX_u8VxDemSts].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_bVxDirSts].pCANSigValue=&g_VSE_VSRE_Out_Com_bVxDirSts;VSE_SigOutMap[VSE_SIG_OUT_IDX_bVxDirSts].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_bVxDemVld].pCANSigValue=&g_VSE_VSRE_Out_Com_bVxDemVld;VSE_SigOutMap[VSE_SIG_OUT_IDX_bVxDemVld].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_bVxDirVld].pCANSigValue=&g_VSE_VSRE_Out_Com_bVxDirVld;VSE_SigOutMap[VSE_SIG_OUT_IDX_bVxDirVld].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_fCentrWSFL].pCANSigValue=&g_VSE_VSRE_Out_Com_fCentrWSFL;VSE_SigOutMap[VSE_SIG_OUT_IDX_fCentrWSFL].emSigDataType= VSE_TYPE_FLOAT;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_fCentrWSFR].pCANSigValue=&g_VSE_VSRE_Out_Com_fCentrWSFR;VSE_SigOutMap[VSE_SIG_OUT_IDX_fCentrWSFR].emSigDataType= VSE_TYPE_FLOAT;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_fCentrWSRL].pCANSigValue=&g_VSE_VSRE_Out_Com_fCentrWSRL;VSE_SigOutMap[VSE_SIG_OUT_IDX_fCentrWSRL].emSigDataType= VSE_TYPE_FLOAT;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_fCentrWSRR].pCANSigValue=&g_VSE_VSRE_Out_Com_fCentrWSRR;VSE_SigOutMap[VSE_SIG_OUT_IDX_fCentrWSRR].emSigDataType= VSE_TYPE_FLOAT;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_bCWSVldFL].pCANSigValue=&g_VSE_VSRE_Out_Com_bCWSVldFL;VSE_SigOutMap[VSE_SIG_OUT_IDX_bCWSVldFL].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_bCWSVldFR].pCANSigValue=&g_VSE_VSRE_Out_Com_bCWSVldFR;VSE_SigOutMap[VSE_SIG_OUT_IDX_bCWSVldFR].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_bCWSVldRL].pCANSigValue=&g_VSE_VSRE_Out_Com_bCWSVldRL;VSE_SigOutMap[VSE_SIG_OUT_IDX_bCWSVldRL].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_bCWSVldRR].pCANSigValue=&g_VSE_VSRE_Out_Com_bCWSVldRR;VSE_SigOutMap[VSE_SIG_OUT_IDX_bCWSVldRR].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_fSlopAg].pCANSigValue=&g_VSE_SAE_Out_Com_fSlopAg;VSE_SigOutMap[VSE_SIG_OUT_IDX_fSlopAg].emSigDataType= VSE_TYPE_FLOAT;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_fSlopGrdt].pCANSigValue=&g_VSE_SAE_Out_Com_fSlopGrdt;VSE_SigOutMap[VSE_SIG_OUT_IDX_fSlopGrdt].emSigDataType= VSE_TYPE_FLOAT;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_bSlopAgVld].pCANSigValue=&g_VSE_SAE_Out_Com_bSlopAgVld;VSE_SigOutMap[VSE_SIG_OUT_IDX_bSlopAgVld].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_fSlopYAg].pCANSigValue=&g_VSE_SYE_Out_Com_fSlopYAg;VSE_SigOutMap[VSE_SIG_OUT_IDX_fSlopYAg].emSigDataType= VSE_TYPE_FLOAT;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_bSlopYAgVld].pCANSigValue=&g_VSE_SYE_Out_Com_bSlopYAgVld;VSE_SigOutMap[VSE_SIG_OUT_IDX_bSlopYAgVld].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_fSlopYGrdt].pCANSigValue=&g_VSE_SYE_Out_Com_fSlopYGrdt;VSE_SigOutMap[VSE_SIG_OUT_IDX_fSlopYGrdt].emSigDataType= VSE_TYPE_FLOAT;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_fVehMEstim].pCANSigValue=&g_VSE_VME_Out_Com_fVehMEstim;VSE_SigOutMap[VSE_SIG_OUT_IDX_fVehMEstim].emSigDataType= VSE_TYPE_FLOAT;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_fVehSprMEstim].pCANSigValue=&g_VSE_VME_Out_Com_fVehSprMEstim;VSE_SigOutMap[VSE_SIG_OUT_IDX_fVehSprMEstim].emSigDataType= VSE_TYPE_FLOAT;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_fWhlVFEFL].pCANSigValue=&g_VSE_VME_Out_Com_fWhlVFEFL;VSE_SigOutMap[VSE_SIG_OUT_IDX_fWhlVFEFL].emSigDataType= VSE_TYPE_FLOAT;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_fWhlVFEFR].pCANSigValue=&g_VSE_VME_Out_Com_fWhlVFEFR;VSE_SigOutMap[VSE_SIG_OUT_IDX_fWhlVFEFR].emSigDataType= VSE_TYPE_FLOAT;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_fWhlVFERL].pCANSigValue=&g_VSE_VME_Out_Com_fWhlVFERL;VSE_SigOutMap[VSE_SIG_OUT_IDX_fWhlVFERL].emSigDataType= VSE_TYPE_FLOAT;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_fWhlVFERR].pCANSigValue=&g_VSE_VME_Out_Com_fWhlVFERR;VSE_SigOutMap[VSE_SIG_OUT_IDX_fWhlVFERR].emSigDataType= VSE_TYPE_FLOAT;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_bVehMEstimVld].pCANSigValue=&g_VSE_VME_Out_Com_bVehMEstimVld;VSE_SigOutMap[VSE_SIG_OUT_IDX_bVehMEstimVld].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_bWhlVFEFLVld].pCANSigValue=&g_VSE_VME_Out_Com_bWhlVFEFLVld;VSE_SigOutMap[VSE_SIG_OUT_IDX_bWhlVFEFLVld].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_bWhlVFEFRVld].pCANSigValue=&g_VSE_VME_Out_Com_bWhlVFEFRVld;VSE_SigOutMap[VSE_SIG_OUT_IDX_bWhlVFEFRVld].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_bWhlVFERLVld].pCANSigValue=&g_VSE_VME_Out_Com_bWhlVFERLVld;VSE_SigOutMap[VSE_SIG_OUT_IDX_bWhlVFERLVld].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_bWhlVFERRVld].pCANSigValue=&g_VSE_VME_Out_Com_bWhlVFERRVld;VSE_SigOutMap[VSE_SIG_OUT_IDX_bWhlVFERRVld].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_fFLAdhCoeff].pCANSigValue=&g_VSE_RAC_Out_Com_fFLAdhCoeff;VSE_SigOutMap[VSE_SIG_OUT_IDX_fFLAdhCoeff].emSigDataType= VSE_TYPE_FLOAT;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_fRLAdhCoeff].pCANSigValue=&g_VSE_RAC_Out_Com_fRLAdhCoeff;VSE_SigOutMap[VSE_SIG_OUT_IDX_fRLAdhCoeff].emSigDataType= VSE_TYPE_FLOAT;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_fFRAdhCoeff].pCANSigValue=&g_VSE_RAC_Out_Com_fFRAdhCoeff;VSE_SigOutMap[VSE_SIG_OUT_IDX_fFRAdhCoeff].emSigDataType= VSE_TYPE_FLOAT;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_fRRAdhCoeff].pCANSigValue=&g_VSE_RAC_Out_Com_fRRAdhCoeff;VSE_SigOutMap[VSE_SIG_OUT_IDX_fRRAdhCoeff].emSigDataType= VSE_TYPE_FLOAT;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_bFLACoeffVld].pCANSigValue=&g_VSE_RAC_Out_Com_bFLACoeffVld;VSE_SigOutMap[VSE_SIG_OUT_IDX_bFLACoeffVld].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_bFRACoeffVld].pCANSigValue=&g_VSE_RAC_Out_Com_bFRACoeffVld;VSE_SigOutMap[VSE_SIG_OUT_IDX_bFRACoeffVld].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_bRLACoeffVld].pCANSigValue=&g_VSE_RAC_Out_Com_bRLACoeffVld;VSE_SigOutMap[VSE_SIG_OUT_IDX_bRLACoeffVld].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_bRRACoeffVld].pCANSigValue=&g_VSE_RAC_Out_Com_bRRACoeffVld;VSE_SigOutMap[VSE_SIG_OUT_IDX_bRRACoeffVld].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_u8FLTypIdn].pCANSigValue=&g_VSE_RAC_Out_Com_u8FLTypIdn;VSE_SigOutMap[VSE_SIG_OUT_IDX_u8FLTypIdn].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_u8FRTypIdn].pCANSigValue=&g_VSE_RAC_Out_Com_u8FRTypIdn;VSE_SigOutMap[VSE_SIG_OUT_IDX_u8FRTypIdn].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_u8RLTypIdn].pCANSigValue=&g_VSE_RAC_Out_Com_u8RLTypIdn;VSE_SigOutMap[VSE_SIG_OUT_IDX_u8RLTypIdn].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_u8RRTypIdn].pCANSigValue=&g_VSE_RAC_Out_Com_u8RRTypIdn;VSE_SigOutMap[VSE_SIG_OUT_IDX_u8RRTypIdn].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_bFLTypIdnVld].pCANSigValue=&g_VSE_RAC_Out_Com_bFLTypIdnVld;VSE_SigOutMap[VSE_SIG_OUT_IDX_bFLTypIdnVld].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_bFRTypIdnVld].pCANSigValue=&g_VSE_RAC_Out_Com_bFRTypIdnVld;VSE_SigOutMap[VSE_SIG_OUT_IDX_bFRTypIdnVld].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_bRLTypIdnVld].pCANSigValue=&g_VSE_RAC_Out_Com_bRLTypIdnVld;VSE_SigOutMap[VSE_SIG_OUT_IDX_bRLTypIdnVld].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_bRRTypIdnVld].pCANSigValue=&g_VSE_RAC_Out_Com_bRRTypIdnVld;VSE_SigOutMap[VSE_SIG_OUT_IDX_bRRTypIdnVld].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_fSpliFLMu].pCANSigValue=&g_VSE_RACS_Out_Com_fFLMue;VSE_SigOutMap[VSE_SIG_OUT_IDX_fSpliFLMu].emSigDataType= VSE_TYPE_FLOAT;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_fSpliFRMu].pCANSigValue=&g_VSE_RACS_Out_Com_fFRMue;VSE_SigOutMap[VSE_SIG_OUT_IDX_fSpliFRMu].emSigDataType= VSE_TYPE_FLOAT;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_fSpliRLMu].pCANSigValue=&g_VSE_RACS_Out_Com_fRLMue;VSE_SigOutMap[VSE_SIG_OUT_IDX_fSpliRLMu].emSigDataType= VSE_TYPE_FLOAT;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_fSpliRRMu].pCANSigValue=&g_VSE_RACS_Out_Com_fRRMue;VSE_SigOutMap[VSE_SIG_OUT_IDX_fSpliRRMu].emSigDataType= VSE_TYPE_FLOAT;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_bSpliFLMuVld].pCANSigValue=&g_VSE_RACS_Out_Com_bFLMueVld;VSE_SigOutMap[VSE_SIG_OUT_IDX_bSpliFLMuVld].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_bSpliFRMuVld].pCANSigValue=&g_VSE_RACS_Out_Com_bFRMueVld;VSE_SigOutMap[VSE_SIG_OUT_IDX_bSpliFRMuVld].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_bSpliRLMuVld].pCANSigValue=&g_VSE_RACS_Out_Com_bRLMueVld;VSE_SigOutMap[VSE_SIG_OUT_IDX_bSpliRLMuVld].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_bSpliRRMuVld].pCANSigValue=&g_VSE_RACS_Out_Com_bRRMueVld;VSE_SigOutMap[VSE_SIG_OUT_IDX_bSpliRRMuVld].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_bSplitFlg].pCANSigValue=&g_VSE_RACS_Out_Com_bSplitFlg;VSE_SigOutMap[VSE_SIG_OUT_IDX_bSplitFlg].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_fSplitReliable].pCANSigValue=&g_VSE_RACS_Out_Com_fSplitRel;VSE_SigOutMap[VSE_SIG_OUT_IDX_fSplitReliable].emSigDataType= VSE_TYPE_FLOAT;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_u8SplitTyp].pCANSigValue=&g_VSE_RACS_Out_Com_u8SplitTyp;VSE_SigOutMap[VSE_SIG_OUT_IDX_u8SplitTyp].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_u8DamSig].pCANSigValue=&g_VSE_RACS_Out_Com_u8DamSig;VSE_SigOutMap[VSE_SIG_OUT_IDX_u8DamSig].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_fVehYSpd].pCANSigValue=&g_VSE_VYE_Out_Com_fVehYSpd;VSE_SigOutMap[VSE_SIG_OUT_IDX_fVehYSpd].emSigDataType= VSE_TYPE_FLOAT;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_bVehYSpdValid].pCANSigValue=&g_VSE_VYE_Out_Com_bVehYSpdValid;VSE_SigOutMap[VSE_SIG_OUT_IDX_bVehYSpdValid].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_fSideSlipAg].pCANSigValue=&g_VSE_SSE_Out_Com_fSideSlipAg;VSE_SigOutMap[VSE_SIG_OUT_IDX_fSideSlipAg].emSigDataType= VSE_TYPE_FLOAT;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_bSSAValid].pCANSigValue=&g_VSE_SSE_Out_Com_bSSAValid;VSE_SigOutMap[VSE_SIG_OUT_IDX_bSSAValid].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_fRollAgEst].pCANSigValue=&g_VSE_VPE_Out_Com_fRollAgEst;VSE_SigOutMap[VSE_SIG_OUT_IDX_fRollAgEst].emSigDataType= VSE_TYPE_FLOAT;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_bRollAgEstVld].pCANSigValue=&g_VSE_VPE_Out_Com_bRollAgEstVld;VSE_SigOutMap[VSE_SIG_OUT_IDX_bRollAgEstVld].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_fRollRateEst].pCANSigValue=&g_VSE_VPE_Out_Com_fRollRateEst;VSE_SigOutMap[VSE_SIG_OUT_IDX_fRollRateEst].emSigDataType= VSE_TYPE_FLOAT;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_bRollRateEstVld].pCANSigValue=&g_VSE_VPE_Out_Com_bRollRateVld;VSE_SigOutMap[VSE_SIG_OUT_IDX_bRollRateEstVld].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_fPitchAgEst].pCANSigValue=&g_VSE_VPE_Out_Com_fPitchAgEst;VSE_SigOutMap[VSE_SIG_OUT_IDX_fPitchAgEst].emSigDataType= VSE_TYPE_FLOAT;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_bPitchAgEstVld].pCANSigValue=&g_VSE_VPE_Out_Com_bPitchAgVld;VSE_SigOutMap[VSE_SIG_OUT_IDX_bPitchAgEstVld].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_fPitchRateEst].pCANSigValue=&g_VSE_VPE_Out_Com_fPitchRateEst;VSE_SigOutMap[VSE_SIG_OUT_IDX_fPitchRateEst].emSigDataType= VSE_TYPE_FLOAT;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_bPitchRateEstVld].pCANSigValue=&g_VSE_VPE_Out_Com_bPitchRateVld;VSE_SigOutMap[VSE_SIG_OUT_IDX_bPitchRateEstVld].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_fTarYawR].pCANSigValue=&g_VSE_TYE_Out_Com_fTarWz;VSE_SigOutMap[VSE_SIG_OUT_IDX_fTarYawR].emSigDataType= VSE_TYPE_FLOAT;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_bTarYawRVld].pCANSigValue=&g_VSE_TYE_Out_Com_bTarWzVld;VSE_SigOutMap[VSE_SIG_OUT_IDX_bTarYawRVld].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_u8VSESoftVersNr0].pCANSigValue=&g_VSE_Out_Com_u8VSESoftVersNr[0];VSE_SigOutMap[VSE_SIG_OUT_IDX_u8VSESoftVersNr0].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_u8VSESoftVersNr1].pCANSigValue=&g_VSE_Out_Com_u8VSESoftVersNr[1];VSE_SigOutMap[VSE_SIG_OUT_IDX_u8VSESoftVersNr1].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_u8VSESoftVersNr2].pCANSigValue=&g_VSE_Out_Com_u8VSESoftVersNr[2];VSE_SigOutMap[VSE_SIG_OUT_IDX_u8VSESoftVersNr2].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_u8VSESoftVersNr3].pCANSigValue=&g_VSE_Out_Com_u8VSESoftVersNr[3];VSE_SigOutMap[VSE_SIG_OUT_IDX_u8VSESoftVersNr3].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_u8VSESoftVersNr4].pCANSigValue=&g_VSE_Out_Com_u8VSESoftVersNr[4];VSE_SigOutMap[VSE_SIG_OUT_IDX_u8VSESoftVersNr4].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_u8VSESoftVersNr5].pCANSigValue=&g_VSE_Out_Com_u8VSESoftVersNr[5];VSE_SigOutMap[VSE_SIG_OUT_IDX_u8VSESoftVersNr5].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_u8VSESoftVersNr6].pCANSigValue=&g_VSE_Out_Com_u8VSESoftVersNr[6];VSE_SigOutMap[VSE_SIG_OUT_IDX_u8VSESoftVersNr6].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_u8VSESoftVersNr7].pCANSigValue=&g_VSE_Out_Com_u8VSESoftVersNr[7];VSE_SigOutMap[VSE_SIG_OUT_IDX_u8VSESoftVersNr7].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_u8VSESoftVersNr8].pCANSigValue=&g_VSE_Out_Com_u8VSESoftVersNr[8];VSE_SigOutMap[VSE_SIG_OUT_IDX_u8VSESoftVersNr8].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_u8VSESoftVersNr9].pCANSigValue=&g_VSE_Out_Com_u8VSESoftVersNr[9];VSE_SigOutMap[VSE_SIG_OUT_IDX_u8VSESoftVersNr9].emSigDataType= VSE_TYPE_UINT8;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_fTireRollgRFrnt].pCANSigValue=&g_VSE_Out_Com_fTireRollgRFrnt;VSE_SigOutMap[VSE_SIG_OUT_IDX_fTireRollgRFrnt].emSigDataType= VSE_TYPE_FLOAT;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_fTireRollgRRe].pCANSigValue=&g_VSE_Out_Com_fTireRollgRRe;VSE_SigOutMap[VSE_SIG_OUT_IDX_fTireRollgRRe].emSigDataType= VSE_TYPE_FLOAT;
    VSE_SigOutMap[VSE_SIG_OUT_IDX_u8SysSts].pCANSigValue=&g_VSE_SSM_Out_Com_u8SysSts;VSE_SigOutMap[VSE_SIG_OUT_IDX_u8SysSts].emSigDataType= VSE_TYPE_UINT8;
}

/*********************************************************
 * Function Name: VSE_RTE_SIG_OUT_READ
 * Description  : VSE输出信号获取
 * Parameter    : Par1: VSE_SIG_OUT_IDXS_t:需要获取的信号对应的索引  
 *                Par2:g_VSE_Out_Sig_Addr:获取到信号的地址
 * return       : null
 *********************************************************/
void VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDXS_t idx, void *g_VSE_Out_Sig_Addr)
{
    if (NULL != VSE_SigOutMap[idx].pCANSigValue)
    {
        switch (VSE_SigOutMap[idx].emSigDataType)
        {
        case VSE_TYPE_UINT8:
            *(uint8_T *)(g_VSE_Out_Sig_Addr) = *(uint8_T *)(VSE_SigOutMap[idx].pCANSigValue);
            break;

        case VSE_TYPE_UINT16:
            *(uint16_T *)(g_VSE_Out_Sig_Addr) = *(uint16_T *)(VSE_SigOutMap[idx].pCANSigValue);
            break;

        case VSE_TYPE_INT16:
            *(int16_T *)(g_VSE_Out_Sig_Addr) = *(int16_T *)(VSE_SigOutMap[idx].pCANSigValue);
            break;

        case VSE_TYPE_UINT32:
            *(uint32_T *)(g_VSE_Out_Sig_Addr) = *(uint32_T *)(VSE_SigOutMap[idx].pCANSigValue);
            break;

        case VSE_TYPE_FLOAT:
            *(real32_T *)(g_VSE_Out_Sig_Addr) = *(real32_T *)(VSE_SigOutMap[idx].pCANSigValue);
            break;

        default:
            /*default*/
            break;
        }
    }
}

/*********************************************************
 * Function Name: VSE_GetDataFromDiSus
 * Description  : VSE与DiSus板间通信，VSE获取DiSus信号值
 * Parameter    : null
 * return       : null
 *********************************************************/
#if (TASK_InnerCom_DiSus_ENABLE == VSE_RTE_ON)

void VSE_GetDataFromDiSus(void)
{
    //采用数据隔离，函数调用方式
    if(TASK_InnerCom_FORMAT_DiSus == VSE_RTE_ON)  
    {
        DiSusC_to_DiDyna tDiSusC_Out;
        tDiSusC_Out = RteGetDiSusC_to_DiDynaInput();

        VSE_RTE_SIG_u16DamprPosnFL = tDiSusC_Out.DiSusA_out_g_i16FL_WheelAltitude;
        VSE_RTE_SIG_u16DamprPosnFR = tDiSusC_Out.DiSusA_out_g_i16FR_WheelAltitude;
        VSE_RTE_SIG_u16DamprPosnRL = tDiSusC_Out.DiSusA_out_g_i16RL_WheelAltitude;
        VSE_RTE_SIG_u16DamprPosnRR = tDiSusC_Out.DiSusA_out_g_i16RR_WheelAltitude;
        VSE_RTE_SIG_bDamprPosnFLSts = tDiSusC_Out.DiSusA_out_g_bFL_WheelAltitude_Fault;
        VSE_RTE_SIG_bDamprPosnFRSts = tDiSusC_Out.DiSusA_out_g_bFR_WheelAltitude_Fault;
        VSE_RTE_SIG_bDamprPosnRLSts = tDiSusC_Out.DiSusA_out_g_bRL_WheelAltitude_Fault;
        VSE_RTE_SIG_bDamprPosnRRSts = tDiSusC_Out.DiSusA_out_g_bRR_WheelAltitude_Fault;
        /*VSE_RTE_SIG_u16FLActualCurrent = tDiSusC_Out.g_Inspector_Damper_FL_S;
        VSE_RTE_SIG_u16FRActualCurrent = tDiSusC_Out.g_Inspector_Damper_FR_S;
        VSE_RTE_SIG_u16RLActualCurrent = tDiSusC_Out.g_Inspector_Damper_RL_S;
        VSE_RTE_SIG_u16RRActualCurrent = tDiSusC_Out.g_Inspector_Damper_RR_S;
        VSE_RTE_SIG_u8DiSusModExeSts = tDiSusC_Out.g_DiSus_Mode_Execute_Status_S;
        VSE_RTE_SIG_bDiSusHeiAdjSts = tDiSusC_Out.g_DiSus_Height_Adjust_Status_S;
        VSE_RTE_SIG_u8DiSusHeiAdjProc = tDiSusC_Out.g_DiSus_Height_Adjust_Process_S;
        VSE_RTE_SIG_bDiSusHeiAdjFltSts = tDiSusC_Out.g_DiSus_Height_Adjust_Fault_S;*/
    }
    //采用全局变量方式获取
    else
    {
        VSE_RTE_SIG_u16DamprPosnFL = g_u16DiSus_Actual_Height_FL_S;
        VSE_RTE_SIG_u16DamprPosnFR = g_u16DiSus_Actual_Height_FR_S;
        VSE_RTE_SIG_u16DamprPosnRL = g_u16DiSus_Actual_Height_RL_S;
        VSE_RTE_SIG_u16DamprPosnRR = g_u16DiSus_Actual_Height_RR_S;
        VSE_RTE_SIG_bDamprPosnFLSts = g_bDiSus_Actual_Height_Invalid_FL_S;
        VSE_RTE_SIG_bDamprPosnFRSts = g_bDiSus_Actual_Height_Invalid_FR_S;
        VSE_RTE_SIG_bDamprPosnRLSts = g_bDiSus_Actual_Height_Invalid_RL_S;
        VSE_RTE_SIG_bDamprPosnRRSts = g_bDiSus_Actual_Height_Invalid_RR_S;
        /*VSE_RTE_SIG_u16FLActualCurrent = g_u16Inspector_Damper_FL_S;
        VSE_RTE_SIG_u16FRActualCurrent = g_u16Inspector_Damper_FR_S;
        VSE_RTE_SIG_u16RLActualCurrent = g_u16Inspector_Damper_RL_S;
        VSE_RTE_SIG_u16RRActualCurrent = g_u16Inspector_Damper_RR_S;
        VSE_RTE_SIG_u8DiSusModExeSts = g_u8DiSus_Mode_Execute_Status_S;
        VSE_RTE_SIG_bDiSusHeiAdjSts = g_bDiSus_Height_Adjust_Status_S;
        VSE_RTE_SIG_u8DiSusHeiAdjProc = g_u8DiSus_Height_Adjust_Process_S;
        VSE_RTE_SIG_bDiSusHeiAdjFltSts = g_bDiSus_Height_Adjust_Fault_S;*/

    }
}

#endif 


/*********************************************************
 * Function Name: VSE_GetDataToDiDyna
 * Description  : VSE与DiDyna板间通信，VSE传输给DiDyna信号
 * Parameter    : null
 * return       : null
 *********************************************************/
#if (TASK_InnerCom_DiDyna_ENABLE == VSE_RTE_ON)

void VSE_GetDataToDiDyna(void)
{
    //采用数据隔离，函数调用方式
    if(TASK_InnerCom_FORMAT_DiDyna == VSE_RTE_ON)  
    {
    VSE_to_DiDyna VSE2DiDyna;

    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fYawRate,&VSE2DiDyna.g_VSE_SIG_OUT_fYawRate);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bYawRateVld,&VSE2DiDyna.g_VSE_SIG_OUT_bYawRateVld);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fYawRateA,&VSE2DiDyna.g_VSE_SIG_OUT_fYawRateA);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bYawRateAVld,&VSE2DiDyna.g_VSE_SIG_OUT_bYawRateAVld);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fAy,&VSE2DiDyna.g_VSE_SIG_OUT_fAy);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bAyVld,&VSE2DiDyna.g_VSE_SIG_OUT_bAyVld);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fAx,&VSE2DiDyna.g_VSE_SIG_OUT_fAx);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bAxVld,&VSE2DiDyna.g_VSE_SIG_OUT_bAxVld);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fVxEst,&VSE2DiDyna.g_VSE_SIG_OUT_fVxEst);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fFLSlipRate,&VSE2DiDyna.g_VSE_SIG_OUT_fFLSlipRate);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fFRSlipRate,&VSE2DiDyna.g_VSE_SIG_OUT_fFRSlipRate);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fRLSlipRate,&VSE2DiDyna.g_VSE_SIG_OUT_fRLSlipRate);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fRRSlipRate,&VSE2DiDyna.g_VSE_SIG_OUT_fRRSlipRate);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bVxVld,&VSE2DiDyna.g_VSE_SIG_OUT_bVxVld);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bFLSRVld,&VSE2DiDyna.g_VSE_SIG_OUT_bFLSRVld);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bFRSRVld,&VSE2DiDyna.g_VSE_SIG_OUT_bFRSRVld);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bRLSRVld,&VSE2DiDyna.g_VSE_SIG_OUT_bRLSRVld);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bRRSRVld,&VSE2DiDyna.g_VSE_SIG_OUT_bRRSRVld);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_u8VxDemSts,&VSE2DiDyna.g_VSE_SIG_OUT_u8VxDemSts);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bVxDirSts,&VSE2DiDyna.g_VSE_SIG_OUT_bVxDirSts);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bVxDemVld,&VSE2DiDyna.g_VSE_SIG_OUT_bVxDemVld);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bVxDirVld,&VSE2DiDyna.g_VSE_SIG_OUT_bVxDirVld);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fCentrWSFL,&VSE2DiDyna.g_VSE_SIG_OUT_fCentrWSFL);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fCentrWSFR,&VSE2DiDyna.g_VSE_SIG_OUT_fCentrWSFR);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fCentrWSRL,&VSE2DiDyna.g_VSE_SIG_OUT_fCentrWSRL);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fCentrWSRR,&VSE2DiDyna.g_VSE_SIG_OUT_fCentrWSRR);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bCWSVldFL,&VSE2DiDyna.g_VSE_SIG_OUT_bCWSVldFL);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bCWSVldFR,&VSE2DiDyna.g_VSE_SIG_OUT_bCWSVldFR);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bCWSVldRL,&VSE2DiDyna.g_VSE_SIG_OUT_bCWSVldRL);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bCWSVldRR,&VSE2DiDyna.g_VSE_SIG_OUT_bCWSVldRR);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fSlopAg,&VSE2DiDyna.g_VSE_SIG_OUT_fSlopAg);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fSlopGrdt,&VSE2DiDyna.g_VSE_SIG_OUT_fSlopGrdt);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bSlopAgVld,&VSE2DiDyna.g_VSE_SIG_OUT_bSlopAgVld);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fSlopYAg,&VSE2DiDyna.g_VSE_SIG_OUT_fSlopYAg);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bSlopYAgVld,&VSE2DiDyna.g_VSE_SIG_OUT_bSlopYAgVld);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fSlopYGrdt,&VSE2DiDyna.g_VSE_SIG_OUT_fSlopYGrdt);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fVehMEstim,&VSE2DiDyna.g_VSE_SIG_OUT_fVehMEstim);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fVehSprMEstim,&VSE2DiDyna.g_VSE_SIG_OUT_fVehSprMEstim);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fWhlVFEFL,&VSE2DiDyna.g_VSE_SIG_OUT_fWhlVFEFL);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fWhlVFEFR,&VSE2DiDyna.g_VSE_SIG_OUT_fWhlVFEFR);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fWhlVFERL,&VSE2DiDyna.g_VSE_SIG_OUT_fWhlVFERL);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fWhlVFERR,&VSE2DiDyna.g_VSE_SIG_OUT_fWhlVFERR);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bVehMEstimVld,&VSE2DiDyna.g_VSE_SIG_OUT_bVehMEstimVld);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bWhlVFEFLVld,&VSE2DiDyna.g_VSE_SIG_OUT_bWhlVFEFLVld);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bWhlVFEFRVld,&VSE2DiDyna.g_VSE_SIG_OUT_bWhlVFEFRVld);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bWhlVFERLVld,&VSE2DiDyna.g_VSE_SIG_OUT_bWhlVFERLVld);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bWhlVFERRVld,&VSE2DiDyna.g_VSE_SIG_OUT_bWhlVFERRVld);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fFLAdhCoeff,&VSE2DiDyna.g_VSE_SIG_OUT_fFLAdhCoeff);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fRLAdhCoeff,&VSE2DiDyna.g_VSE_SIG_OUT_fRLAdhCoeff);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fFRAdhCoeff,&VSE2DiDyna.g_VSE_SIG_OUT_fFRAdhCoeff);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fRRAdhCoeff,&VSE2DiDyna.g_VSE_SIG_OUT_fRRAdhCoeff);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bFLACoeffVld,&VSE2DiDyna.g_VSE_SIG_OUT_bFLACoeffVld);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bFRACoeffVld,&VSE2DiDyna.g_VSE_SIG_OUT_bFRACoeffVld);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bRLACoeffVld,&VSE2DiDyna.g_VSE_SIG_OUT_bRLACoeffVld);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bRRACoeffVld,&VSE2DiDyna.g_VSE_SIG_OUT_bRRACoeffVld);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_u8FLTypIdn,&VSE2DiDyna.g_VSE_SIG_OUT_u8FLTypIdn);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_u8FRTypIdn,&VSE2DiDyna.g_VSE_SIG_OUT_u8FRTypIdn);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_u8RLTypIdn,&VSE2DiDyna.g_VSE_SIG_OUT_u8RLTypIdn);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_u8RRTypIdn,&VSE2DiDyna.g_VSE_SIG_OUT_u8RRTypIdn);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bFLTypIdnVld,&VSE2DiDyna.g_VSE_SIG_OUT_bFLTypIdnVld);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bFRTypIdnVld,&VSE2DiDyna.g_VSE_SIG_OUT_bFRTypIdnVld);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bRLTypIdnVld,&VSE2DiDyna.g_VSE_SIG_OUT_bRLTypIdnVld);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bRRTypIdnVld,&VSE2DiDyna.g_VSE_SIG_OUT_bRRTypIdnVld);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fSpliFLMu,&VSE2DiDyna.g_VSE_SIG_OUT_fSpliFLMu);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fSpliFRMu,&VSE2DiDyna.g_VSE_SIG_OUT_fSpliFRMu);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fSpliRLMu,&VSE2DiDyna.g_VSE_SIG_OUT_fSpliRLMu);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fSpliRRMu,&VSE2DiDyna.g_VSE_SIG_OUT_fSpliRRMu);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bSpliFLMuVld,&VSE2DiDyna.g_VSE_SIG_OUT_bSpliFLMuVld);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bSpliFRMuVld,&VSE2DiDyna.g_VSE_SIG_OUT_bSpliFRMuVld);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bSpliRLMuVld,&VSE2DiDyna.g_VSE_SIG_OUT_bSpliRLMuVld);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bSpliRRMuVld,&VSE2DiDyna.g_VSE_SIG_OUT_bSpliRRMuVld);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bSplitFlg,&VSE2DiDyna.g_VSE_SIG_OUT_bSplitFlg);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fSplitReliable,&VSE2DiDyna.g_VSE_SIG_OUT_fSplitReliable);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_u8SplitTyp,&VSE2DiDyna.g_VSE_SIG_OUT_u8SplitTyp);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fVehYSpd,&VSE2DiDyna.g_VSE_SIG_OUT_fVehYSpd);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bVehYSpdValid,&VSE2DiDyna.g_VSE_SIG_OUT_bVehYSpdValid);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fSideSlipAg,&VSE2DiDyna.g_VSE_SIG_OUT_fSideSlipAg);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bSSAValid,&VSE2DiDyna.g_VSE_SIG_OUT_bSSAValid);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fRollAgEst,&VSE2DiDyna.g_VSE_SIG_OUT_fRollAgEst);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bRollAgEstVld,&VSE2DiDyna.g_VSE_SIG_OUT_bRollAgEstVld);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fRollRateEst,&VSE2DiDyna.g_VSE_SIG_OUT_fRollRateEst);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bRollRateEstVld,&VSE2DiDyna.g_VSE_SIG_OUT_bRollRateEstVld);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fPitchAgEst,&VSE2DiDyna.g_VSE_SIG_OUT_fPitchAgEst);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bPitchAgEstVld,&VSE2DiDyna.g_VSE_SIG_OUT_bPitchAgEstVld);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fPitchRateEst,&VSE2DiDyna.g_VSE_SIG_OUT_fPitchRateEst);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bPitchRateEstVld,&VSE2DiDyna.g_VSE_SIG_OUT_bPitchRateEstVld);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fTarYawR,&VSE2DiDyna.g_VSE_SIG_OUT_fTarYawR);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bTarYawRVld,&VSE2DiDyna.g_VSE_SIG_OUT_bTarYawRVld);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fTireRollgRFrnt,&VSE2DiDyna.g_VSE_SIG_OUT_fTireRollgRFrnt);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fTireRollgRRe,&VSE2DiDyna.g_VSE_SIG_OUT_fTireRollgRRe);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_u8SysSts,&VSE2DiDyna.g_VSE_SIG_OUT_u8SysSts);

    RteSetVSE_to_DiDynaOutput(&VSE2DiDyna);
    }
}

#endif 

/*********************************************************
 * Function Name: VSE_GetDataFromEPSA
 * Description  : VSE与EPSA板间通信，VSE获取EPSA信号
 * Parameter    : null
 * return       : null
 *********************************************************/
#if (TASK_InnerCom_EPSA_ENABLE == VSE_RTE_ON)
void VSE_GetDataFromEPSA(void)
{
    //采用数据隔离，函数调用方式
    if(TASK_InnerCom_FORMAT_EPSA == VSE_RTE_ON)  
    {
    EPSA_to_VSE tEPSA_Out;
    tEPSA_Out = RteGetEPSA_to_VSEInput();
    VSE_RTE_SIG_u16RWhlSteerAg = tEPSA_Out.g_EPSA_Out_com_fDsA_RWhlAgl;
    VSE_RTE_SIG_bRWhlSteerAgSts = tEPSA_Out.g_EPSA_Out_com_u8DsA_RWhlAgl_VD;
    }
    //采用全局变量方式获取
    else
    {
    VSE_RTE_SIG_u16RWhlSteerAg = g_EPSA_Out_com_fDsA_RWhlAgl;
    VSE_RTE_SIG_bRWhlSteerAgSts = g_EPSA_Out_com_u8DsA_RWhlAgl_VD;
    }
    

}

/*********************************************************
 * Function Name: VSE_GetDataToEPSA
 * Description  : VSE与EPSA板间通信，VSE输出EPSA信号
 * Parameter    : null
 * return       : null
 *********************************************************/
void VSE_GetDataToEPSA(void)
{
    //采用数据隔离，函数调用方式
    if(TASK_InnerCom_FORMAT_EPSA == VSE_RTE_ON)
    {
    VSE_to_EPSA VSE2EPSA;
    
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fVxEst,&VSE2EPSA.g_VSE_SIG_OUT_fVxEst);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bVxVld,&VSE2EPSA.g_VSE_SIG_OUT_bVxVld);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fFLSlipRate,&VSE2EPSA.g_VSE_SIG_OUT_fFLSlipRate);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fFRSlipRate,&VSE2EPSA.g_VSE_SIG_OUT_fFRSlipRate);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fRLSlipRate,&VSE2EPSA.g_VSE_SIG_OUT_fRLSlipRate);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_fRRSlipRate,&VSE2EPSA.g_VSE_SIG_OUT_fRRSlipRate);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bFLSRVld,&VSE2EPSA.g_VSE_SIG_OUT_bFLSRVld);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bFRSRVld,&VSE2EPSA.g_VSE_SIG_OUT_bFRSRVld);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bRLSRVld,&VSE2EPSA.g_VSE_SIG_OUT_bRLSRVld);
    VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDX_bRRSRVld,&VSE2EPSA.g_VSE_SIG_OUT_bRRSRVld);  
    RteSetVSE_to_EPSAOutput(&VSE2EPSA);
    }  

}
#endif 
