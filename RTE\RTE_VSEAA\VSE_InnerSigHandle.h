﻿
#ifndef VSE_INNERSIGHANDLE_H_
#define VSE_INNERSIGHANDLE_H_

#include "VSE_RteCfg.h"
#include "VSE_CanMRx.h"



/*VSE 输出信号索引*/
typedef struct
{
  void *pCANSigValue;
  VSE_DATA_TYPE_t emSigDataType;
} VSE_SIG_OUT_MAP;

/*Signal Idx*/
typedef enum
{
    VSE_SIG_OUT_IDX_fYawRate,
    VSE_SIG_OUT_IDX_bYawRateVld,
    VSE_SIG_OUT_IDX_fYawRateA,
    VSE_SIG_OUT_IDX_bYawRateAVld,
    VSE_SIG_OUT_IDX_fAy,
    VSE_SIG_OUT_IDX_bAyVld,
    VSE_SIG_OUT_IDX_fAx,
    VSE_SIG_OUT_IDX_bAxVld,
    VSE_SIG_OUT_IDX_fVxEst,
    VSE_SIG_OUT_IDX_fFLSlipRate,
    VSE_SIG_OUT_IDX_fFRSlipRate,
    VSE_SIG_OUT_IDX_fRLSlipRate,
    VSE_SIG_OUT_IDX_fRRSlipRate,
    VSE_SIG_OUT_IDX_bVxVld,
    VSE_SIG_OUT_IDX_bFLSRVld,
    VSE_SIG_OUT_IDX_bFRSRVld,
    VSE_SIG_OUT_IDX_bRLSRVld,
    VSE_SIG_OUT_IDX_bRRSRVld,
    VSE_SIG_OUT_IDX_u8VxDemSts,
    VSE_SIG_OUT_IDX_bVxDirSts,
    VSE_SIG_OUT_IDX_bVxDemVld,
    VSE_SIG_OUT_IDX_bVxDirVld,
    VSE_SIG_OUT_IDX_fCentrWSFL,
    VSE_SIG_OUT_IDX_fCentrWSFR,
    VSE_SIG_OUT_IDX_fCentrWSRL,
    VSE_SIG_OUT_IDX_fCentrWSRR,
    VSE_SIG_OUT_IDX_bCWSVldFL,
    VSE_SIG_OUT_IDX_bCWSVldFR,
    VSE_SIG_OUT_IDX_bCWSVldRL,
    VSE_SIG_OUT_IDX_bCWSVldRR,
    VSE_SIG_OUT_IDX_fSlopAg,
    VSE_SIG_OUT_IDX_fSlopGrdt,
    VSE_SIG_OUT_IDX_bSlopAgVld,
    VSE_SIG_OUT_IDX_fSlopYAg,
    VSE_SIG_OUT_IDX_bSlopYAgVld,
    VSE_SIG_OUT_IDX_fSlopYGrdt,
    VSE_SIG_OUT_IDX_fVehMEstim,
    VSE_SIG_OUT_IDX_fVehSprMEstim,
    VSE_SIG_OUT_IDX_fWhlVFEFL,
    VSE_SIG_OUT_IDX_fWhlVFEFR,
    VSE_SIG_OUT_IDX_fWhlVFERL,
    VSE_SIG_OUT_IDX_fWhlVFERR,
    VSE_SIG_OUT_IDX_bVehMEstimVld,
    VSE_SIG_OUT_IDX_bWhlVFEFLVld,
    VSE_SIG_OUT_IDX_bWhlVFEFRVld,
    VSE_SIG_OUT_IDX_bWhlVFERLVld,
    VSE_SIG_OUT_IDX_bWhlVFERRVld,
    VSE_SIG_OUT_IDX_fFLAdhCoeff,
    VSE_SIG_OUT_IDX_fRLAdhCoeff,
    VSE_SIG_OUT_IDX_fFRAdhCoeff,
    VSE_SIG_OUT_IDX_fRRAdhCoeff,
    VSE_SIG_OUT_IDX_bFLACoeffVld,
    VSE_SIG_OUT_IDX_bFRACoeffVld,
    VSE_SIG_OUT_IDX_bRLACoeffVld,
    VSE_SIG_OUT_IDX_bRRACoeffVld,
    VSE_SIG_OUT_IDX_u8FLTypIdn,
    VSE_SIG_OUT_IDX_u8FRTypIdn,
    VSE_SIG_OUT_IDX_u8RLTypIdn,
    VSE_SIG_OUT_IDX_u8RRTypIdn,
    VSE_SIG_OUT_IDX_bFLTypIdnVld,
    VSE_SIG_OUT_IDX_bFRTypIdnVld,
    VSE_SIG_OUT_IDX_bRLTypIdnVld,
    VSE_SIG_OUT_IDX_bRRTypIdnVld,
    VSE_SIG_OUT_IDX_fSpliFLMu,
    VSE_SIG_OUT_IDX_fSpliFRMu,
    VSE_SIG_OUT_IDX_fSpliRLMu,
    VSE_SIG_OUT_IDX_fSpliRRMu,
    VSE_SIG_OUT_IDX_bSpliFLMuVld,
    VSE_SIG_OUT_IDX_bSpliFRMuVld,
    VSE_SIG_OUT_IDX_bSpliRLMuVld,
    VSE_SIG_OUT_IDX_bSpliRRMuVld,
    VSE_SIG_OUT_IDX_bSplitFlg,
    VSE_SIG_OUT_IDX_fSplitReliable,
    VSE_SIG_OUT_IDX_u8SplitTyp,
    VSE_SIG_OUT_IDX_u8DamSig,
    VSE_SIG_OUT_IDX_fVehYSpd,
    VSE_SIG_OUT_IDX_bVehYSpdValid,
    VSE_SIG_OUT_IDX_fSideSlipAg,
    VSE_SIG_OUT_IDX_bSSAValid,
    VSE_SIG_OUT_IDX_fRollAgEst,
    VSE_SIG_OUT_IDX_bRollAgEstVld,
    VSE_SIG_OUT_IDX_fRollRateEst,
    VSE_SIG_OUT_IDX_bRollRateEstVld,
    VSE_SIG_OUT_IDX_fPitchAgEst,
    VSE_SIG_OUT_IDX_bPitchAgEstVld,
    VSE_SIG_OUT_IDX_fPitchRateEst,
    VSE_SIG_OUT_IDX_bPitchRateEstVld,
    VSE_SIG_OUT_IDX_fTarYawR,
    VSE_SIG_OUT_IDX_bTarYawRVld,
    VSE_SIG_OUT_IDX_u8VSESoftVersNr0,
    VSE_SIG_OUT_IDX_u8VSESoftVersNr1,
    VSE_SIG_OUT_IDX_u8VSESoftVersNr2,
    VSE_SIG_OUT_IDX_u8VSESoftVersNr3,
    VSE_SIG_OUT_IDX_u8VSESoftVersNr4,
    VSE_SIG_OUT_IDX_u8VSESoftVersNr5,
    VSE_SIG_OUT_IDX_u8VSESoftVersNr6,
    VSE_SIG_OUT_IDX_u8VSESoftVersNr7,
    VSE_SIG_OUT_IDX_u8VSESoftVersNr8,
    VSE_SIG_OUT_IDX_u8VSESoftVersNr9,
    VSE_SIG_OUT_IDX_fTireRollgRFrnt,
    VSE_SIG_OUT_IDX_fTireRollgRRe,
    VSE_SIG_OUT_IDX_u8SysSts,
    VSE_SIG_OUT_IDX_NUM,
} VSE_SIG_OUT_IDXS_t;

extern uint16_T g_EPSA_Out_com_fDsA_RWhlAgl;
extern uint8_T g_EPSA_Out_com_u8DsA_RWhlAgl_VD;

extern void VSE_SigOutMapInit(void);
extern void VSE_RTE_SIG_OUT_READ(VSE_SIG_OUT_IDXS_t idx, void *g_VSE_Out_Sig_Addr);

#if (TASK_InnerCom_DiSus_ENABLE == VSE_RTE_ON)
extern void VSE_GetDataFromDiSus(void);
#endif

#if (TASK_InnerCom_DiDyna_ENABLE == VSE_RTE_ON)
extern void VSE_GetDataToDiDyna(void);
#endif

#if (TASK_InnerCom_EPSA_ENABLE == VSE_RTE_ON)
extern void VSE_GetDataFromEPSA(void);
extern void VSE_GetDataToEPSA(void);
#endif





#endif

