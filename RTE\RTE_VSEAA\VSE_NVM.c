#include "VSE_NVM.h"

/*****************************************************************
函数名：VSE_VehicleCoding_Read
函数功能：从NVM读出车型配置字
参    数：无
返    回：无
******************************************************************/

void VSE_VehicleCoding_Read(void)
 {
     uint8_t data_buf[8] = {0u};
     ErrCode ret;
     ret = (ErrCode)ShrExp_Get_VehicleCfgData(data_buf, 8U);  ////带校验位读出

    if (ret == (int)ERR_SUCCESS)
    {
     /* Byte1-Byte2车型代码 Byte3ECU名称 Byte4-Byte5供应商名称 Byte6车型配置 Byte7预留 Byte8校验位*/
     VSE_RTE_SIG_u16CarType =((uint16_t)data_buf[0] << 8U) | ((uint16_t)data_buf[1]);
     VSE_RTE_SIG_u8CarConfig=(uint8_t)data_buf[5];

    }
    else
    {
     VSE_RTE_SIG_u16CarType= 0u;
     VSE_RTE_SIG_u8CarConfig= 0u;
    }
    
 }

//VSE NVM信号
static VSE_SIG_NVM_MAP VSE_SigNVMMap[VSE_SIG_NVM_IDX_NUM];


/*****************************************************************
函数名：VSE_SigNVMMapInit
函数功能：NVM数组初始化
参    数：无
返    回：无
******************************************************************/

void VSE_SigNVMMapInit(void)
{
    VSE_SigNVMMap[VSE_SIG_NVM_IDX_u16CarType].pCANSigValue=&VSE_RTE_SIG_u16CarType;         VSE_SigNVMMap[VSE_SIG_NVM_IDX_u16CarType].emSigSrcType=VSE_SIGNAL_SOURCE_u16CarType;       VSE_SigNVMMap[VSE_SIG_NVM_IDX_u16CarType].emSigDataType = VSE_TYPE_UINT16;
    VSE_SigNVMMap[VSE_SIG_NVM_IDX_u8CarConfig].pCANSigValue=&VSE_RTE_SIG_u8CarConfig;         VSE_SigNVMMap[VSE_SIG_NVM_IDX_u8CarConfig].emSigSrcType=VSE_SIGNAL_SOURCE_u8CarConfig;   VSE_SigNVMMap[VSE_SIG_NVM_IDX_u8CarConfig].emSigDataType = VSE_TYPE_UINT8;
}

/*********************************************************
 * Function Name: VSE_RTE_SIG_OUT_READ
 * Description  : VSE输出信号获取
 * Parameter    : Par1: VSE_SIG_OUT_IDXS_t:需要获取的信号对应的索引  
 *                Par2:g_VSE_Out_Sig_Addr:获取到信号的地址
 * return       : null
 *********************************************************/
void VSE_RTE_SIG_NVM_READ(VSE_SIG_NVM_IDXS_t idx, void *g_VSE_Out_Sig_Addr)
{
    if (NULL != VSE_SigNVMMap[idx].pCANSigValue)
    {
        switch (VSE_SigNVMMap[idx].emSigDataType)
        {
        case VSE_TYPE_UINT8:
            *(uint8_T *)(g_VSE_Out_Sig_Addr) = *(uint8_T *)(VSE_SigNVMMap[idx].pCANSigValue);
            break;

        case VSE_TYPE_UINT16:
            *(uint16_T *)(g_VSE_Out_Sig_Addr) = *(uint16_T *)(VSE_SigNVMMap[idx].pCANSigValue);
            break;

        case VSE_TYPE_INT16:
            *(int16_T *)(g_VSE_Out_Sig_Addr) = *(int16_T *)(VSE_SigNVMMap[idx].pCANSigValue);
            break;

        case VSE_TYPE_UINT32:
            *(uint32_T *)(g_VSE_Out_Sig_Addr) = *(uint32_T *)(VSE_SigNVMMap[idx].pCANSigValue);
            break;

        case VSE_TYPE_FLOAT:
            *(real32_T *)(g_VSE_Out_Sig_Addr) = *(real32_T *)(VSE_SigNVMMap[idx].pCANSigValue);
            break;

        default:
            /*default*/
            break;
        }
    }
}




