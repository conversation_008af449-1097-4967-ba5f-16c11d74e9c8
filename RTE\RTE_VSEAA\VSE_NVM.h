﻿
#ifndef VSE_NVM_H_
#define VSE_NVM_H_

#include "ErrorCode.h"
#include "SignalMgr.h"
#include "VSE_SignalInRTE.h"
#include "VSE_CanMRx.h"
#include "VSE_RteCfg.h"

/*VSE NVM信号索引*/
typedef struct
{
  void *pCANSigValue;
  VSE_DATA_TYPE_t emSigDataType;
  VSE_SIG_SRC_TYPE_t emSigSrcType;
} VSE_SIG_NVM_MAP;

/*Signal Idx*/
typedef enum
{
    VSE_SIG_NVM_IDX_u16CarType,
    VSE_SIG_NVM_IDX_u8CarConfig,
    VSE_SIG_NVM_IDX_NUM,
} VSE_SIG_NVM_IDXS_t;


extern void VSE_VehicleCoding_Read(void);//读取整车配置字
extern void VSE_SigNVMMapInit(void);
extern void VSE_RTE_SIG_NVM_READ(VSE_SIG_NVM_IDXS_t idx, void *g_VSE_Out_Sig_Addr);

#endif

