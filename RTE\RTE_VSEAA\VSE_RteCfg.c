#include "VSE_RteCfg.h"
#include "kernel.h"


/*********************************************************
 * Function Name: VSE_CanMRxMain
 * Description  : VSE主函数，进行VSE任务调度
 * Parameter
 * return       : null
 *********************************************************/

void VSE_CanMRxMain(void *param)
{
    // int32_t level;
    dt_tick_t pre_tick = 0;
    VSE_AppInit();                    //VSE APP初始化
    VSE_VehicleCoding_Read();         //车辆配置读取
    VSE_CanRxMonitorFrameInit();      //CAN报文接收监控初始化

    
    VSE_SigSrcMapInit();              //VSE输入信号数组初始化
    VSE_SigOutMapInit();              //VSE输出信号数组初始化
    VSE_SigNVMMapInit();              //VSENVM数组初始化

    // real32_T aa = 0;
    while (true)
    {
        pre_tick = dt_tick_get();

        VSE_CanRxMonitorFrameDeal(10);
        // diff =sys_clock_tick_get();
        Task_VSE_APPIF_10ms();
        // VSE_External_Pla_Signal_Output();
        VSE_CAN_CYCLE_Signal_GET_Handle();
        VSE_Signal_Input();
	
        impl_dt_thread_refresh();
        dt_thread_delay_until(&pre_tick, 10);
    }
}

static uint32_t VSE_CanMRxTaskStk[6000];           //VSE堆栈大小设置

/*********************************************************
 * Function Name: VSE_InitApp
 * Description  : VSE线程创建
 * Parameter
 * return       : null
 *********************************************************/

void VSE_InitApp(void)
{
    dt_thread_attr_t attr = {
        .options = K_TASK_CORE(1),
    };
    attr.name = "VSE_CanMRx";
    attr.prio = 9;
    attr.stackaddr = VSE_CanMRxTaskStk;
    attr.stacksize = sizeof(VSE_CanMRxTaskStk);
    attr.timelimit = 5000U;
    dt_tid_t VSE_CanMRxTaskTcb = 0;
    (void)dt_thread_create(&VSE_CanMRxTaskTcb, &attr, VSE_CanMRxMain, DT_NULL);
    (void)dt_thread_startup(VSE_CanMRxTaskTcb);
}


