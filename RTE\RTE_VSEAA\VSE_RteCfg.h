﻿/***************************************************************
* File Name: VSE_RteCfg.h
*
*Description
*This file is for the configuration of VSE RTE
*
* Copyright (c) 2008 by BYD. All rights reserved.
****************************************************************/

#ifndef VSE_RTECFG_H_
#define VSE_RTECFG_H_

#include "Std_Types.h"
#include "bsw_if.h"




#define VSE_Base_Address                    0x0400//VSE存储基地址
#define VSE_RWSfcn_Switch_Address          (VSE_Base_Address + 1) //VSE后轮转向开关存储地址

#define VSE_RTE_NULL             ((void *)0)
#define VSE_RTE_OFF              (0u)   //功能关闭
#define VSE_RTE_ON               (1u)   //功能打开

/*RTE Task enable switch*/
#define TASK_VSE_ENABLE               ( VSE_RTE_ON )  //VSE使能
#define TASK_VSE_DBG_CANMTX_ENABLE    ( VSE_RTE_OFF )  //VSE发送调试报文开关
#define TASK_VSE_NVM_ENABLE           ( VSE_RTE_ON )  //预留

//VSE调试报文发送通道
//D3  VSE_Debug_CanMTx_Channel0为Adas网，VSE_Debug_CanMTx_Channel1为底盘网，VSE_Debug_CanMTx_Channel2为冗余网
//域控  VSE_Debug_CanMTx_Channel0为底盘网，VSE_Debug_CanMTx_Channel1为车辆网
#define VSE_DebSig_SendChannel VSE_Debug_CanMTx_Channel1

//VSE与其他模块板内通信使能
#define TASK_InnerCom_EPSA_ENABLE           ( VSE_RTE_OFF )  //VSE与EPSA板内通信使能，1:使能  0:不使能
#define TASK_InnerCom_DiSus_ENABLE          ( VSE_RTE_OFF )  //VSE与DiSus板内通信使能，1:使能  0:不使能
#define TASK_InnerCom_DiDyna_ENABLE         ( VSE_RTE_OFF )  //VSE与DiDyna板内通信使能，1:使能  0:不使能

//VSE与其他模块板内通信方式   0：通过全局变量方式  1:通过函数调用，数据隔离方式
#define TASK_InnerCom_FORMAT_EPSA         ( VSE_RTE_OFF )
#define TASK_InnerCom_FORMAT_DiSus        ( VSE_RTE_OFF )  
#define TASK_InnerCom_FORMAT_DiDyna       ( VSE_RTE_OFF )  

/*VSE报文接收校验开关配置*/
#define VSE_CANMRX_MONITER_EN_051  ( VSE_RTE_OFF )
#define VSE_CANMRX_MONITER_EN_0FC  ( VSE_RTE_OFF )
#define VSE_CANMRX_MONITER_EN_342  ( VSE_RTE_ON )
#define VSE_CANMRX_MONITER_EN_12D  ( VSE_RTE_ON )
#define VSE_CANMRX_MONITER_EN_242  ( VSE_RTE_OFF )
#define VSE_CANMRX_MONITER_EN_241  ( VSE_RTE_OFF )
#define VSE_CANMRX_MONITER_EN_251  ( VSE_RTE_ON )
#define VSE_CANMRX_MONITER_EN_122  ( VSE_RTE_OFF )
#define VSE_CANMRX_MONITER_EN_321  ( VSE_RTE_ON )
#define VSE_CANMRX_MONITER_EN_123  ( VSE_RTE_ON )
#define VSE_CANMRX_MONITER_EN_222  ( VSE_RTE_OFF )
#define VSE_CANMRX_MONITER_EN_11F  ( VSE_RTE_ON )
#define VSE_CANMRX_MONITER_EN_218  ( VSE_RTE_OFF )
#define VSE_CANMRX_MONITER_EN_0F4  ( VSE_RTE_OFF )
#define VSE_CANMRX_MONITER_EN_0B1  ( VSE_RTE_OFF )
#define VSE_CANMRX_MONITER_EN_0B2  ( VSE_RTE_OFF )
#define VSE_CANMRX_MONITER_EN_0B3  ( VSE_RTE_OFF )
#define VSE_CANMRX_MONITER_EN_0B4  ( VSE_RTE_OFF )
#define VSE_CANMRX_MONITER_EN_0B5  ( VSE_RTE_OFF )

/*RTE Task priority */
#define TASK_VSE_MONITOR_PRIO         (23)
#define TASK_VSE_PRIO                 (21)
#define TASK_VSE_CANMTX_PRIO          (12)
#define TASK_VSE_NVM_PRIO             (11)

/*RTE Task period. unit: ms */
#define TASK_VSE_CANMTX_PERIOD        (10U)
#define TASK_VSE_PERIOD               (5U)
#define TASK_VSE_NVM_PERIOD           (20U)
#define TASK_VSE_MONITOR_PERIOD       (1U)

/*信号来源配置 CAN报文、板内通讯、NVM*/
//0x051
#define VSE_SIGNAL_SOURCE_u16IMUAx VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_u16IMUAy VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_u16IMUAz VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_u16IMUWx VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_u16IMUWy VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_u16IMUWz VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bIMUAxSt VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bIMUAySt VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bIMUAzSt VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bIMUWxSt VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bIMUWySt VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bIMUWzSt VSE_SIG_SRC_CAN
// 0x0FC
#define VSE_SIGNAL_SOURCE_i16VehTqFL VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_i16VehTqFR VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_i16VehTqRL VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_i16VehTqRR VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bTqStsFL VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bTqStsFR VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bTqStsRL VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bTqStsRR VSE_SIG_SRC_CAN
// 0x109
#define VSE_SIGNAL_SOURCE_u16DamprPosnFL VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_u16DamprPosnFR VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_u16DamprPosnRL VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_u16DamprPosnRR VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bDamprPosnFLSts VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bDamprPosnFRSts VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bDamprPosnRLSts VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bDamprPosnRRSts VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_u16FLActualCurrent VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_u16FRActualCurrent VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_u16RLActualCurrent VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_u16RRActualCurrent VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_u8DiSusModExeSts VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_u8DiSus_Type VSE_SIG_SRC_CAN
// 0x112
#define VSE_SIGNAL_SOURCE_u16RWhlSteerAg VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bRWhlSteerAgSts VSE_SIG_SRC_CAN
// 0x11F
#define VSE_SIGNAL_SOURCE_i16SteerAg VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_u16SteerAgSpd VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bSteerAgSnsSt VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bSteerAgCASnsSt VSE_SIG_SRC_CAN
// 0x122
#define VSE_SIGNAL_SOURCE_u16FLWhlSpd VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_u16FRWhlSpd VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_u16RLWhlSpd VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_u16RRWhlSpd VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bWhlSpdFLSt VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bWhlSpdFRSt VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bWhlSpdRLSt VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bWhlSpdRRSt VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bABSActS VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bABSFlt VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bTCSActS VSE_SIG_SRC_CAN
// 0x123
#define VSE_SIGNAL_SOURCE_u8IPBBrkSts VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bTCSFlt VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bVDCFlt VSE_SIG_SRC_CAN
// 0x12D
#define VSE_SIGNAL_SOURCE_u8PwrGear VSE_SIG_SRC_CAN
// 0x218
#define VSE_SIGNAL_SOURCE_u8EPBSt VSE_SIG_SRC_CAN  
// 0x222
#define VSE_SIGNAL_SOURCE_bVDCActS VSE_SIG_SRC_CAN
// 0x241
#define VSE_SIGNAL_SOURCE_u16FrntMotTq VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bFrntMotTqSts VSE_SIG_SRC_CAN
// 0x251
#define VSE_SIGNAL_SOURCE_u16ReMotTq VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bReMotTqSts VSE_SIG_SRC_CAN
// 0x321
#define VSE_SIGNAL_SOURCE_u16IPBPPrs VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bIPBPluPreSts VSE_SIG_SRC_CAN
// 0x342
#define VSE_SIGNAL_SOURCE_u8AccrPedlRate VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bAccrPedlRateFlg VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_u8BrkDepth VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bBrkDepthSts VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_u8VehDrvMod VSE_SIG_SRC_CAN
// 0x242
#define VSE_SIGNAL_SOURCE_u8GearPosn VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bGearSts VSE_SIG_SRC_CAN
// 0x258
#define VSE_SIGNAL_SOURCE_bDiSusHeiAdjSts VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_u8DiSusHeiAdjProc VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bDiSusHeiAdjFltSts VSE_SIG_SRC_CAN

// 0x116
// #define VSE_SIGNAL_SOURCE_u8FLWhlBraSts VSE_SIG_SRC_CAN
// #define VSE_SIGNAL_SOURCE_u8FRWhlBraSts VSE_SIG_SRC_CAN
// #define VSE_SIGNAL_SOURCE_u8RLWhlBraSts VSE_SIG_SRC_CAN
// #define VSE_SIGNAL_SOURCE_u8RRWhlBraSts VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_i16FLBrkTqExecu VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_i16FRBrkTqExecu VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_i16RLBrkTqExecu VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_i16RRBrkTqExecu VSE_SIG_SRC_CAN

//Others
#define VSE_SIGNAL_SOURCE_u16CmpActIFL VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_u16StchActIFL VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_u16CmpActIFR VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_u16StchActIFR VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_u16CmpActIRL VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_u16StchActIRL VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_u16CmpActIRR VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_u16StchActIRR VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_u16EstimdFFL VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_u16EstimdFFR VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_u16EstimdFRL VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_u16EstimdFRR VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_i16DamprWhlHFL VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_i16DamprWhlHFR VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_i16DamprWhlHRL VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_i16DamprWhlHRR VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_SNS_u16FLWhlSpd VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_SNS_u16FRWhlSpd VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_SNS_u16RLWhlSpd VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_SNS_u16RRWhlSpd VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_SNS_bWhlSpdFLSt VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_SNS_bWhlSpdFRSt VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_SNS_bWhlSpdRLSt VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_SNS_bWhlSpdRRSt VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_u16CarType VSE_SIG_SRC_NVM
#define VSE_SIGNAL_SOURCE_u8CarConfig VSE_SIG_SRC_NVM
//通信故障标志位
#define VSE_SIGNAL_SOURCE_bIMU_051Flt VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bVCU_0FCFlt VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bVCU_342Flt VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bVCU_12DFlt VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bVCU_242Flt VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bVCU_241Flt VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bVCU_251Flt VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bIPB_122Flt VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bIPB_321Flt VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bIPB_123Flt VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bIPB_222Flt VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bEPS_11FFlt VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bEPB_218Flt VSE_SIG_SRC_CAN
#define VSE_SIGNAL_SOURCE_bCCU_0F4Flt VSE_SIG_SRC_CAN



//VSE数据类型定义
typedef signed char int8_T;
typedef unsigned char uint8_T;
typedef short int16_T;
typedef unsigned short uint16_T;
typedef int int32_T;
typedef unsigned int uint32_T;
typedef long long int64_T;
typedef unsigned long long uint64_T;
typedef float real32_T;
typedef double real64_T;


typedef enum
{   
    #if(VSE_CANMRX_MONITER_EN_051 == VSE_RTE_ON)
        VSE_IDX_0x051,
    #endif
    #if(VSE_CANMRX_MONITER_EN_0FC == VSE_RTE_ON)
        VSE_IDX_0x0FC,
    #endif
    #if(VSE_CANMRX_MONITER_EN_342 == VSE_RTE_ON)
        VSE_IDX_0x342,
    #endif
    #if(VSE_CANMRX_MONITER_EN_12D == VSE_RTE_ON)
        VSE_IDX_0x12D,
    #endif
    #if(VSE_CANMRX_MONITER_EN_242 == VSE_RTE_ON)
        VSE_IDX_0x242,
    #endif
    #if(VSE_CANMRX_MONITER_EN_241 == VSE_RTE_ON)
        VSE_IDX_0x241,
    #endif
    #if(VSE_CANMRX_MONITER_EN_251 == VSE_RTE_ON)
        VSE_IDX_0x251,
    #endif
    #if(VSE_CANMRX_MONITER_EN_122 == VSE_RTE_ON)
        VSE_IDX_0x122,
    #endif
    #if(VSE_CANMRX_MONITER_EN_321 == VSE_RTE_ON)
        VSE_IDX_0x321,
    #endif
    #if(VSE_CANMRX_MONITER_EN_123 == VSE_RTE_ON)
        VSE_IDX_0x123,
    #endif
    #if(VSE_CANMRX_MONITER_EN_222 == VSE_RTE_ON)
        VSE_IDX_0x222,
    #endif
    #if(VSE_CANMRX_MONITER_EN_11F == VSE_RTE_ON)
        VSE_IDX_0x11F,
    #endif
    #if(VSE_CANMRX_MONITER_EN_218 == VSE_RTE_ON)
        VSE_IDX_0x218,
    #endif
    #if(VSE_CANMRX_MONITER_EN_0F4 == VSE_RTE_ON)
        VSE_IDX_0x0F4,
    #endif
    #if(VSE_CANMRX_MONITER_EN_0B1 == VSE_RTE_ON)
        VSE_IDX_0x0B1,
    #endif
    #if(VSE_CANMRX_MONITER_EN_0B2 == VSE_RTE_ON)
        VSE_IDX_0x0B2,
    #endif
    #if(VSE_CANMRX_MONITER_EN_0B3 == VSE_RTE_ON)
        VSE_IDX_0x0B3,
    #endif
    #if(VSE_CANMRX_MONITER_EN_0B4 == VSE_RTE_ON)
        VSE_IDX_0x0B4,
    #endif
    #if(VSE_CANMRX_MONITER_EN_0B5 == VSE_RTE_ON)
        VSE_IDX_0x0B5,
    #endif
    VSE_IDX_SIZE,
}VSE_CANMRX_IDX_t;

/*信号来源枚举型定义*/
typedef enum
{
  VSE_SIG_SRC_CAN,
  VSE_SIG_SRC_INNER,
  VSE_SIG_SRC_NVM
}VSE_SIG_SRC_TYPE_t;

/*CAN Debug报文发送通道 枚举型定义*/
typedef enum
{
  VSE_Debug_CanMTx_Channel0,
  VSE_Debug_CanMTx_Channel1,
  VSE_Debug_CanMTx_Channel2,
  VSE_Debug_CanMTx_Channel3,
}VSE_DebugSigSendChannel_t;


extern void VSE_InitApp(void);

#endif



