﻿#include "VSE_SWC.h"

/*********************************************************
 * Function Name: VSE_Signal_Input
 * Description  : 将VSE接收的输入信号传递至ASW输入信号
 * Parameter    : void
 * return       : null
 *********************************************************/

void VSE_Signal_Input(void)
{
	VSE_RTE_READ_CAN(VSE_SIG_IDX_u16IMUAx,&g_VSE_In_Com_u16IMUAx);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_u16IMUAy,&g_VSE_In_Com_u16IMUAy);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_u16IMUAz,&g_VSE_In_Com_u16IMUAz);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_u16IMUWx,&g_VSE_In_Com_u16IMUWx);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_u16IMUWy,&g_VSE_In_Com_u16IMUWy);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_u16IMUWz,&g_VSE_In_Com_u16IMUWz);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_bIMUAxSt,&g_VSE_In_Com_bIMUAxSt);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_bIMUAySt,&g_VSE_In_Com_bIMUAySt);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_bIMUAzSt,&g_VSE_In_Com_bIMUAzSt);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_bIMUWxSt,&g_VSE_In_Com_bIMUWxSt);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_bIMUWySt,&g_VSE_In_Com_bIMUWySt);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_bIMUWzSt,&g_VSE_In_Com_bIMUWzSt);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_i16VehTqFL,&g_VSE_In_Com_i16VehTqFL);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_i16VehTqFR,&g_VSE_In_Com_i16VehTqFR);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_i16VehTqRL,&g_VSE_In_Com_i16VehTqRL);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_i16VehTqRR,&g_VSE_In_Com_i16VehTqRR);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_bTqStsFL,&g_VSE_In_Com_bTqStsFL);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_bTqStsFR,&g_VSE_In_Com_bTqStsFR);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_bTqStsRL,&g_VSE_In_Com_bTqStsRL);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_bTqStsRR,&g_VSE_In_Com_bTqStsRR);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_u16FrntMotTq,&g_VSE_In_Com_u16FrntMotTq);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_u16ReMotTq,&g_VSE_In_Com_u16ReMotTq);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_bFrntMotTqSts,&g_VSE_In_Com_bFrntMotTqSts);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_bReMotTqSts,&g_VSE_In_Com_bReMotTqSts);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_u16FLWhlSpd,&g_VSE_In_Com_u16FLWhlSpd);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_u16FRWhlSpd,&g_VSE_In_Com_u16FRWhlSpd);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_u16RLWhlSpd,&g_VSE_In_Com_u16RLWhlSpd);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_u16RRWhlSpd,&g_VSE_In_Com_u16RRWhlSpd);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_bWhlSpdFLSt,&g_VSE_In_Com_bWhlSpdFLSt);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_bWhlSpdFRSt,&g_VSE_In_Com_bWhlSpdFRSt);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_bWhlSpdRLSt,&g_VSE_In_Com_bWhlSpdRLSt);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_bWhlSpdRRSt,&g_VSE_In_Com_bWhlSpdRRSt);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_u8AccrPedlRate,&g_VSE_In_Com_u8AccrPedlRate);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_bAccrPedlRateFlg,&g_VSE_In_Com_bAccrPedlRateFlg);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_u8IPBBrkSts,&g_VSE_In_Com_u8IPBBrkSts);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_u8BrkDepth,&g_VSE_In_Com_u8BrkDepth);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_bBrkDepthSts,&g_VSE_In_Com_bBrkDepthSts);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_u16IPBPPrs,&g_VSE_In_Com_u16IPBPPrs);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_bIPBPluPreSts,&g_VSE_In_Com_bIPBPluPreSts);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_u8GearPosn,&g_VSE_In_Com_u8GearPosn);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_bGearSts,&g_VSE_In_Com_bGearSts);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_i16SteerAg,&g_VSE_In_Com_i16SteerAg);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_u16SteerAgSpd,&g_VSE_In_Com_u16SteerAgSpd);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_bSteerAgSnsSt,&g_VSE_In_Com_bSteerAgSnsSt);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_bSteerAgCASnsSt,&g_VSE_In_Com_bSteerAgCASnsSt);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_bTCSActS,&g_VSE_In_Com_bTCSActS);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_bTCSFlt,&g_VSE_In_Com_bTCSFlt);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_bABSActS,&g_VSE_In_Com_bABSActS);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_bABSFlt,&g_VSE_In_Com_bABSFlt);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_bVDCActS,&g_VSE_In_Com_bVDCActS);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_bVDCFlt,&g_VSE_In_Com_bVDCFlt);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_u8VehDrvMod,&g_VSE_In_Com_u8VehDrvMod);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_u8PwrGear,&g_VSE_In_Com_u8PwrGear);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_u8EPBSt,&g_VSE_In_Com_u8EPBSt);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_u16CmpActIFL,&g_VSE_In_Sns_u16CmpActIFL);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_u16StchActIFL,&g_VSE_In_Sns_u16StchActIFL);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_u16CmpActIFR,&g_VSE_In_Sns_u16CmpActIFR);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_u16StchActIFR,&g_VSE_In_Sns_u16StchActIFR);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_u16CmpActIRL,&g_VSE_In_Sns_u16CmpActIRL);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_u16StchActIRL,&g_VSE_In_Sns_u16StchActIRL);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_u16CmpActIRR,&g_VSE_In_Sns_u16CmpActIRR);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_u16StchActIRR,&g_VSE_In_Sns_u16StchActIRR);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_u16EstimdFFL,&g_VSE_In_Com_u16EstimdFFL);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_u16EstimdFFR,&g_VSE_In_Com_u16EstimdFFR);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_u16EstimdFRL,&g_VSE_In_Com_u16EstimdFRL);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_u16EstimdFRR,&g_VSE_In_Com_u16EstimdFRR);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_i16DamprWhlHFL,&g_VSE_In_Sns_i16DamprWhlHFL);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_i16DamprWhlHFR,&g_VSE_In_Sns_i16DamprWhlHFR);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_i16DamprWhlHRL,&g_VSE_In_Sns_i16DamprWhlHRL);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_i16DamprWhlHRR,&g_VSE_In_Sns_i16DamprWhlHRR);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_SNS_u16FLWhlSpd,&g_VSE_In_Sns_u16FLWhlSpd);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_SNS_u16FRWhlSpd,&g_VSE_In_Sns_u16FRWhlSpd);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_SNS_u16RLWhlSpd,&g_VSE_In_Sns_u16RLWhlSpd);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_SNS_u16RRWhlSpd,&g_VSE_In_Sns_u16RRWhlSpd);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_SNS_bWhlSpdFLSt,&g_VSE_In_Sns_bWhlSpdFLSt);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_SNS_bWhlSpdFRSt,&g_VSE_In_Sns_bWhlSpdFRSt);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_SNS_bWhlSpdRLSt,&g_VSE_In_Sns_bWhlSpdRLSt);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_SNS_bWhlSpdRRSt,&g_VSE_In_Sns_bWhlSpdRRSt);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_bIMU_051Flt,&g_VSE_In_Mem_bIMU_051Flt);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_bVCU_0FCFlt,&g_VSE_In_Mem_bVCU_0FCFlt);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_bVCU_342Flt,&g_VSE_In_Mem_bVCU_342Flt);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_bVCU_12DFlt,&g_VSE_In_Mem_bVCU_12DFlt);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_bVCU_242Flt,&g_VSE_In_Mem_bVCU_242Flt);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_bVCU_241Flt,&g_VSE_In_Mem_bVCU_241Flt);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_bVCU_251Flt,&g_VSE_In_Mem_bVCU_251Flt);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_bIPB_122Flt,&g_VSE_In_Mem_bIPB_122Flt);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_bIPB_321Flt,&g_VSE_In_Mem_bIPB_321Flt);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_bIPB_123Flt,&g_VSE_In_Mem_bIPB_123Flt);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_bIPB_222Flt,&g_VSE_In_Mem_bIPB_222Flt);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_bEPS_11FFlt,&g_VSE_In_Mem_bEPS_11FFlt);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_bEPB_218Flt,&g_VSE_In_Mem_bEPB_218Flt);
	VSE_RTE_READ_CAN(VSE_SIG_IDX_bCCU_0F4Flt,&g_VSE_In_Mem_bCCU_0F4Flt);
/*
    0x116,预留
    VSE_RTE_READ_CAN(VSE_SIG_IDX_u8FLWhlBraSts,&g_VSE_In_Com_u8FLWhlBraSts);
    VSE_RTE_READ_CAN(VSE_SIG_IDX_u8FRWhlBraSts,&g_VSE_In_Com_u8FRWhlBraSts);
    VSE_RTE_READ_CAN(VSE_SIG_IDX_u8RLWhlBraSts,&g_VSE_In_Com_u8RLWhlBraSts);
    VSE_RTE_READ_CAN(VSE_SIG_IDX_u8RRWhlBraSts,&g_VSE_In_Com_u8RRWhlBraSts);
    */
    VSE_RTE_READ_CAN(VSE_SIG_IDX_i16FLBrkTqExecu,&g_VSE_In_Com_i16FLBrkTqExecu);
    VSE_RTE_READ_CAN(VSE_SIG_IDX_i16FRBrkTqExecu,&g_VSE_In_Com_i16FRBrkTqExecu);
    VSE_RTE_READ_CAN(VSE_SIG_IDX_i16RLBrkTqExecu,&g_VSE_In_Com_i16RLBrkTqExecu);
    VSE_RTE_READ_CAN(VSE_SIG_IDX_i16RRBrkTqExecu,&g_VSE_In_Com_i16RRBrkTqExecu);



    //CAN ID 0x109 & CAN ID 0x258
    if((VSE_SIGNAL_SOURCE_u16DamprPosnFL==VSE_SIG_SRC_CAN)&& \
    (VSE_SIGNAL_SOURCE_u16DamprPosnFR==VSE_SIG_SRC_CAN)&& \
    (VSE_SIGNAL_SOURCE_u16DamprPosnRL==VSE_SIG_SRC_CAN)&& \
    (VSE_SIGNAL_SOURCE_u16DamprPosnRR==VSE_SIG_SRC_CAN)&& \
    (VSE_SIGNAL_SOURCE_bDamprPosnFLSts==VSE_SIG_SRC_CAN)&& \
    (VSE_SIGNAL_SOURCE_bDamprPosnFRSts==VSE_SIG_SRC_CAN)&& \
    (VSE_SIGNAL_SOURCE_bDamprPosnRLSts==VSE_SIG_SRC_CAN)&& \
    (VSE_SIGNAL_SOURCE_bDamprPosnRRSts==VSE_SIG_SRC_CAN)&& \
    (VSE_SIGNAL_SOURCE_u16FLActualCurrent==VSE_SIG_SRC_CAN)&& \
    (VSE_SIGNAL_SOURCE_u16FRActualCurrent==VSE_SIG_SRC_CAN)&& \
    (VSE_SIGNAL_SOURCE_u16RLActualCurrent==VSE_SIG_SRC_CAN)&& \
    (VSE_SIGNAL_SOURCE_u16RRActualCurrent==VSE_SIG_SRC_CAN)&& \
    (VSE_SIGNAL_SOURCE_u8DiSusModExeSts==VSE_SIG_SRC_CAN)&& \
    (VSE_SIGNAL_SOURCE_u8DiSus_Type==VSE_SIG_SRC_CAN)&& \
    (VSE_SIGNAL_SOURCE_bDiSusHeiAdjSts==VSE_SIG_SRC_CAN)&& \
    (VSE_SIGNAL_SOURCE_u8DiSusHeiAdjProc==VSE_SIG_SRC_CAN)&& \
    (VSE_SIGNAL_SOURCE_bDiSusHeiAdjFltSts==VSE_SIG_SRC_CAN))
    {
    //CAN ID 0x109
    //减振器高度位置有效性需要特殊处理,当VSE_RTE_SIG_u8DiSus_Type类型为0x05(C平台)时，如果减震器高度为最大值0xFFFF时，则减震器高度有效性为无效，否则为有效
    VSE_RTE_READ_CAN(VSE_SIG_IDX_u16DamprPosnFL,&g_VSE_In_Com_u16DamprPosnFL);
    VSE_RTE_READ_CAN(VSE_SIG_IDX_u16DamprPosnFR,&g_VSE_In_Com_u16DamprPosnFR);
    VSE_RTE_READ_CAN(VSE_SIG_IDX_u16DamprPosnRL,&g_VSE_In_Com_u16DamprPosnRL);
    VSE_RTE_READ_CAN(VSE_SIG_IDX_u16DamprPosnRR,&g_VSE_In_Com_u16DamprPosnRR);
    VSE_RTE_READ_CAN(VSE_SIG_IDX_bDamprPosnFLSts,&g_VSE_In_Com_bDamprPosnFLSts);
    VSE_RTE_READ_CAN(VSE_SIG_IDX_bDamprPosnFRSts,&g_VSE_In_Com_bDamprPosnFRSts);
    VSE_RTE_READ_CAN(VSE_SIG_IDX_bDamprPosnRLSts,&g_VSE_In_Com_bDamprPosnRLSts);
    VSE_RTE_READ_CAN(VSE_SIG_IDX_bDamprPosnRRSts,&g_VSE_In_Com_bDamprPosnRRSts);
    VSE_RTE_READ_CAN(VSE_SIG_IDX_u16FLActualCurrent,&g_VSE_In_Com_u16FLActualCurrent);
    VSE_RTE_READ_CAN(VSE_SIG_IDX_u16FRActualCurrent,&g_VSE_In_Com_u16FRActualCurrent);
    VSE_RTE_READ_CAN(VSE_SIG_IDX_u16RLActualCurrent,&g_VSE_In_Com_u16RLActualCurrent);
    VSE_RTE_READ_CAN(VSE_SIG_IDX_u16RRActualCurrent,&g_VSE_In_Com_u16RRActualCurrent);
    VSE_RTE_READ_CAN(VSE_SIG_IDX_u8DiSusModExeSts,&g_VSE_In_Com_u8DiSusModExeSts);
    VSE_RTE_READ_CAN(VSE_SIG_IDX_u8DiSus_Type,&g_VSE_In_Com_u8DiSus_Type);
    //CAN ID 0x258
    VSE_RTE_READ_CAN(VSE_SIG_IDX_bDiSusHeiAdjSts,&g_VSE_In_Com_bDiSusHeiAdjSts);
    VSE_RTE_READ_CAN(VSE_SIG_IDX_u8DiSusHeiAdjProc,&g_VSE_In_Com_u8DiSusHeiAdjProc);
    VSE_RTE_READ_CAN(VSE_SIG_IDX_bDiSusHeiAdjFltSts,&g_VSE_In_Com_bDiSusHeiAdjFltSts);
    }
    else  if((VSE_SIGNAL_SOURCE_u16DamprPosnFL==VSE_SIG_SRC_INNER)&& \
    (VSE_SIGNAL_SOURCE_u16DamprPosnFR==VSE_SIG_SRC_INNER)&& \
    (VSE_SIGNAL_SOURCE_u16DamprPosnRL==VSE_SIG_SRC_INNER)&& \
    (VSE_SIGNAL_SOURCE_u16DamprPosnRR==VSE_SIG_SRC_INNER)&& \
    (VSE_SIGNAL_SOURCE_bDamprPosnFLSts==VSE_SIG_SRC_INNER)&& \
    (VSE_SIGNAL_SOURCE_bDamprPosnFRSts==VSE_SIG_SRC_INNER)&& \
    (VSE_SIGNAL_SOURCE_bDamprPosnRLSts==VSE_SIG_SRC_INNER)&& \
    (VSE_SIGNAL_SOURCE_bDamprPosnRRSts==VSE_SIG_SRC_INNER)&& \
    (VSE_SIGNAL_SOURCE_u16FLActualCurrent==VSE_SIG_SRC_INNER)&& \
    (VSE_SIGNAL_SOURCE_u16FRActualCurrent==VSE_SIG_SRC_INNER)&& \
    (VSE_SIGNAL_SOURCE_u16RLActualCurrent==VSE_SIG_SRC_INNER)&& \
    (VSE_SIGNAL_SOURCE_u16RRActualCurrent==VSE_SIG_SRC_INNER)&& \
    (VSE_SIGNAL_SOURCE_u8DiSusModExeSts==VSE_SIG_SRC_INNER)&& \
    (VSE_SIGNAL_SOURCE_u8DiSus_Type==VSE_SIG_SRC_INNER)&& \
    (VSE_SIGNAL_SOURCE_bDiSusHeiAdjSts==VSE_SIG_SRC_INNER)&& \
    (VSE_SIGNAL_SOURCE_u8DiSusHeiAdjProc==VSE_SIG_SRC_INNER)&& \
    (VSE_SIGNAL_SOURCE_bDiSusHeiAdjFltSts==VSE_SIG_SRC_INNER))
    {
        #if (TASK_InnerCom_DiSus_ENABLE == VSE_RTE_ON)
        VSE_GetDataFromDiSus();
        #endif
    }
    else
    {
            /* do nothing */
    }
    //CAN ID 0x112
    if((VSE_SIGNAL_SOURCE_u16RWhlSteerAg==VSE_SIG_SRC_CAN)&& \
    (VSE_SIGNAL_SOURCE_bRWhlSteerAgSts==VSE_SIG_SRC_CAN))
    {
    VSE_RTE_READ_CAN(VSE_SIG_IDX_u16RWhlSteerAg,&g_VSE_In_Com_u16RWhlSteerAg);
    VSE_RTE_READ_CAN(VSE_SIG_IDX_bRWhlSteerAgSts,&g_VSE_In_Com_bRWhlSteerAgSts);
    }

    else if((VSE_SIGNAL_SOURCE_u16RWhlSteerAg==VSE_SIG_SRC_INNER)&& \
    (VSE_SIGNAL_SOURCE_bRWhlSteerAgSts==VSE_SIG_SRC_INNER))
    {
        #if (TASK_InnerCom_EPSA_ENABLE == VSE_RTE_ON)
        VSE_GetDataFromEPSA();
        #endif
    }
    else
    {
            /* do nothing */
    }
    //车型配置信息读取
    VSE_RTE_SIG_NVM_READ(VSE_SIG_NVM_IDX_u16CarType,&g_VSE_In_Mem_u16CarType);
    VSE_RTE_SIG_NVM_READ(VSE_SIG_NVM_IDX_u8CarConfig,&g_VSE_In_Mem_u8CarConfig);
}



