﻿#include "VSE_SignalInRTE.h"

/*********************VSE_SignalToASW************************/
// VSE2.0
uint16_T VSE_RTE_SIG_u16IMUAx = 25000U; 
uint16_T VSE_RTE_SIG_u16IMUAy = 25000U; 
uint16_T VSE_RTE_SIG_u16IMUAz = 25000U; 
uint16_T VSE_RTE_SIG_u16IMUWx = 30000U; 
uint16_T VSE_RTE_SIG_u16IMUWy = 30000U; 
uint16_T VSE_RTE_SIG_u16IMUWz = 30000U; 
boolean_T VSE_RTE_SIG_bIMUAxSt = false;
boolean_T VSE_RTE_SIG_bIMUAySt = false;
boolean_T VSE_RTE_SIG_bIMUAzSt = false;
boolean_T VSE_RTE_SIG_bIMUWxSt = false;
boolean_T VSE_RTE_SIG_bIMUWySt = false;
boolean_T VSE_RTE_SIG_bIMUWzSt = false;
int16_T VSE_RTE_SIG_i16VehTqFL = 0;
int16_T VSE_RTE_SIG_i16VehTqFR = 0;
int16_T VSE_RTE_SIG_i16VehTqRL = 0;
int16_T VSE_RTE_SIG_i16VehTqRR = 0;
boolean_T VSE_RTE_SIG_bTqStsFL = false;
boolean_T VSE_RTE_SIG_bTqStsFR = false;
boolean_T VSE_RTE_SIG_bTqStsRL = false;
boolean_T VSE_RTE_SIG_bTqStsRR = false;
uint8_T VSE_RTE_SIG_u8AccrPedlRate = 0U;
boolean_T VSE_RTE_SIG_bAccrPedlRateFlg = true;
uint8_T VSE_RTE_SIG_u8VehDrvMod = 9U;
uint16_T VSE_RTE_SIG_u16FLWhlSpd = 0U;
uint16_T VSE_RTE_SIG_u16FRWhlSpd = 0U;
uint16_T VSE_RTE_SIG_u16RLWhlSpd = 0U;
uint16_T VSE_RTE_SIG_u16RRWhlSpd = 0U;
boolean_T VSE_RTE_SIG_bWhlSpdFLSt = false;
boolean_T VSE_RTE_SIG_bWhlSpdFRSt = false;
boolean_T VSE_RTE_SIG_bWhlSpdRLSt = false;
boolean_T VSE_RTE_SIG_bWhlSpdRRSt = false;
uint16_T VSE_RTE_SIG_u16IPBPPrs = 17U;
boolean_T VSE_RTE_SIG_bIPBPluPreSts = false;
uint8_T VSE_RTE_SIG_u8IPBBrkSts = 0U;
uint8_T VSE_RTE_SIG_u8BrkDepth = 0U;
boolean_T VSE_RTE_SIG_bBrkDepthSts = true;
int16_T VSE_RTE_SIG_i16SteerAg = 0;
uint16_T VSE_RTE_SIG_u16SteerAgSpd = 0U;
boolean_T VSE_RTE_SIG_bSteerAgSnsSt = false;
boolean_T VSE_RTE_SIG_bSteerAgCASnsSt = true;
uint8_T VSE_RTE_SIG_u8PwrGear = 0U; 
uint8_T VSE_RTE_SIG_u8EPBSt = 0U;  
boolean_T VSE_RTE_SIG_bTCSActS = false;
boolean_T VSE_RTE_SIG_bTCSFlt = false; 
uint8_T VSE_RTE_SIG_u8GearPosn = 0U;
boolean_T VSE_RTE_SIG_bGearSts = false;
uint16_T VSE_RTE_SIG_u16FrntMotTq = 0U;
boolean_T VSE_RTE_SIG_bFrntMotTqSts = false;
uint16_T VSE_RTE_SIG_u16ReMotTq = 0U;
boolean_T VSE_RTE_SIG_bReMotTqSts = true;
boolean_T VSE_RTE_SIG_bABSActS = false;
boolean_T VSE_RTE_SIG_bABSFlt = true; 
boolean_T VSE_RTE_SIG_bVDCActS = false;
boolean_T VSE_RTE_SIG_bVDCFlt = true; 
uint16_T VSE_RTE_SIG_u16RWhlSteerAg = 200U;
boolean_T VSE_RTE_SIG_bRWhlSteerAgSts = false;
uint16_T VSE_RTE_SIG_u16DamprPosnFL = 1000U;
uint16_T VSE_RTE_SIG_u16DamprPosnFR = 1000U;
uint16_T VSE_RTE_SIG_u16DamprPosnRL = 1000U;
uint16_T VSE_RTE_SIG_u16DamprPosnRR = 1000U;
boolean_T VSE_RTE_SIG_bDamprPosnFLSts = false;
boolean_T VSE_RTE_SIG_bDamprPosnFRSts = false;
boolean_T VSE_RTE_SIG_bDamprPosnRLSts = false;
boolean_T VSE_RTE_SIG_bDamprPosnRRSts = false;
uint16_T VSE_RTE_SIG_u16CarType;
uint8_T VSE_RTE_SIG_u8CarConfig;
boolean_T VSE_RTE_SIG_bIMU_051Flt = false;
boolean_T VSE_RTE_SIG_bVCU_0FCFlt = false;
boolean_T VSE_RTE_SIG_bVCU_342Flt = false;
boolean_T VSE_RTE_SIG_bVCU_12DFlt = false;
boolean_T VSE_RTE_SIG_bVCU_242Flt = false;
boolean_T VSE_RTE_SIG_bVCU_241Flt = false;
boolean_T VSE_RTE_SIG_bVCU_251Flt = false;
boolean_T VSE_RTE_SIG_bIPB_122Flt = false;
boolean_T VSE_RTE_SIG_bIPB_321Flt = false;
boolean_T VSE_RTE_SIG_bIPB_123Flt = false;
boolean_T VSE_RTE_SIG_bIPB_222Flt = false;
boolean_T VSE_RTE_SIG_bEPS_11FFlt = false;
boolean_T VSE_RTE_SIG_bEPB_218Flt = false;
boolean_T VSE_RTE_SIG_bCCU_0F4Flt = false;
uint16_T VSE_RTE_SIG_SNS_u16FLWhlSpd = 0U;
uint16_T VSE_RTE_SIG_SNS_u16FRWhlSpd = 0U;
uint16_T VSE_RTE_SIG_SNS_u16RLWhlSpd = 0U;
uint16_T VSE_RTE_SIG_SNS_u16RRWhlSpd = 0U;
boolean_T VSE_RTE_SIG_SNS_bWhlSpdFLSt = false;
boolean_T VSE_RTE_SIG_SNS_bWhlSpdFRSt = false;
boolean_T VSE_RTE_SIG_SNS_bWhlSpdRLSt = false;
boolean_T VSE_RTE_SIG_SNS_bWhlSpdRRSt = false;
uint16_T VSE_RTE_SIG_u16FLActualCurrent;
uint16_T VSE_RTE_SIG_u16FRActualCurrent;
uint16_T VSE_RTE_SIG_u16RLActualCurrent;
uint16_T VSE_RTE_SIG_u16RRActualCurrent;
uint8_T VSE_RTE_SIG_u8DiSusModExeSts;
uint8_T VSE_RTE_SIG_u8DiSus_Type;
boolean_T VSE_RTE_SIG_bDiSusHeiAdjSts;
uint8_T VSE_RTE_SIG_u8DiSusHeiAdjProc;
boolean_T VSE_RTE_SIG_bDiSusHeiAdjFltSts;
uint16_T VSE_RTE_SIG_u16CmpActIFL;
uint16_T VSE_RTE_SIG_u16StchActIFL;
uint16_T VSE_RTE_SIG_u16CmpActIFR;
uint16_T VSE_RTE_SIG_u16StchActIFR;
uint16_T VSE_RTE_SIG_u16CmpActIRL;
uint16_T VSE_RTE_SIG_u16StchActIRL;
uint16_T VSE_RTE_SIG_u16CmpActIRR;
uint16_T VSE_RTE_SIG_u16StchActIRR;
uint16_T VSE_RTE_SIG_u16EstimdFFL;
uint16_T VSE_RTE_SIG_u16EstimdFFR;
uint16_T VSE_RTE_SIG_u16EstimdFRL;
uint16_T VSE_RTE_SIG_u16EstimdFRR;
int16_T VSE_RTE_SIG_i16DamprWhlHFL;
int16_T VSE_RTE_SIG_i16DamprWhlHFR;
int16_T VSE_RTE_SIG_i16DamprWhlHRL;
int16_T VSE_RTE_SIG_i16DamprWhlHRR;

uint8_T VSE_RTE_SIG_u8FLWhlBraSts = 0U;
uint8_T VSE_RTE_SIG_u8FRWhlBraSts = 0U;
uint8_T VSE_RTE_SIG_u8RLWhlBraSts = 0U;
uint8_T VSE_RTE_SIG_u8RRWhlBraSts = 0U;
int16_T VSE_RTE_SIG_i16FLBrkTqExecu = 30000;
int16_T VSE_RTE_SIG_i16FRBrkTqExecu = 30000;
int16_T VSE_RTE_SIG_i16RLBrkTqExecu = 30000;
int16_T VSE_RTE_SIG_i16RRBrkTqExecu = 30000;




// Inject_Default 故障注入默认值
uint16_T VSE_Inj_Dft_u16IMUAx; 
uint16_T VSE_Inj_Dft_u16IMUAy; 
uint16_T VSE_Inj_Dft_u16IMUAz; 
uint16_T VSE_Inj_Dft_u16IMUWx; 
uint16_T VSE_Inj_Dft_u16IMUWy; 
uint16_T VSE_Inj_Dft_u16IMUWz; 
boolean_T VSE_Inj_Dft_bIMUAxSt;
boolean_T VSE_Inj_Dft_bIMUAySt;
boolean_T VSE_Inj_Dft_bIMUAzSt;
boolean_T VSE_Inj_Dft_bIMUWxSt;
boolean_T VSE_Inj_Dft_bIMUWySt;
boolean_T VSE_Inj_Dft_bIMUWzSt;
int16_T VSE_Inj_Dft_i16VehTqFL;
int16_T VSE_Inj_Dft_i16VehTqFR;
int16_T VSE_Inj_Dft_i16VehTqRL;
int16_T VSE_Inj_Dft_i16VehTqRR;
boolean_T VSE_Inj_Dft_bTqStsFL;
boolean_T VSE_Inj_Dft_bTqStsFR;
boolean_T VSE_Inj_Dft_bTqStsRL;
boolean_T VSE_Inj_Dft_bTqStsRR;
uint16_T VSE_Inj_Dft_u16FrntMotTq;
uint16_T VSE_Inj_Dft_u16ReMotTq;
boolean_T VSE_Inj_Dft_bFrntMotTqSts;
boolean_T VSE_Inj_Dft_bReMotTqSts;
uint16_T VSE_Inj_Dft_u16FLWhlSpd;
uint16_T VSE_Inj_Dft_u16FRWhlSpd;
uint16_T VSE_Inj_Dft_u16RLWhlSpd;
uint16_T VSE_Inj_Dft_u16RRWhlSpd;
boolean_T VSE_Inj_Dft_bWhlSpdFLSt;
boolean_T VSE_Inj_Dft_bWhlSpdFRSt;
boolean_T VSE_Inj_Dft_bWhlSpdRLSt;
boolean_T VSE_Inj_Dft_bWhlSpdRRSt;
uint8_T VSE_Inj_Dft_u8AccrPedlRate;
boolean_T VSE_Inj_Dft_bAccrPedlRateFlg;
uint8_T VSE_Inj_Dft_u8IPBBrkSts;
uint8_T VSE_Inj_Dft_u8BrkDepth;
boolean_T VSE_Inj_Dft_bBrkDepthSts;
uint16_T VSE_Inj_Dft_u16IPBPPrs;
boolean_T VSE_Inj_Dft_bIPBPluPreSts;
uint8_T VSE_Inj_Dft_u8GearPosn;
boolean_T VSE_Inj_Dft_bGearSts;
int16_T VSE_Inj_Dft_i16SteerAg;
uint16_T VSE_Inj_Dft_u16SteerAgSpd;
boolean_T VSE_Inj_Dft_bSteerAgSnsSt;
boolean_T VSE_Inj_Dft_bSteerAgCASnsSt;
uint16_T VSE_Inj_Dft_u16RWhlSteerAg;
boolean_T VSE_Inj_Dft_bRWhlSteerAgSts;
boolean_T VSE_Inj_Dft_bTCSActS;
boolean_T VSE_Inj_Dft_bTCSFlt; 
boolean_T VSE_Inj_Dft_bABSActS;
boolean_T VSE_Inj_Dft_bABSFlt; 
boolean_T VSE_Inj_Dft_bVDCActS;
boolean_T VSE_Inj_Dft_bVDCFlt; 
uint8_T VSE_Inj_Dft_u8VehDrvMod;
uint8_T VSE_Inj_Dft_u8PwrGear; 
uint8_T VSE_Inj_Dft_u8EPBSt;   
uint16_T VSE_Inj_Dft_u16DamprPosnFL;
uint16_T VSE_Inj_Dft_u16DamprPosnFR;
uint16_T VSE_Inj_Dft_u16DamprPosnRL;
uint16_T VSE_Inj_Dft_u16DamprPosnRR;
boolean_T VSE_Inj_Dft_bDamprPosnFLSts;
boolean_T VSE_Inj_Dft_bDamprPosnFRSts;
boolean_T VSE_Inj_Dft_bDamprPosnRLSts;
boolean_T VSE_Inj_Dft_bDamprPosnRRSts;
uint16_T VSE_Inj_Dft_u16FLActualCurrent;
uint16_T VSE_Inj_Dft_u16FRActualCurrent;
uint16_T VSE_Inj_Dft_u16RLActualCurrent;
uint16_T VSE_Inj_Dft_u16RRActualCurrent;
uint8_T VSE_Inj_Dft_u8DiSusModExeSts;
uint8_T VSE_Inj_Dft_u8DiSus_Type;
boolean_T VSE_Inj_Dft_bDiSusHeiAdjSts;
uint8_T VSE_Inj_Dft_u8DiSusHeiAdjProc;
boolean_T VSE_Inj_Dft_bDiSusHeiAdjFltSts;
uint16_T VSE_Inj_Dft_u16CmpActIFL;
uint16_T VSE_Inj_Dft_u16StchActIFL;
uint16_T VSE_Inj_Dft_u16CmpActIFR;
uint16_T VSE_Inj_Dft_u16StchActIFR;
uint16_T VSE_Inj_Dft_u16CmpActIRL;
uint16_T VSE_Inj_Dft_u16StchActIRL;
uint16_T VSE_Inj_Dft_u16CmpActIRR;
uint16_T VSE_Inj_Dft_u16StchActIRR;
uint16_T VSE_Inj_Dft_u16EstimdFFL;
uint16_T VSE_Inj_Dft_u16EstimdFFR;
uint16_T VSE_Inj_Dft_u16EstimdFRL;
uint16_T VSE_Inj_Dft_u16EstimdFRR;
int16_T VSE_Inj_Dft_i16DamprWhlHFL;
int16_T VSE_Inj_Dft_i16DamprWhlHFR;
int16_T VSE_Inj_Dft_i16DamprWhlHRL;
int16_T VSE_Inj_Dft_i16DamprWhlHRR;
uint16_T VSE_Inj_Dft_SNS_u16FLWhlSpd;
uint16_T VSE_Inj_Dft_SNS_u16FRWhlSpd;
uint16_T VSE_Inj_Dft_SNS_u16RLWhlSpd;
uint16_T VSE_Inj_Dft_SNS_u16RRWhlSpd;
boolean_T VSE_Inj_Dft_SNS_bWhlSpdFLSt;
boolean_T VSE_Inj_Dft_SNS_bWhlSpdFRSt;
boolean_T VSE_Inj_Dft_SNS_bWhlSpdRLSt;
boolean_T VSE_Inj_Dft_SNS_bWhlSpdRRSt;
uint16_T VSE_Inj_Dft_u16CarType;
uint8_T VSE_Inj_Dft_u8CarConfig;
boolean_T VSE_Inj_Dft_bIMU_051Flt;
boolean_T VSE_Inj_Dft_bVCU_0FCFlt;
boolean_T VSE_Inj_Dft_bVCU_342Flt;
boolean_T VSE_Inj_Dft_bVCU_12DFlt;
boolean_T VSE_Inj_Dft_bVCU_242Flt;
boolean_T VSE_Inj_Dft_bVCU_241Flt;
boolean_T VSE_Inj_Dft_bVCU_251Flt;
boolean_T VSE_Inj_Dft_bIPB_122Flt;
boolean_T VSE_Inj_Dft_bIPB_321Flt;
boolean_T VSE_Inj_Dft_bIPB_123Flt;
boolean_T VSE_Inj_Dft_bIPB_222Flt;
boolean_T VSE_Inj_Dft_bEPS_11FFlt;
boolean_T VSE_Inj_Dft_bEPB_218Flt;
boolean_T VSE_Inj_Dft_bCCU_0F4Flt;
uint8_T VSE_Inj_Dft_u8FLWhlBraSts;
uint8_T VSE_Inj_Dft_u8FRWhlBraSts;
uint8_T VSE_Inj_Dft_u8RLWhlBraSts;
uint8_T VSE_Inj_Dft_u8RRWhlBraSts;
int16_T VSE_Inj_Dft_i16FLBrkTqExecu;
int16_T VSE_Inj_Dft_i16FRBrkTqExecu;
int16_T VSE_Inj_Dft_i16RLBrkTqExecu;
int16_T VSE_Inj_Dft_i16RRBrkTqExecu;


// Inject_Flag 故障注入标志位
boolean_T VSE_Inj_Flg_u16IMUAx; 
boolean_T VSE_Inj_Flg_u16IMUAy; 
boolean_T VSE_Inj_Flg_u16IMUAz; 
boolean_T VSE_Inj_Flg_u16IMUWx; 
boolean_T VSE_Inj_Flg_u16IMUWy; 
boolean_T VSE_Inj_Flg_u16IMUWz; 
boolean_T VSE_Inj_Flg_bIMUAxSt;
boolean_T VSE_Inj_Flg_bIMUAySt;
boolean_T VSE_Inj_Flg_bIMUAzSt;
boolean_T VSE_Inj_Flg_bIMUWxSt;
boolean_T VSE_Inj_Flg_bIMUWySt;
boolean_T VSE_Inj_Flg_bIMUWzSt;
boolean_T VSE_Inj_Flg_i16VehTqFL;
boolean_T VSE_Inj_Flg_i16VehTqFR;
boolean_T VSE_Inj_Flg_i16VehTqRL;
boolean_T VSE_Inj_Flg_i16VehTqRR;
boolean_T VSE_Inj_Flg_bTqStsFL;
boolean_T VSE_Inj_Flg_bTqStsFR;
boolean_T VSE_Inj_Flg_bTqStsRL;
boolean_T VSE_Inj_Flg_bTqStsRR;
boolean_T VSE_Inj_Flg_u16FrntMotTq;
boolean_T VSE_Inj_Flg_u16ReMotTq;
boolean_T VSE_Inj_Flg_bFrntMotTqSts;
boolean_T VSE_Inj_Flg_bReMotTqSts;
boolean_T VSE_Inj_Flg_u16FLWhlSpd;
boolean_T VSE_Inj_Flg_u16FRWhlSpd;
boolean_T VSE_Inj_Flg_u16RLWhlSpd;
boolean_T VSE_Inj_Flg_u16RRWhlSpd;
boolean_T VSE_Inj_Flg_bWhlSpdFLSt;
boolean_T VSE_Inj_Flg_bWhlSpdFRSt;
boolean_T VSE_Inj_Flg_bWhlSpdRLSt;
boolean_T VSE_Inj_Flg_bWhlSpdRRSt;
boolean_T VSE_Inj_Flg_u8AccrPedlRate;
boolean_T VSE_Inj_Flg_bAccrPedlRateFlg;
boolean_T VSE_Inj_Flg_u8IPBBrkSts;
boolean_T VSE_Inj_Flg_u8BrkDepth;
boolean_T VSE_Inj_Flg_bBrkDepthSts;
boolean_T VSE_Inj_Flg_u16IPBPPrs;
boolean_T VSE_Inj_Flg_bIPBPluPreSts;
boolean_T VSE_Inj_Flg_u8GearPosn;
boolean_T VSE_Inj_Flg_bGearSts;
boolean_T VSE_Inj_Flg_i16SteerAg;
boolean_T VSE_Inj_Flg_u16SteerAgSpd;
boolean_T VSE_Inj_Flg_bSteerAgSnsSt;
boolean_T VSE_Inj_Flg_bSteerAgCASnsSt;
boolean_T VSE_Inj_Flg_u16RWhlSteerAg;
boolean_T VSE_Inj_Flg_bRWhlSteerAgSts;
boolean_T VSE_Inj_Flg_bTCSActS;
boolean_T VSE_Inj_Flg_bTCSFlt; 
boolean_T VSE_Inj_Flg_bABSActS;
boolean_T VSE_Inj_Flg_bABSFlt; 
boolean_T VSE_Inj_Flg_bVDCActS;
boolean_T VSE_Inj_Flg_bVDCFlt; 
boolean_T VSE_Inj_Flg_u8VehDrvMod;
boolean_T VSE_Inj_Flg_u8PwrGear; 
boolean_T VSE_Inj_Flg_u8EPBSt;   
boolean_T VSE_Inj_Flg_u16DamprPosnFL;
boolean_T VSE_Inj_Flg_u16DamprPosnFR;
boolean_T VSE_Inj_Flg_u16DamprPosnRL;
boolean_T VSE_Inj_Flg_u16DamprPosnRR;
boolean_T VSE_Inj_Flg_bDamprPosnFLSts;
boolean_T VSE_Inj_Flg_bDamprPosnFRSts;
boolean_T VSE_Inj_Flg_bDamprPosnRLSts;
boolean_T VSE_Inj_Flg_bDamprPosnRRSts;
boolean_T VSE_Inj_Flg_u16FLActualCurrent;
boolean_T VSE_Inj_Flg_u16FRActualCurrent;
boolean_T VSE_Inj_Flg_u16RLActualCurrent;
boolean_T VSE_Inj_Flg_u16RRActualCurrent;
boolean_T VSE_Inj_Flg_u8DiSusModExeSts;
boolean_T VSE_Inj_Flg_u8DiSus_Type;
boolean_T VSE_Inj_Flg_bDiSusHeiAdjSts;
boolean_T VSE_Inj_Flg_u8DiSusHeiAdjProc;
boolean_T VSE_Inj_Flg_bDiSusHeiAdjFltSts;
boolean_T VSE_Inj_Flg_u16CmpActIFL;
boolean_T VSE_Inj_Flg_u16StchActIFL;
boolean_T VSE_Inj_Flg_u16CmpActIFR;
boolean_T VSE_Inj_Flg_u16StchActIFR;
boolean_T VSE_Inj_Flg_u16CmpActIRL;
boolean_T VSE_Inj_Flg_u16StchActIRL;
boolean_T VSE_Inj_Flg_u16CmpActIRR;
boolean_T VSE_Inj_Flg_u16StchActIRR;
boolean_T VSE_Inj_Flg_u16EstimdFFL;
boolean_T VSE_Inj_Flg_u16EstimdFFR;
boolean_T VSE_Inj_Flg_u16EstimdFRL;
boolean_T VSE_Inj_Flg_u16EstimdFRR;
boolean_T VSE_Inj_Flg_i16DamprWhlHFL;
boolean_T VSE_Inj_Flg_i16DamprWhlHFR;
boolean_T VSE_Inj_Flg_i16DamprWhlHRL;
boolean_T VSE_Inj_Flg_i16DamprWhlHRR;
boolean_T VSE_Inj_Flg_SNS_u16FLWhlSpd;
boolean_T VSE_Inj_Flg_SNS_u16FRWhlSpd;
boolean_T VSE_Inj_Flg_SNS_u16RLWhlSpd;
boolean_T VSE_Inj_Flg_SNS_u16RRWhlSpd;
boolean_T VSE_Inj_Flg_SNS_bWhlSpdFLSt;
boolean_T VSE_Inj_Flg_SNS_bWhlSpdFRSt;
boolean_T VSE_Inj_Flg_SNS_bWhlSpdRLSt;
boolean_T VSE_Inj_Flg_SNS_bWhlSpdRRSt;
boolean_T VSE_Inj_Flg_u16CarType;
boolean_T VSE_Inj_Flg_u8CarConfig;
boolean_T VSE_Inj_Flg_bIMU_051Flt;
boolean_T VSE_Inj_Flg_bVCU_0FCFlt;
boolean_T VSE_Inj_Flg_bVCU_342Flt;
boolean_T VSE_Inj_Flg_bVCU_12DFlt;
boolean_T VSE_Inj_Flg_bVCU_242Flt;
boolean_T VSE_Inj_Flg_bVCU_241Flt;
boolean_T VSE_Inj_Flg_bVCU_251Flt;
boolean_T VSE_Inj_Flg_bIPB_122Flt;
boolean_T VSE_Inj_Flg_bIPB_321Flt;
boolean_T VSE_Inj_Flg_bIPB_123Flt;
boolean_T VSE_Inj_Flg_bIPB_222Flt;
boolean_T VSE_Inj_Flg_bEPS_11FFlt;
boolean_T VSE_Inj_Flg_bEPB_218Flt;
boolean_T VSE_Inj_Flg_bCCU_0F4Flt;
boolean_T VSE_Inj_Flg_u8FLWhlBraSts;
boolean_T VSE_Inj_Flg_u8FRWhlBraSts;
boolean_T VSE_Inj_Flg_u8RLWhlBraSts;
boolean_T VSE_Inj_Flg_u8RRWhlBraSts;
boolean_T VSE_Inj_Flg_i16FLBrkTqExecu;
boolean_T VSE_Inj_Flg_i16FRBrkTqExecu;
boolean_T VSE_Inj_Flg_i16RLBrkTqExecu;
boolean_T VSE_Inj_Flg_i16RRBrkTqExecu;


uint8_T   RTE_bIMUAxSt1;
uint8_T   RTE_bIMUAySt1;
uint8_T   RTE_bIMUAzSt1;
uint8_T   RTE_bIMUWxSt1;
uint8_T   RTE_bIMUWySt1;
uint8_T   RTE_bIMUWzSt1;
