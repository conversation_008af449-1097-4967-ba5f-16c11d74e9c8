﻿#ifndef VSE_SIGNALINRTE_H_
#define VSE_SIGNALINRTE_H_

#include "rtwtypes.h"

// VSEAA 2.0 RTE层变量定义
extern uint16_T VSE_RTE_SIG_u16IMUAx; 
extern uint16_T VSE_RTE_SIG_u16IMUAy; 
extern uint16_T VSE_RTE_SIG_u16IMUAz; 
extern uint16_T VSE_RTE_SIG_u16IMUWx; 
extern uint16_T VSE_RTE_SIG_u16IMUWy; 
extern uint16_T VSE_RTE_SIG_u16IMUWz; 
extern boolean_T VSE_RTE_SIG_bIMUAxSt;
extern boolean_T VSE_RTE_SIG_bIMUAySt;
extern boolean_T VSE_RTE_SIG_bIMUAzSt;
extern boolean_T VSE_RTE_SIG_bIMUWxSt;
extern boolean_T VSE_RTE_SIG_bIMUWySt;
extern boolean_T VSE_RTE_SIG_bIMUWzSt;
extern int16_T VSE_RTE_SIG_i16VehTqFL;
extern int16_T VSE_RTE_SIG_i16VehTqFR;
extern int16_T VSE_RTE_SIG_i16VehTqRL;
extern int16_T VSE_RTE_SIG_i16VehTqRR;
extern boolean_T VSE_RTE_SIG_bTqStsFL;
extern boolean_T VSE_RTE_SIG_bTqStsFR;
extern boolean_T VSE_RTE_SIG_bTqStsRL;
extern boolean_T VSE_RTE_SIG_bTqStsRR;
extern uint16_T VSE_RTE_SIG_u16FrntMotTq;
extern uint16_T VSE_RTE_SIG_u16ReMotTq;
extern boolean_T VSE_RTE_SIG_bFrntMotTqSts;
extern boolean_T VSE_RTE_SIG_bReMotTqSts;
extern uint16_T VSE_RTE_SIG_u16FLWhlSpd;
extern uint16_T VSE_RTE_SIG_u16FRWhlSpd;
extern uint16_T VSE_RTE_SIG_u16RLWhlSpd;
extern uint16_T VSE_RTE_SIG_u16RRWhlSpd;
extern boolean_T VSE_RTE_SIG_bWhlSpdFLSt;
extern boolean_T VSE_RTE_SIG_bWhlSpdFRSt;
extern boolean_T VSE_RTE_SIG_bWhlSpdRLSt;
extern boolean_T VSE_RTE_SIG_bWhlSpdRRSt;
extern uint8_T VSE_RTE_SIG_u8AccrPedlRate;
extern boolean_T VSE_RTE_SIG_bAccrPedlRateFlg;
extern uint8_T VSE_RTE_SIG_u8IPBBrkSts;
extern uint8_T VSE_RTE_SIG_u8BrkDepth;
extern boolean_T VSE_RTE_SIG_bBrkDepthSts;
extern uint16_T VSE_RTE_SIG_u16IPBPPrs;
extern boolean_T VSE_RTE_SIG_bIPBPluPreSts;
extern uint8_T VSE_RTE_SIG_u8GearPosn;
extern boolean_T VSE_RTE_SIG_bGearSts;
extern int16_T VSE_RTE_SIG_i16SteerAg;
extern uint16_T VSE_RTE_SIG_u16SteerAgSpd;
extern boolean_T VSE_RTE_SIG_bSteerAgSnsSt;
extern boolean_T VSE_RTE_SIG_bSteerAgCASnsSt;
extern uint16_T VSE_RTE_SIG_u16RWhlSteerAg;
extern boolean_T VSE_RTE_SIG_bRWhlSteerAgSts;
extern boolean_T VSE_RTE_SIG_bTCSActS;
extern boolean_T VSE_RTE_SIG_bTCSFlt; 
extern boolean_T VSE_RTE_SIG_bABSActS;
extern boolean_T VSE_RTE_SIG_bABSFlt; 
extern boolean_T VSE_RTE_SIG_bVDCActS;
extern boolean_T VSE_RTE_SIG_bVDCFlt; 
extern uint8_T VSE_RTE_SIG_u8VehDrvMod;
extern uint8_T VSE_RTE_SIG_u8PwrGear; 
extern uint8_T VSE_RTE_SIG_u8EPBSt;   
extern uint16_T VSE_RTE_SIG_u16DamprPosnFL;
extern uint16_T VSE_RTE_SIG_u16DamprPosnFR;
extern uint16_T VSE_RTE_SIG_u16DamprPosnRL;
extern uint16_T VSE_RTE_SIG_u16DamprPosnRR;
extern boolean_T VSE_RTE_SIG_bDamprPosnFLSts;
extern boolean_T VSE_RTE_SIG_bDamprPosnFRSts;
extern boolean_T VSE_RTE_SIG_bDamprPosnRLSts;
extern boolean_T VSE_RTE_SIG_bDamprPosnRRSts;
extern uint16_T VSE_RTE_SIG_u16FLActualCurrent;
extern uint16_T VSE_RTE_SIG_u16FRActualCurrent;
extern uint16_T VSE_RTE_SIG_u16RLActualCurrent;
extern uint16_T VSE_RTE_SIG_u16RRActualCurrent;
extern uint8_T VSE_RTE_SIG_u8DiSusModExeSts;
extern uint8_T VSE_RTE_SIG_u8DiSus_Type;
extern boolean_T VSE_RTE_SIG_bDiSusHeiAdjSts;
extern uint8_T VSE_RTE_SIG_u8DiSusHeiAdjProc;
extern boolean_T VSE_RTE_SIG_bDiSusHeiAdjFltSts;
extern uint16_T VSE_RTE_SIG_u16CmpActIFL;
extern uint16_T VSE_RTE_SIG_u16StchActIFL;
extern uint16_T VSE_RTE_SIG_u16CmpActIFR;
extern uint16_T VSE_RTE_SIG_u16StchActIFR;
extern uint16_T VSE_RTE_SIG_u16CmpActIRL;
extern uint16_T VSE_RTE_SIG_u16StchActIRL;
extern uint16_T VSE_RTE_SIG_u16CmpActIRR;
extern uint16_T VSE_RTE_SIG_u16StchActIRR;
extern uint16_T VSE_RTE_SIG_u16EstimdFFL;
extern uint16_T VSE_RTE_SIG_u16EstimdFFR;
extern uint16_T VSE_RTE_SIG_u16EstimdFRL;
extern uint16_T VSE_RTE_SIG_u16EstimdFRR;
extern int16_T VSE_RTE_SIG_i16DamprWhlHFL;
extern int16_T VSE_RTE_SIG_i16DamprWhlHFR;
extern int16_T VSE_RTE_SIG_i16DamprWhlHRL;
extern int16_T VSE_RTE_SIG_i16DamprWhlHRR;
extern uint16_T VSE_RTE_SIG_SNS_u16FLWhlSpd;
extern uint16_T VSE_RTE_SIG_SNS_u16FRWhlSpd;
extern uint16_T VSE_RTE_SIG_SNS_u16RLWhlSpd;
extern uint16_T VSE_RTE_SIG_SNS_u16RRWhlSpd;
extern boolean_T VSE_RTE_SIG_SNS_bWhlSpdFLSt;
extern boolean_T VSE_RTE_SIG_SNS_bWhlSpdFRSt;
extern boolean_T VSE_RTE_SIG_SNS_bWhlSpdRLSt;
extern boolean_T VSE_RTE_SIG_SNS_bWhlSpdRRSt;
extern uint16_T VSE_RTE_SIG_u16CarType;
extern uint8_T VSE_RTE_SIG_u8CarConfig;
extern boolean_T VSE_RTE_SIG_bIMU_051Flt;
extern boolean_T VSE_RTE_SIG_bVCU_0FCFlt;
extern boolean_T VSE_RTE_SIG_bVCU_342Flt;
extern boolean_T VSE_RTE_SIG_bVCU_12DFlt;
extern boolean_T VSE_RTE_SIG_bVCU_242Flt;
extern boolean_T VSE_RTE_SIG_bVCU_241Flt;
extern boolean_T VSE_RTE_SIG_bVCU_251Flt;
extern boolean_T VSE_RTE_SIG_bIPB_122Flt;
extern boolean_T VSE_RTE_SIG_bIPB_321Flt;
extern boolean_T VSE_RTE_SIG_bIPB_123Flt;
extern boolean_T VSE_RTE_SIG_bIPB_222Flt;
extern boolean_T VSE_RTE_SIG_bEPS_11FFlt;
extern boolean_T VSE_RTE_SIG_bEPB_218Flt;
extern boolean_T VSE_RTE_SIG_bCCU_0F4Flt;

extern uint8_T VSE_RTE_SIG_u8FLWhlBraSts;
extern uint8_T VSE_RTE_SIG_u8FRWhlBraSts;
extern uint8_T VSE_RTE_SIG_u8RLWhlBraSts;
extern uint8_T VSE_RTE_SIG_u8RRWhlBraSts;
extern int16_T VSE_RTE_SIG_i16FLBrkTqExecu;
extern int16_T VSE_RTE_SIG_i16FRBrkTqExecu;
extern int16_T VSE_RTE_SIG_i16RLBrkTqExecu;
extern int16_T VSE_RTE_SIG_i16RRBrkTqExecu;


// Inject_Default 故障注入默认值
extern uint16_T VSE_Inj_Dft_u16IMUAx; 
extern uint16_T VSE_Inj_Dft_u16IMUAy; 
extern uint16_T VSE_Inj_Dft_u16IMUAz; 
extern uint16_T VSE_Inj_Dft_u16IMUWx; 
extern uint16_T VSE_Inj_Dft_u16IMUWy; 
extern uint16_T VSE_Inj_Dft_u16IMUWz; 
extern boolean_T VSE_Inj_Dft_bIMUAxSt;
extern boolean_T VSE_Inj_Dft_bIMUAySt;
extern boolean_T VSE_Inj_Dft_bIMUAzSt;
extern boolean_T VSE_Inj_Dft_bIMUWxSt;
extern boolean_T VSE_Inj_Dft_bIMUWySt;
extern boolean_T VSE_Inj_Dft_bIMUWzSt;
extern int16_T VSE_Inj_Dft_i16VehTqFL;
extern int16_T VSE_Inj_Dft_i16VehTqFR;
extern int16_T VSE_Inj_Dft_i16VehTqRL;
extern int16_T VSE_Inj_Dft_i16VehTqRR;
extern boolean_T VSE_Inj_Dft_bTqStsFL;
extern boolean_T VSE_Inj_Dft_bTqStsFR;
extern boolean_T VSE_Inj_Dft_bTqStsRL;
extern boolean_T VSE_Inj_Dft_bTqStsRR;
extern uint16_T VSE_Inj_Dft_u16FrntMotTq;
extern uint16_T VSE_Inj_Dft_u16ReMotTq;
extern boolean_T VSE_Inj_Dft_bFrntMotTqSts;
extern boolean_T VSE_Inj_Dft_bReMotTqSts;
extern uint16_T VSE_Inj_Dft_u16FLWhlSpd;
extern uint16_T VSE_Inj_Dft_u16FRWhlSpd;
extern uint16_T VSE_Inj_Dft_u16RLWhlSpd;
extern uint16_T VSE_Inj_Dft_u16RRWhlSpd;
extern boolean_T VSE_Inj_Dft_bWhlSpdFLSt;
extern boolean_T VSE_Inj_Dft_bWhlSpdFRSt;
extern boolean_T VSE_Inj_Dft_bWhlSpdRLSt;
extern boolean_T VSE_Inj_Dft_bWhlSpdRRSt;
extern uint8_T VSE_Inj_Dft_u8AccrPedlRate;
extern boolean_T VSE_Inj_Dft_bAccrPedlRateFlg;
extern uint8_T VSE_Inj_Dft_u8IPBBrkSts;
extern uint8_T VSE_Inj_Dft_u8BrkDepth;
extern boolean_T VSE_Inj_Dft_bBrkDepthSts;
extern uint16_T VSE_Inj_Dft_u16IPBPPrs;
extern boolean_T VSE_Inj_Dft_bIPBPluPreSts;
extern uint8_T VSE_Inj_Dft_u8GearPosn;
extern boolean_T VSE_Inj_Dft_bGearSts;
extern int16_T VSE_Inj_Dft_i16SteerAg;
extern uint16_T VSE_Inj_Dft_u16SteerAgSpd;
extern boolean_T VSE_Inj_Dft_bSteerAgSnsSt;
extern boolean_T VSE_Inj_Dft_bSteerAgCASnsSt;
extern uint16_T VSE_Inj_Dft_u16RWhlSteerAg;
extern boolean_T VSE_Inj_Dft_bRWhlSteerAgSts;
extern boolean_T VSE_Inj_Dft_bTCSActS;
extern boolean_T VSE_Inj_Dft_bTCSFlt; 
extern boolean_T VSE_Inj_Dft_bABSActS;
extern boolean_T VSE_Inj_Dft_bABSFlt; 
extern boolean_T VSE_Inj_Dft_bVDCActS;
extern boolean_T VSE_Inj_Dft_bVDCFlt; 
extern uint8_T VSE_Inj_Dft_u8VehDrvMod;
extern uint8_T VSE_Inj_Dft_u8PwrGear; 
extern uint8_T VSE_Inj_Dft_u8EPBSt;   
extern uint16_T VSE_Inj_Dft_u16DamprPosnFL;
extern uint16_T VSE_Inj_Dft_u16DamprPosnFR;
extern uint16_T VSE_Inj_Dft_u16DamprPosnRL;
extern uint16_T VSE_Inj_Dft_u16DamprPosnRR;
extern boolean_T VSE_Inj_Dft_bDamprPosnFLSts;
extern boolean_T VSE_Inj_Dft_bDamprPosnFRSts;
extern boolean_T VSE_Inj_Dft_bDamprPosnRLSts;
extern boolean_T VSE_Inj_Dft_bDamprPosnRRSts;
extern uint16_T VSE_Inj_Dft_u16FLActualCurrent;
extern uint16_T VSE_Inj_Dft_u16FRActualCurrent;
extern uint16_T VSE_Inj_Dft_u16RLActualCurrent;
extern uint16_T VSE_Inj_Dft_u16RRActualCurrent;
extern uint8_T VSE_Inj_Dft_u8DiSusModExeSts;
extern uint8_T VSE_Inj_Dft_u8DiSus_Type;
extern boolean_T VSE_Inj_Dft_bDiSusHeiAdjSts;
extern uint8_T VSE_Inj_Dft_u8DiSusHeiAdjProc;
extern boolean_T VSE_Inj_Dft_bDiSusHeiAdjFltSts;
extern uint16_T VSE_Inj_Dft_u16CmpActIFL;
extern uint16_T VSE_Inj_Dft_u16StchActIFL;
extern uint16_T VSE_Inj_Dft_u16CmpActIFR;
extern uint16_T VSE_Inj_Dft_u16StchActIFR;
extern uint16_T VSE_Inj_Dft_u16CmpActIRL;
extern uint16_T VSE_Inj_Dft_u16StchActIRL;
extern uint16_T VSE_Inj_Dft_u16CmpActIRR;
extern uint16_T VSE_Inj_Dft_u16StchActIRR;
extern uint16_T VSE_Inj_Dft_u16EstimdFFL;
extern uint16_T VSE_Inj_Dft_u16EstimdFFR;
extern uint16_T VSE_Inj_Dft_u16EstimdFRL;
extern uint16_T VSE_Inj_Dft_u16EstimdFRR;
extern int16_T VSE_Inj_Dft_i16DamprWhlHFL;
extern int16_T VSE_Inj_Dft_i16DamprWhlHFR;
extern int16_T VSE_Inj_Dft_i16DamprWhlHRL;
extern int16_T VSE_Inj_Dft_i16DamprWhlHRR;
extern uint16_T VSE_Inj_Dft_SNS_u16FLWhlSpd;
extern uint16_T VSE_Inj_Dft_SNS_u16FRWhlSpd;
extern uint16_T VSE_Inj_Dft_SNS_u16RLWhlSpd;
extern uint16_T VSE_Inj_Dft_SNS_u16RRWhlSpd;
extern boolean_T VSE_Inj_Dft_SNS_bWhlSpdFLSt;
extern boolean_T VSE_Inj_Dft_SNS_bWhlSpdFRSt;
extern boolean_T VSE_Inj_Dft_SNS_bWhlSpdRLSt;
extern boolean_T VSE_Inj_Dft_SNS_bWhlSpdRRSt;
extern uint16_T VSE_Inj_Dft_u16CarType;
extern uint8_T VSE_Inj_Dft_u8CarConfig;
extern boolean_T VSE_Inj_Dft_bIMU_051Flt;
extern boolean_T VSE_Inj_Dft_bVCU_0FCFlt;
extern boolean_T VSE_Inj_Dft_bVCU_342Flt;
extern boolean_T VSE_Inj_Dft_bVCU_12DFlt;
extern boolean_T VSE_Inj_Dft_bVCU_242Flt;
extern boolean_T VSE_Inj_Dft_bVCU_241Flt;
extern boolean_T VSE_Inj_Dft_bVCU_251Flt;
extern boolean_T VSE_Inj_Dft_bIPB_122Flt;
extern boolean_T VSE_Inj_Dft_bIPB_321Flt;
extern boolean_T VSE_Inj_Dft_bIPB_123Flt;
extern boolean_T VSE_Inj_Dft_bIPB_222Flt;
extern boolean_T VSE_Inj_Dft_bEPS_11FFlt;
extern boolean_T VSE_Inj_Dft_bEPB_218Flt;
extern boolean_T VSE_Inj_Dft_bCCU_0F4Flt;
extern uint8_T VSE_Inj_Dft_u8FLWhlBraSts;
extern uint8_T VSE_Inj_Dft_u8FRWhlBraSts;
extern uint8_T VSE_Inj_Dft_u8RLWhlBraSts;
extern uint8_T VSE_Inj_Dft_u8RRWhlBraSts;
extern int16_T VSE_Inj_Dft_i16FLBrkTqExecu;
extern int16_T VSE_Inj_Dft_i16FRBrkTqExecu;
extern int16_T VSE_Inj_Dft_i16RLBrkTqExecu;
extern int16_T VSE_Inj_Dft_i16RRBrkTqExecu;


// Inject_Flag 故障注入标志位
extern boolean_T VSE_Inj_Flg_u16IMUAx; 
extern boolean_T VSE_Inj_Flg_u16IMUAy; 
extern boolean_T VSE_Inj_Flg_u16IMUAz; 
extern boolean_T VSE_Inj_Flg_u16IMUWx; 
extern boolean_T VSE_Inj_Flg_u16IMUWy; 
extern boolean_T VSE_Inj_Flg_u16IMUWz; 
extern boolean_T VSE_Inj_Flg_bIMUAxSt;
extern boolean_T VSE_Inj_Flg_bIMUAySt;
extern boolean_T VSE_Inj_Flg_bIMUAzSt;
extern boolean_T VSE_Inj_Flg_bIMUWxSt;
extern boolean_T VSE_Inj_Flg_bIMUWySt;
extern boolean_T VSE_Inj_Flg_bIMUWzSt;
extern boolean_T VSE_Inj_Flg_i16VehTqFL;
extern boolean_T VSE_Inj_Flg_i16VehTqFR;
extern boolean_T VSE_Inj_Flg_i16VehTqRL;
extern boolean_T VSE_Inj_Flg_i16VehTqRR;
extern boolean_T VSE_Inj_Flg_bTqStsFL;
extern boolean_T VSE_Inj_Flg_bTqStsFR;
extern boolean_T VSE_Inj_Flg_bTqStsRL;
extern boolean_T VSE_Inj_Flg_bTqStsRR;
extern boolean_T VSE_Inj_Flg_u16FrntMotTq;
extern boolean_T VSE_Inj_Flg_u16ReMotTq;
extern boolean_T VSE_Inj_Flg_bFrntMotTqSts;
extern boolean_T VSE_Inj_Flg_bReMotTqSts;
extern boolean_T VSE_Inj_Flg_u16FLWhlSpd;
extern boolean_T VSE_Inj_Flg_u16FRWhlSpd;
extern boolean_T VSE_Inj_Flg_u16RLWhlSpd;
extern boolean_T VSE_Inj_Flg_u16RRWhlSpd;
extern boolean_T VSE_Inj_Flg_bWhlSpdFLSt;
extern boolean_T VSE_Inj_Flg_bWhlSpdFRSt;
extern boolean_T VSE_Inj_Flg_bWhlSpdRLSt;
extern boolean_T VSE_Inj_Flg_bWhlSpdRRSt;
extern boolean_T VSE_Inj_Flg_u8AccrPedlRate;
extern boolean_T VSE_Inj_Flg_bAccrPedlRateFlg;
extern boolean_T VSE_Inj_Flg_u8IPBBrkSts;
extern boolean_T VSE_Inj_Flg_u8BrkDepth;
extern boolean_T VSE_Inj_Flg_bBrkDepthSts;
extern boolean_T VSE_Inj_Flg_u16IPBPPrs;
extern boolean_T VSE_Inj_Flg_bIPBPluPreSts;
extern boolean_T VSE_Inj_Flg_u8GearPosn;
extern boolean_T VSE_Inj_Flg_bGearSts;
extern boolean_T VSE_Inj_Flg_i16SteerAg;
extern boolean_T VSE_Inj_Flg_u16SteerAgSpd;
extern boolean_T VSE_Inj_Flg_bSteerAgSnsSt;
extern boolean_T VSE_Inj_Flg_bSteerAgCASnsSt;
extern boolean_T VSE_Inj_Flg_u16RWhlSteerAg;
extern boolean_T VSE_Inj_Flg_bRWhlSteerAgSts;
extern boolean_T VSE_Inj_Flg_bTCSActS;
extern boolean_T VSE_Inj_Flg_bTCSFlt; 
extern boolean_T VSE_Inj_Flg_bABSActS;
extern boolean_T VSE_Inj_Flg_bABSFlt; 
extern boolean_T VSE_Inj_Flg_bVDCActS;
extern boolean_T VSE_Inj_Flg_bVDCFlt; 
extern boolean_T VSE_Inj_Flg_u8VehDrvMod;
extern boolean_T VSE_Inj_Flg_u8PwrGear; 
extern boolean_T VSE_Inj_Flg_u8EPBSt;   
extern boolean_T VSE_Inj_Flg_u16DamprPosnFL;
extern boolean_T VSE_Inj_Flg_u16DamprPosnFR;
extern boolean_T VSE_Inj_Flg_u16DamprPosnRL;
extern boolean_T VSE_Inj_Flg_u16DamprPosnRR;
extern boolean_T VSE_Inj_Flg_bDamprPosnFLSts;
extern boolean_T VSE_Inj_Flg_bDamprPosnFRSts;
extern boolean_T VSE_Inj_Flg_bDamprPosnRLSts;
extern boolean_T VSE_Inj_Flg_bDamprPosnRRSts;
extern boolean_T VSE_Inj_Flg_u16FLActualCurrent;
extern boolean_T VSE_Inj_Flg_u16FRActualCurrent;
extern boolean_T VSE_Inj_Flg_u16RLActualCurrent;
extern boolean_T VSE_Inj_Flg_u16RRActualCurrent;
extern boolean_T VSE_Inj_Flg_u8DiSusModExeSts;
extern boolean_T VSE_Inj_Flg_u8DiSus_Type;
extern boolean_T VSE_Inj_Flg_bDiSusHeiAdjSts;
extern boolean_T VSE_Inj_Flg_u8DiSusHeiAdjProc;
extern boolean_T VSE_Inj_Flg_bDiSusHeiAdjFltSts;
extern boolean_T VSE_Inj_Flg_u16CmpActIFL;
extern boolean_T VSE_Inj_Flg_u16StchActIFL;
extern boolean_T VSE_Inj_Flg_u16CmpActIFR;
extern boolean_T VSE_Inj_Flg_u16StchActIFR;
extern boolean_T VSE_Inj_Flg_u16CmpActIRL;
extern boolean_T VSE_Inj_Flg_u16StchActIRL;
extern boolean_T VSE_Inj_Flg_u16CmpActIRR;
extern boolean_T VSE_Inj_Flg_u16StchActIRR;
extern boolean_T VSE_Inj_Flg_u16EstimdFFL;
extern boolean_T VSE_Inj_Flg_u16EstimdFFR;
extern boolean_T VSE_Inj_Flg_u16EstimdFRL;
extern boolean_T VSE_Inj_Flg_u16EstimdFRR;
extern boolean_T VSE_Inj_Flg_i16DamprWhlHFL;
extern boolean_T VSE_Inj_Flg_i16DamprWhlHFR;
extern boolean_T VSE_Inj_Flg_i16DamprWhlHRL;
extern boolean_T VSE_Inj_Flg_i16DamprWhlHRR;
extern boolean_T VSE_Inj_Flg_SNS_u16FLWhlSpd;
extern boolean_T VSE_Inj_Flg_SNS_u16FRWhlSpd;
extern boolean_T VSE_Inj_Flg_SNS_u16RLWhlSpd;
extern boolean_T VSE_Inj_Flg_SNS_u16RRWhlSpd;
extern boolean_T VSE_Inj_Flg_SNS_bWhlSpdFLSt;
extern boolean_T VSE_Inj_Flg_SNS_bWhlSpdFRSt;
extern boolean_T VSE_Inj_Flg_SNS_bWhlSpdRLSt;
extern boolean_T VSE_Inj_Flg_SNS_bWhlSpdRRSt;
extern boolean_T VSE_Inj_Flg_u16CarType;
extern boolean_T VSE_Inj_Flg_u8CarConfig;
extern boolean_T VSE_Inj_Flg_bIMU_051Flt;
extern boolean_T VSE_Inj_Flg_bVCU_0FCFlt;
extern boolean_T VSE_Inj_Flg_bVCU_342Flt;
extern boolean_T VSE_Inj_Flg_bVCU_12DFlt;
extern boolean_T VSE_Inj_Flg_bVCU_242Flt;
extern boolean_T VSE_Inj_Flg_bVCU_241Flt;
extern boolean_T VSE_Inj_Flg_bVCU_251Flt;
extern boolean_T VSE_Inj_Flg_bIPB_122Flt;
extern boolean_T VSE_Inj_Flg_bIPB_321Flt;
extern boolean_T VSE_Inj_Flg_bIPB_123Flt;
extern boolean_T VSE_Inj_Flg_bIPB_222Flt;
extern boolean_T VSE_Inj_Flg_bEPS_11FFlt;
extern boolean_T VSE_Inj_Flg_bEPB_218Flt;
extern boolean_T VSE_Inj_Flg_bCCU_0F4Flt;
extern boolean_T VSE_Inj_Flg_u8FLWhlBraSts;
extern boolean_T VSE_Inj_Flg_u8FRWhlBraSts;
extern boolean_T VSE_Inj_Flg_u8RLWhlBraSts;
extern boolean_T VSE_Inj_Flg_u8RRWhlBraSts;
extern boolean_T VSE_Inj_Flg_i16FLBrkTqExecu;
extern boolean_T VSE_Inj_Flg_i16FRBrkTqExecu;
extern boolean_T VSE_Inj_Flg_i16RLBrkTqExecu;
extern boolean_T VSE_Inj_Flg_i16RRBrkTqExecu;

#endif
