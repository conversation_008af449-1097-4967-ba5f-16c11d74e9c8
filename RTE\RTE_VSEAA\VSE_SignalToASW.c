#include "VSE_SignalToASW.h"

uint16_T g_VSE_In_Com_u16IMUAx; 
uint16_T g_VSE_In_Com_u16IMUAy; 
uint16_T g_VSE_In_Com_u16IMUAz; 
uint16_T g_VSE_In_Com_u16IMUWx; 
uint16_T g_VSE_In_Com_u16IMUWy; 
uint16_T g_VSE_In_Com_u16IMUWz; 
int16_T g_VSE_In_Com_i16VehTqFL;
int16_T g_VSE_In_Com_i16VehTqFR;
int16_T g_VSE_In_Com_i16VehTqRL;
int16_T g_VSE_In_Com_i16VehTqRR;
uint16_T g_VSE_In_Com_u16FrntMotTq;
uint16_T g_VSE_In_Com_u16ReMotTq;
uint16_T g_VSE_In_Com_u16FLWhlSpd;
uint16_T g_VSE_In_Com_u16FRWhlSpd;
uint16_T g_VSE_In_Com_u16RLWhlSpd;
uint16_T g_VSE_In_Com_u16RRWhlSpd;
uint8_T g_VSE_In_Com_u8AccrPedlRate;
uint8_T g_VSE_In_Com_u8BrkDepth;
uint16_T g_VSE_In_Com_u16IPBPPrs;
uint8_T g_VSE_In_Com_u8GearPosn;
int16_T g_VSE_In_Com_i16SteerAg;
uint16_T g_VSE_In_Com_u16SteerAgSpd;
boolean_T g_VSE_In_Com_bSteerAgCASnsSt;
uint16_T g_VSE_In_Com_u16RWhlSteerAg;
boolean_T g_VSE_In_Com_bRWhlSteerAgSts;
boolean_T g_VSE_In_Com_bTCSActS;
boolean_T g_VSE_In_Com_bABSActS;
boolean_T g_VSE_In_Com_bVDCActS;
uint16_T g_VSE_In_Com_u16DamprPosnFL;
uint16_T g_VSE_In_Com_u16DamprPosnFR;
uint16_T g_VSE_In_Com_u16DamprPosnRL;
uint16_T g_VSE_In_Com_u16DamprPosnRR;
boolean_T g_VSE_In_Com_bDamprPosnFLSts;
boolean_T g_VSE_In_Com_bDamprPosnFRSts;
boolean_T g_VSE_In_Com_bDamprPosnRLSts;
boolean_T g_VSE_In_Com_bDamprPosnRRSts;
uint16_T g_VSE_In_Com_u16FLActualCurrent;
uint16_T g_VSE_In_Com_u16FRActualCurrent;
uint16_T g_VSE_In_Com_u16RLActualCurrent;
uint16_T g_VSE_In_Com_u16RRActualCurrent;
uint8_T g_VSE_In_Com_u8DiSusModExeSts;
uint8_T g_VSE_In_Com_u8DiSus_Type;
boolean_T g_VSE_In_Com_bDiSusHeiAdjSts;
uint8_T g_VSE_In_Com_u8DiSusHeiAdjProc;
boolean_T g_VSE_In_Com_bDiSusHeiAdjFltSts;
uint16_T g_VSE_In_Sns_u16CmpActIFL;
uint16_T g_VSE_In_Sns_u16StchActIFL;
uint16_T g_VSE_In_Sns_u16CmpActIFR;
uint16_T g_VSE_In_Sns_u16StchActIFR;
uint16_T g_VSE_In_Sns_u16CmpActIRL;
uint16_T g_VSE_In_Sns_u16StchActIRL;
uint16_T g_VSE_In_Sns_u16CmpActIRR;
uint16_T g_VSE_In_Sns_u16StchActIRR;
uint16_T g_VSE_In_Com_u16EstimdFFL;
uint16_T g_VSE_In_Com_u16EstimdFFR;
uint16_T g_VSE_In_Com_u16EstimdFRL;
uint16_T g_VSE_In_Com_u16EstimdFRR;
int16_T g_VSE_In_Sns_i16DamprWhlHFL;
int16_T g_VSE_In_Sns_i16DamprWhlHFR;
int16_T g_VSE_In_Sns_i16DamprWhlHRL;
int16_T g_VSE_In_Sns_i16DamprWhlHRR;
uint16_T g_VSE_In_Sns_u16FLWhlSpd;
uint16_T g_VSE_In_Sns_u16FRWhlSpd;
uint16_T g_VSE_In_Sns_u16RLWhlSpd;
uint16_T g_VSE_In_Sns_u16RRWhlSpd;
boolean_T g_VSE_In_Sns_bWhlSpdFLSt;
boolean_T g_VSE_In_Sns_bWhlSpdFRSt;
boolean_T g_VSE_In_Sns_bWhlSpdRLSt;
boolean_T g_VSE_In_Sns_bWhlSpdRRSt;
boolean_T g_VSE_In_Com_bIMUAxSt;
boolean_T g_VSE_In_Com_bIMUAySt;
boolean_T g_VSE_In_Com_bIMUAzSt;
boolean_T g_VSE_In_Com_bIMUWxSt;
boolean_T g_VSE_In_Com_bIMUWySt;
boolean_T g_VSE_In_Com_bIMUWzSt;
boolean_T g_VSE_In_Com_bTqStsFL;
boolean_T g_VSE_In_Com_bTqStsFR;
boolean_T g_VSE_In_Com_bTqStsRL;
boolean_T g_VSE_In_Com_bTqStsRR;
boolean_T g_VSE_In_Com_bAccrPedlRateFlg;
boolean_T g_VSE_In_Com_bBrkDepthSts;
uint8_T g_VSE_In_Com_u8VehDrvMod;
uint8_T g_VSE_In_Com_u8PwrGear; 
boolean_T g_VSE_In_Com_bGearSts;
boolean_T g_VSE_In_Com_bFrntMotTqSts;
boolean_T g_VSE_In_Com_bReMotTqSts;
boolean_T g_VSE_In_Com_bWhlSpdFLSt;
boolean_T g_VSE_In_Com_bWhlSpdFRSt;
boolean_T g_VSE_In_Com_bWhlSpdRLSt;
boolean_T g_VSE_In_Com_bWhlSpdRRSt;
boolean_T g_VSE_In_Com_bABSFlt; 
boolean_T g_VSE_In_Com_bIPBPluPreSts;
uint8_T g_VSE_In_Com_u8IPBBrkSts;
boolean_T g_VSE_In_Com_bVDCFlt; 
boolean_T g_VSE_In_Com_bSteerAgSnsSt;
uint8_T g_VSE_In_Com_u8EPBSt;   
boolean_T g_VSE_In_Com_bTCSFlt; 
boolean_T g_VSE_In_Mem_bIMU_051Flt;
boolean_T g_VSE_In_Mem_bVCU_0FCFlt;
boolean_T g_VSE_In_Mem_bVCU_342Flt;
boolean_T g_VSE_In_Mem_bVCU_12DFlt;
boolean_T g_VSE_In_Mem_bVCU_242Flt;
boolean_T g_VSE_In_Mem_bVCU_241Flt;
boolean_T g_VSE_In_Mem_bVCU_251Flt;
boolean_T g_VSE_In_Mem_bIPB_122Flt;
boolean_T g_VSE_In_Mem_bIPB_321Flt;
boolean_T g_VSE_In_Mem_bIPB_123Flt;
boolean_T g_VSE_In_Mem_bIPB_222Flt;
boolean_T g_VSE_In_Mem_bEPS_11FFlt;
boolean_T g_VSE_In_Mem_bEPB_218Flt;
boolean_T g_VSE_In_Mem_bCCU_0F4Flt;     
uint16_T g_VSE_In_Mem_u16CarType;
uint8_T g_VSE_In_Mem_u8CarConfig;

uint8_T g_VSE_In_Com_u8FLWhlBraSts;
uint8_T g_VSE_In_Com_u8FRWhlBraSts;
uint8_T g_VSE_In_Com_u8RLWhlBraSts;
uint8_T g_VSE_In_Com_u8RRWhlBraSts;
int16_T g_VSE_In_Com_i16FLBrkTqExecu;
int16_T g_VSE_In_Com_i16FRBrkTqExecu;
int16_T g_VSE_In_Com_i16RLBrkTqExecu;
int16_T g_VSE_In_Com_i16RRBrkTqExecu;


