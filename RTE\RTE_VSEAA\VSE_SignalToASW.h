#ifndef VSE_SIGNALTOASW_H_
#define VSE_SIGNALTOASW_H_

#include "rtwtypes.h"

extern uint16_T g_VSE_In_Com_u16IMUAx; 
extern uint16_T g_VSE_In_Com_u16IMUAy; 
extern uint16_T g_VSE_In_Com_u16IMUAz; 
extern uint16_T g_VSE_In_Com_u16IMUWx; 
extern uint16_T g_VSE_In_Com_u16IMUWy; 
extern uint16_T g_VSE_In_Com_u16IMUWz; 
extern int16_T g_VSE_In_Com_i16VehTqFL;
extern int16_T g_VSE_In_Com_i16VehTqFR;
extern int16_T g_VSE_In_Com_i16VehTqRL;
extern int16_T g_VSE_In_Com_i16VehTqRR;
extern uint16_T g_VSE_In_Com_u16FrntMotTq;
extern uint16_T g_VSE_In_Com_u16ReMotTq;
extern uint16_T g_VSE_In_Com_u16FLWhlSpd;
extern uint16_T g_VSE_In_Com_u16FRWhlSpd;
extern uint16_T g_VSE_In_Com_u16RLWhlSpd;
extern uint16_T g_VSE_In_Com_u16RRWhlSpd;
extern uint8_T g_VSE_In_Com_u8AccrPedlRate;
extern uint8_T g_VSE_In_Com_u8BrkDepth;
extern uint16_T g_VSE_In_Com_u16IPBPPrs;
extern uint8_T g_VSE_In_Com_u8GearPosn;
extern int16_T g_VSE_In_Com_i16SteerAg;
extern uint16_T g_VSE_In_Com_u16SteerAgSpd;
extern boolean_T g_VSE_In_Com_bSteerAgCASnsSt;
extern uint16_T g_VSE_In_Com_u16RWhlSteerAg;
extern boolean_T g_VSE_In_Com_bRWhlSteerAgSts;
extern boolean_T g_VSE_In_Com_bTCSActS;
extern boolean_T g_VSE_In_Com_bABSActS;
extern boolean_T g_VSE_In_Com_bVDCActS;
extern uint16_T g_VSE_In_Com_u16DamprPosnFL;
extern uint16_T g_VSE_In_Com_u16DamprPosnFR;
extern uint16_T g_VSE_In_Com_u16DamprPosnRL;
extern uint16_T g_VSE_In_Com_u16DamprPosnRR;
extern boolean_T g_VSE_In_Com_bDamprPosnFLSts;
extern boolean_T g_VSE_In_Com_bDamprPosnFRSts;
extern boolean_T g_VSE_In_Com_bDamprPosnRLSts;
extern boolean_T g_VSE_In_Com_bDamprPosnRRSts;
extern uint16_T g_VSE_In_Com_u16FLActualCurrent;
extern uint16_T g_VSE_In_Com_u16FRActualCurrent;
extern uint16_T g_VSE_In_Com_u16RLActualCurrent;
extern uint16_T g_VSE_In_Com_u16RRActualCurrent;
extern uint8_T g_VSE_In_Com_u8DiSusModExeSts;
extern uint8_T g_VSE_In_Com_u8DiSus_Type;
extern boolean_T g_VSE_In_Com_bDiSusHeiAdjSts;
extern uint8_T g_VSE_In_Com_u8DiSusHeiAdjProc;
extern boolean_T g_VSE_In_Com_bDiSusHeiAdjFltSts;
extern uint16_T g_VSE_In_Sns_u16CmpActIFL;
extern uint16_T g_VSE_In_Sns_u16StchActIFL;
extern uint16_T g_VSE_In_Sns_u16CmpActIFR;
extern uint16_T g_VSE_In_Sns_u16StchActIFR;
extern uint16_T g_VSE_In_Sns_u16CmpActIRL;
extern uint16_T g_VSE_In_Sns_u16StchActIRL;
extern uint16_T g_VSE_In_Sns_u16CmpActIRR;
extern uint16_T g_VSE_In_Sns_u16StchActIRR;
extern uint16_T g_VSE_In_Com_u16EstimdFFL;
extern uint16_T g_VSE_In_Com_u16EstimdFFR;
extern uint16_T g_VSE_In_Com_u16EstimdFRL;
extern uint16_T g_VSE_In_Com_u16EstimdFRR;
extern int16_T g_VSE_In_Sns_i16DamprWhlHFL;
extern int16_T g_VSE_In_Sns_i16DamprWhlHFR;
extern int16_T g_VSE_In_Sns_i16DamprWhlHRL;
extern int16_T g_VSE_In_Sns_i16DamprWhlHRR;
extern uint16_T g_VSE_In_Sns_u16FLWhlSpd;
extern uint16_T g_VSE_In_Sns_u16FRWhlSpd;
extern uint16_T g_VSE_In_Sns_u16RLWhlSpd;
extern uint16_T g_VSE_In_Sns_u16RRWhlSpd;
extern boolean_T g_VSE_In_Sns_bWhlSpdFLSt;
extern boolean_T g_VSE_In_Sns_bWhlSpdFRSt;
extern boolean_T g_VSE_In_Sns_bWhlSpdRLSt;
extern boolean_T g_VSE_In_Sns_bWhlSpdRRSt;
extern boolean_T g_VSE_In_Com_bIMUAxSt;
extern boolean_T g_VSE_In_Com_bIMUAySt;
extern boolean_T g_VSE_In_Com_bIMUAzSt;
extern boolean_T g_VSE_In_Com_bIMUWxSt;
extern boolean_T g_VSE_In_Com_bIMUWySt;
extern boolean_T g_VSE_In_Com_bIMUWzSt;
extern boolean_T g_VSE_In_Com_bTqStsFL;
extern boolean_T g_VSE_In_Com_bTqStsFR;
extern boolean_T g_VSE_In_Com_bTqStsRL;
extern boolean_T g_VSE_In_Com_bTqStsRR;
extern boolean_T g_VSE_In_Com_bAccrPedlRateFlg;
extern boolean_T g_VSE_In_Com_bBrkDepthSts;
extern uint8_T g_VSE_In_Com_u8VehDrvMod;
extern uint8_T g_VSE_In_Com_u8PwrGear; 
extern boolean_T g_VSE_In_Com_bGearSts;
extern boolean_T g_VSE_In_Com_bFrntMotTqSts;
extern boolean_T g_VSE_In_Com_bReMotTqSts;
extern boolean_T g_VSE_In_Com_bWhlSpdFLSt;
extern boolean_T g_VSE_In_Com_bWhlSpdFRSt;
extern boolean_T g_VSE_In_Com_bWhlSpdRLSt;
extern boolean_T g_VSE_In_Com_bWhlSpdRRSt;
extern boolean_T g_VSE_In_Com_bABSFlt; 
extern boolean_T g_VSE_In_Com_bIPBPluPreSts;
extern uint8_T g_VSE_In_Com_u8IPBBrkSts;
extern boolean_T g_VSE_In_Com_bVDCFlt; 
extern boolean_T g_VSE_In_Com_bSteerAgSnsSt;
extern uint8_T g_VSE_In_Com_u8EPBSt;   
extern boolean_T g_VSE_In_Com_bTCSFlt; 
extern boolean_T g_VSE_In_Mem_bIMU_051Flt;
extern boolean_T g_VSE_In_Mem_bVCU_0FCFlt;
extern boolean_T g_VSE_In_Mem_bVCU_342Flt;
extern boolean_T g_VSE_In_Mem_bVCU_12DFlt;
extern boolean_T g_VSE_In_Mem_bVCU_242Flt;
extern boolean_T g_VSE_In_Mem_bVCU_241Flt;
extern boolean_T g_VSE_In_Mem_bVCU_251Flt;
extern boolean_T g_VSE_In_Mem_bIPB_122Flt;
extern boolean_T g_VSE_In_Mem_bIPB_321Flt;
extern boolean_T g_VSE_In_Mem_bIPB_123Flt;
extern boolean_T g_VSE_In_Mem_bIPB_222Flt;
extern boolean_T g_VSE_In_Mem_bEPS_11FFlt;
extern boolean_T g_VSE_In_Mem_bEPB_218Flt;
extern boolean_T g_VSE_In_Mem_bCCU_0F4Flt;     
extern uint16_T g_VSE_In_Mem_u16CarType;
extern uint8_T g_VSE_In_Mem_u8CarConfig;

extern uint8_T g_VSE_In_Com_u8FLWhlBraSts;
extern uint8_T g_VSE_In_Com_u8FRWhlBraSts;
extern uint8_T g_VSE_In_Com_u8RLWhlBraSts;
extern uint8_T g_VSE_In_Com_u8RRWhlBraSts;
extern int16_T g_VSE_In_Com_i16FLBrkTqExecu;
extern int16_T g_VSE_In_Com_i16FRBrkTqExecu;
extern int16_T g_VSE_In_Com_i16RLBrkTqExecu;
extern int16_T g_VSE_In_Com_i16RRBrkTqExecu;

#endif
